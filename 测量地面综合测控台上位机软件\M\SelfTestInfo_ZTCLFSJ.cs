﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 测量收发一体机自检信息
    /// </summary>
    public partial class SelfTestInfo_ZTCLFSJ : ObservableObject
    {
        //public event PropertyChangedEventHandler? PropertyChanged;

        public static readonly byte[] FrameHead = [0x5A, 0x54,0xA6];

        public static readonly byte[] FrameTail = [0x1A, 0x5A, 0xFE];


        /// <summary>
        /// 锂电池电压K
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryVoltageK  = 0.0196078431372549m;
        /// <summary>
        /// 锂电池电压B
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryVoltageB  = 0;

        /// <summary>
        /// 锂电池电压
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryVoltage = decimal.Zero;



        /// <summary>
        /// 锂电池电量K
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryElectricityK  = 1;
        
        /// <summary>
        /// 锂电池电量B
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryElectricityB  = 0;

        /// <summary>
        /// 锂电池电量
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryElectricity = decimal.Zero;




        /// <summary>
        /// 设备工作电压K
        /// </summary>
        [ObservableProperty]
        private decimal deviceWorkVoltageK = 0.0196078431372549m;

        /// <summary>
        /// 设备工作电压B
        /// </summary>
        [ObservableProperty]
        private decimal deviceWorkVoltageB  = 0;

        /// <summary>
        /// 设备工作电压
        /// </summary>
        [ObservableProperty]
        private decimal deviceWorkVoltage = decimal.Zero;

        /// <summary>
        /// 设备工作状态_锁定
        /// <para>True:锁定 False:未锁定</para>
        /// </summary>
        [ObservableProperty]
        private bool deviceWorkState_locked = false;


        /// <summary>
        /// 设备工作状态_编码
        /// <para>True:有编码 False:未编码</para>
        /// </summary>
        [ObservableProperty] 
        private bool deviceWorkState_encoded = false;



        
        /// <summary>
        /// 设备工作状态_惯组
        /// <para>True:正常 False:异常</para>
        /// </summary>
        [ObservableProperty]
        private  bool deviceWorkState_gz = false;



        /// <summary>
        /// 设备工作状态_射频输出
        /// <para>True:工作 False:静默</para>
        /// </summary>
        [ObservableProperty]
        private bool deviceWorkState_RFOutput = false;


        /// <summary>
        /// IMU温度
        /// </summary>
        [ObservableProperty]
        private decimal iMUTemperature = decimal.Zero;


        [ObservableProperty]
        private decimal angleXK  = 1;

        [ObservableProperty]
        private decimal angleXB  = 0;

        /// <summary>
        /// 俯仰角X
        /// </summary>
        [ObservableProperty]
        private decimal angleX;




        [ObservableProperty]
        private decimal angleYK = 1;
        
        [ObservableProperty] 
        private decimal angleYB  = 0;

        /// <summary>
        /// 滚转角Y
        /// </summary>
        [ObservableProperty]
        private decimal angleY;



        [ObservableProperty]
        private decimal angleZK= 1;
        
        [ObservableProperty]
        private decimal angleZB  = 0;

        /// <summary>
        /// 偏航角Z
        /// </summary>
        [ObservableProperty]
        private decimal angleZ;




        [ObservableProperty]
        private decimal accelerationXK = 1;
        
        [ObservableProperty] 
        private decimal accelerationXB  = 0;
       
        /// <summary>
        /// 加速度计X轴数据
        /// </summary>
        [ObservableProperty]
        private decimal accelerationX;


        [ObservableProperty] 
        private decimal accelerationYK  = 1;

        [ObservableProperty]
        private decimal accelerationYB  = 0;

        /// <summary>
        /// 加速度计Y轴数据
        /// </summary>
        [ObservableProperty]
        private decimal accelerationY;

        
 


        [ObservableProperty] 
        private decimal accelerationZK  = 1;
        [ObservableProperty] 
        private decimal accelerationZB  = 0;

        /// <summary>
        /// 加速度计Z轴数据
        /// </summary>
        [ObservableProperty] 
        private decimal accelerationZ;



        /// <summary>
        /// 软件版本
        /// </summary>
        [ObservableProperty]
        private string softwareVersion = string.Empty;

    


        /// <summary>
        /// 根据测量收发一体机自检回报帧更新自检信息
        /// </summary>
        /// <param name="data">测量收发一体机自检回报帧</param>
        /// <returns>True:更新信息有效</returns>
        public bool Update(byte[] data)
        {
            if (data == null || data.Length != 33)
            {
                System.Diagnostics.Trace.Assert(data != null && data.Length == 33, "数据长度不足，无法解析帧");

                return false;
            }
            if (data[0] != FrameHead[0] || data[1] != FrameHead[1] || data[2] != FrameHead[2] )
            {
                System.Diagnostics.Trace.TraceError("帧头不匹配");
                return false;
            }
            if (data[^3] != FrameTail[0] || data[^2] != FrameTail[1] || data[^1] != FrameTail[2])
            {
                System.Diagnostics.Trace.TraceError("帧尾不匹配");
                return false;
            }

            byte checkSum = 0;
            for(int i=2;i<28;i++)
            {
                checkSum += data[i];
            }

            if(data[^4]!= checkSum)
            {
                System.Diagnostics.Trace.TraceError("校验和不匹配");
                return false;
            }

            LiBatteryVoltage = data[3] * LiBatteryVoltageK + LiBatteryVoltageB;
            LiBatteryElectricity = data[4] * LiBatteryElectricityK + LiBatteryElectricityB;
            DeviceWorkVoltage = data[5] * DeviceWorkVoltageK + DeviceWorkVoltageB;
            DeviceWorkState_locked = data[6].IsBitEqual1(3);
            DeviceWorkState_encoded = data[6].IsBitEqual1(4);
            DeviceWorkState_gz = data[6].IsBitEqual1(5);
            DeviceWorkState_RFOutput = data[6].IsBitEqual1(6);

            IMUTemperature = data[7] ;

            AngleX = ((data[8]<<16)| (data[9] << 8)| data[10] ) * AngleXK + AngleXB ;
            AngleY = ((data[11] << 16) | (data[12] << 8) | data[13]) * AngleYK + AngleYB ;
            AngleZ = ((data[14] << 16) | (data[15] << 8) | data[16]) * AngleZK + AngleZ;

            AccelerationX = ((data[17] << 16) | (data[18] << 8) | data[19]) * AccelerationXK + AccelerationXB;
            AccelerationY = ((data[20] << 16) | (data[21] << 8) | data[22]) * AccelerationYK + AccelerationYB;
            AccelerationZ = ((data[23] << 16) | (data[24] << 8) | data[25]) * AccelerationZK + AccelerationZB;
            SoftwareVersion = $"{data[26]}.{data[27]:D2}";
            return true;
        }
    }
}
