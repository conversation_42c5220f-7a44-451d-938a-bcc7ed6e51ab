﻿<UserControl x:Class="测量地面综合测控台上位机软件.Examples.ProgressButtonExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="400" d:DesignWidth="600">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="ProgressButton 控件示例" 
                   FontSize="18" 
                   FontWeight="Bold" 
                   HorizontalAlignment="Center"/>

        <!-- 基本示例 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal">
            <TextBlock Text="基本示例：" VerticalAlignment="Center" Width="100"/>
            <local:ProgressButton x:Name="BasicProgressButton"
                                     Width="150"
                                     Height="35"
                                     DefaultText="开始下载"
                                     ProgressText="{Binding DownloadedSize}"
                                     HoverText="停止下载"
                                     Progress="{Binding DownloadProgress}"
                                     IsInProgress="{Binding IsDownloading}"
                                     Click="BasicProgressButton_Click"/>
        </StackPanel>

        <!-- 自定义颜色示例 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal">
            <TextBlock Text="自定义颜色：" VerticalAlignment="Center" Width="100"/>
            <local:ProgressButton x:Name="CustomColorProgressButton"
                                     Width="150"
                                     Height="35"
                                     DefaultText="开始上传"
                                     ProgressText="{Binding UploadedSize}"
                                     HoverText="停止上传"
                                     Progress="{Binding UploadProgress}"
                                     IsInProgress="{Binding IsUploading}"
                                     ProgressBackground="#FFE6E6E6"
                                     ProgressForeground="#FF4CAF50"
                                     Background="#FFF5F5F5"
                                     BorderBrush="#FF4CAF50"
                                     Click="CustomColorProgressButton_Click"/>
        </StackPanel>

        <!-- 大尺寸示例 -->
        <StackPanel Grid.Row="6" Orientation="Horizontal">
            <TextBlock Text="大尺寸：" VerticalAlignment="Center" Width="100"/>
            <local:ProgressButton x:Name="LargeProgressButton"
                                     Width="200"
                                     Height="50"
                                     FontSize="14"
                                     FontWeight="Bold"
                                     DefaultText="开始处理"
                                     ProgressText="{Binding ProcessedSize}"
                                     HoverText="停止处理"
                                     Progress="{Binding ProcessProgress}"
                                     IsInProgress="{Binding IsProcessing}"
                                     ProgressBackground="#FFEEEEEE"
                                     ProgressForeground="#FFFF9800"
                                     Click="LargeProgressButton_Click"/>
        </StackPanel>

        <!-- 控制面板 -->
        <GroupBox Grid.Row="8" Header="控制面板" Margin="0,10,0,0">
            <StackPanel Margin="10">
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="下载进度：" Width="80"/>
                    <Slider x:Name="DownloadProgressSlider" 
                            Width="200" 
                            Minimum="0" 
                            Maximum="100" 
                            Value="{Binding DownloadProgress, Mode=TwoWay}"
                            IsEnabled="{Binding IsDownloading}"/>
                    <TextBlock Text="{Binding DownloadProgress, StringFormat={}{0:F1}%}" 
                               Margin="10,0,0,0" 
                               Width="50"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="上传进度：" Width="80"/>
                    <Slider x:Name="UploadProgressSlider" 
                            Width="200" 
                            Minimum="0" 
                            Maximum="100" 
                            Value="{Binding UploadProgress, Mode=TwoWay}"
                            IsEnabled="{Binding IsUploading}"/>
                    <TextBlock Text="{Binding UploadProgress, StringFormat={}{0:F1}%}" 
                               Margin="10,0,0,0" 
                               Width="50"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="处理进度：" Width="80"/>
                    <Slider x:Name="ProcessProgressSlider" 
                            Width="200" 
                            Minimum="0" 
                            Maximum="100" 
                            Value="{Binding ProcessProgress, Mode=TwoWay}"
                            IsEnabled="{Binding IsProcessing}"/>
                    <TextBlock Text="{Binding ProcessProgress, StringFormat={}{0:F1}%}" 
                               Margin="10,0,0,0" 
                               Width="50"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                    <Button Content="重置所有" Click="ResetAll_Click" Margin="0,0,10,0"/>
                    <Button Content="模拟进度" Click="SimulateProgress_Click"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>
    </Grid>
</UserControl>
