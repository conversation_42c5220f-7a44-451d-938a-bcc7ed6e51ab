﻿using CommunityToolkit.Mvvm.ComponentModel;
using DevExpress.XtraRichEdit.Fields;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.Media.Core;

namespace 测量地面综合测控台上位机软件
{

    public partial class Page0ViewModel:ObservableObject
    {
        [ObservableProperty]
        private decimal voltageK_5V;

        [ObservableProperty]
        private decimal voltageB_5V;

        [ObservableProperty]
        private decimal currentK_5V;

        [ObservableProperty]
        private decimal currentB_5V;


        [ObservableProperty]
        private decimal voltageK_28V;

        [ObservableProperty]
        private decimal voltageB_28V;

        [ObservableProperty]
        private decimal currentK_28V;

        [ObservableProperty]
        private decimal currentB_28V;



        public bool IsChanged =>
            VoltageK_5V != _originVoltageK5 ||
            VoltageB_5V != _originVoltageB5 ||
            CurrentK_5V != _originCurrentK5 ||
            CurrentB_5V != _originCurrentB5 ||
            VoltageK_28V != _originVoltageK28 ||
            VoltageB_28V != _originVoltageB28 ||
            CurrentK_28V != _originCurrentK28 ||
            CurrentB_28V != _originCurrentB28 
            ;

        private decimal _originVoltageK28, _originVoltageB28, _originCurrentK28, _originCurrentB28,
                        _originVoltageK5, _originVoltageB5, _originCurrentK5, _originCurrentB5;


        [ObservableProperty]
        private string saveResultText = string.Empty;

        [ObservableProperty]
        private System.Windows.Media.Brush saveResultBrush = System.Windows.Media.Brushes.Transparent;



        public Page0ViewModel(PowerInfo powerInfo)
        {
            // 初始化时保存原始值
            _originVoltageK28 = powerInfo.Voltage28K;
            _originVoltageB28 = powerInfo.Voltage28B;
            _originCurrentK28 = powerInfo.Current28K;
            _originCurrentB28 = powerInfo.Current28B;

            VoltageK_28V = _originVoltageK28;
            VoltageB_28V = _originVoltageB28;
            CurrentK_28V = _originCurrentK28;
            CurrentB_28V = _originCurrentB28;

            _originVoltageK5 = powerInfo.Voltage5K;
            _originVoltageB5 = powerInfo.Voltage5B;
            _originCurrentK5 = powerInfo.Current5K;
            _originCurrentB5 = powerInfo.Current5B;

            VoltageK_5V = _originVoltageK5;
            VoltageB_5V = _originVoltageB5;
            CurrentK_5V = _originCurrentK5;
            CurrentB_5V = _originCurrentB5;

            PropertyChanged += (_, e) =>
            {
                if (e.PropertyName.Contains("Voltage")|| e.PropertyName.Contains("Current"))
                    OnPropertyChanged(nameof(IsChanged));
            };
        }

        public void UpdatePowerInfoConfig(PowerInfo powerInfo)
        {
            powerInfo.Voltage28K = VoltageK_28V;
            powerInfo.Voltage28B = VoltageB_28V;
            powerInfo.Current28K = CurrentK_28V;
            powerInfo.Current28B = CurrentB_28V;

            // 更新原始值
            _originVoltageK28 = VoltageK_28V;
            _originVoltageB28 = VoltageB_28V;
            _originCurrentK28 = CurrentK_28V;
            _originCurrentB28 = CurrentB_28V;

            powerInfo.Voltage5K = VoltageK_5V;
            powerInfo.Voltage5B = VoltageB_5V;
            powerInfo.Current5K = CurrentK_5V;
            powerInfo.Current5B = CurrentB_5V;

            // 更新原始值
            _originVoltageK5 = VoltageK_5V;
            _originVoltageB5 = VoltageB_5V;
            _originCurrentK5 = CurrentK_5V;
            _originCurrentB5 = CurrentB_5V;
            OnPropertyChanged(nameof(IsChanged));
        }

       


    }
}
