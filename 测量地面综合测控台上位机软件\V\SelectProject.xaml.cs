﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// SelectProject.xaml 的交互逻辑
    /// </summary>
    public partial class SelectProject : Window
    {
        public SelectProject()
        {
            InitializeComponent();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            if((bool)p121.IsChecked)
            {
                Global.Is121 = true;

                Window_Main_121 mainWindow = new();
                mainWindow.Show();
                this.Close();
            }
            else
            {
                Global.Is121 = false;

                Window_Main_122 mainWindow = new();
                mainWindow.Show();
                this.Close();
            }
        }
    }
}
