﻿using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using DevExpress.Utils;
using DevExpress.Xpf.LayoutControl;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Xml.Serialization;


namespace 测量地面综合测控台上位机软件
{
    public class SerializableViewModel
    {
        protected SerializableViewModel() { }

        public object Content { get; set; }
        public string DisplayName { get; set; }
        public string Name { get; set; }

        public static SerializableViewModel Create()
        {
            return ViewModelSource.Create(() => new SerializableViewModel());
        }
        public static SerializableViewModel Create(int id)
        {
            var vm = ViewModelSource.Create(() => new SerializableViewModel());
            vm.Name = "Model" + id;
            vm.DisplayName = "Model " + id;
            vm.Content = id;
            return vm;
        }
        public static SerializableViewModel Create(SerializationInfo info)
        {
            var vm = Create();
            vm.ApplySerializationInfo(info);
            return vm;
        }
        public SerializationInfo GetSerializationInfo()
        {
            return new SerializationInfo() { Content = Content, DisplayName = DisplayName, Name = Name };
        }
        void ApplySerializationInfo(SerializationInfo info)
        {
            Content = info.Content;
            DisplayName = info.DisplayName;
            Name = info.Name;
        }

        public class SerializationInfo
        {
            public SerializationInfo() { }

            public object Content { get; set; }
            public string DisplayName { get; set; }
            public string Name { get; set; }
        }
    }
    public class MVVMSerializationViewModel : ViewModelBase
    {
        private IList<object> _Items;
        Stream layoutStream;
        Stream vmStream;

        public MVVMSerializationViewModel()
        {
            _Items = GenerateItems(8);
        }

        public MVVMSerializationViewModel(Frame[] frames)
        {
            _Items = GenerateItems(frames);
        }


        public IList<object> Items
        {
            get { return _Items; }
        }
        public virtual IDockLayoutManagerSerializationService SerializationService { get { return null; } }

        public bool CanRestore()
        {
            return vmStream != null && layoutStream != null;
        }
        public void Restore()
        {
            vmStream.Position = 0;
            layoutStream.Position = 0;
            RestoreFromStream(vmStream);
            SerializationService.Deserialize(layoutStream);
        }
        public void RestoreSample(int index)
        {
            var assembly = Assembly.GetExecutingAssembly();
            string vmName = string.Format("views{0}.xml", index + 1);
            using (Stream resourceStream = AssemblyHelper.GetEmbeddedResourceStream(assembly, AppDomain.CurrentDomain.BaseDirectory + $"配置文件\\{vmName}", true))
            {
                RestoreFromStream(resourceStream);
            }
            string name = string.Format("savedworkspace{0}.xml", index + 1);
            using (Stream resourceStream = AssemblyHelper.GetEmbeddedResourceStream(assembly, AppDomain.CurrentDomain.BaseDirectory + $"配置文件\\{name}", true))
            {
                SerializationService.Deserialize(resourceStream);
            }
        }
        public void Store()
        {
            if (vmStream == null) vmStream = new MemoryStream();
            if (layoutStream == null) layoutStream = new MemoryStream();
            vmStream.SetLength(0);
            layoutStream.SetLength(0);
            StoreToStream(vmStream);
            SerializationService.Serialize(layoutStream);
        }
        IList<object> GenerateItems(int count)
        {
            ObservableCollection<object> items = new ObservableCollection<object>();
            for (int i = 0; i < count; i++)
            {
                items.Add(SerializableViewModel.Create(i));
            }
            return items;
        }

        IList<object> GenerateItems(Frame[] frames)
        {
            ObservableCollection<object> items = new ObservableCollection<object>();

            // 选择使用哪种布局控件（可以根据需要切换）
            bool useAdaptivePanel = false; // 设置为true使用新的AdaptiveChannelPanel

            for (int i = 0; i < frames.Length; i++)
            {
                List<Channel> chs = frames[i].CurrentDeviceChannels;

                if (useAdaptivePanel)
                {
                    // 使用新的AdaptiveChannelPanel
                    var adaptivePanel = new AdaptiveChannelPanel()
                    {
                        Name = $"adaptivePanel{frames[i].FrameName}",
                        ValueBindingPath = "LastDecValue",
                        ValueFormat = "F2",
                        ItemMinWidth = 100,
                        ItemHeight = 50
                    };

                    // 设置通道列表
                    adaptivePanel.SetChannels(chs);

                    SerializableViewModel.SerializationInfo serializationInfo = new SerializableViewModel.SerializationInfo()
                    {
                        Name = frames[i].FrameName,
                        DisplayName = frames[i].FrameName,
                        Content = adaptivePanel
                    };

                    items.Add(SerializableViewModel.Create(serializationInfo));
                }
                else
                {
                    // 使用原来的AutoChannelLayoutGroup
                    var autoLayoutGroup = new AutoChannelLayoutGroup()
                    {
                        Name = $"layCtl{frames[i].FrameName}",
                        IsCollapsible = false,
                        View = LayoutGroupView.Group,
                        VerticalAlignment = System.Windows.VerticalAlignment.Stretch,
                        Orientation = System.Windows.Controls.Orientation.Vertical,
                        // 配置显示属性
                        ValueBindingPath = "LastDecValue",
                        ShowChannelDescription = true,
                        ValueFormat = "",
                        // 布局配置
                        ChannelColumns = 1,
                        RowHeight = 25,
                    };

                    // 设置通道列表
                    autoLayoutGroup.SetChannels(chs);
                    

                    SerializableViewModel.SerializationInfo serializationInfo = new SerializableViewModel.SerializationInfo()
                    {
                        Name = frames[i].FrameName,
                        DisplayName = frames[i].FrameName,
                        Content = autoLayoutGroup
                    };

                    items.Add(SerializableViewModel.Create(serializationInfo));
                }
            }

            // 注意：AdaptiveChannelPanel暂时不需要配置面板支持，因为它有自己的依赖属性

            return items;
        }




        void RestoreFromStream(Stream stream)
        {
            ViewModelSerializer serializer = ViewModelSerializer.Deserialize(stream);
            Items.Clear();
            foreach (var info in serializer.Infos)
            {
                Items.Add(SerializableViewModel.Create(info));
            }
        }
        void StoreToStream(Stream stream)
        {
            ViewModelSerializer serializer = new ViewModelSerializer();
            foreach (SerializableViewModel item in Items)
            {
                serializer.Infos.Add(item.GetSerializationInfo());
            }
            ViewModelSerializer.Serialize(stream, serializer);
        }

        [XmlRoot("ViewModels")]
        public class ViewModelSerializer
        {
            public ViewModelSerializer()
            {
                Infos = new List<SerializableViewModel.SerializationInfo>();
            }

            public List<SerializableViewModel.SerializationInfo> Infos { get; set; }
            public string Name { get; set; }

            public static ViewModelSerializer Deserialize(Stream stream)
            {
                return SafeXml.Deserialize<ViewModelSerializer>(stream, new Type[] { typeof(SerializableViewModel.SerializationInfo) });
            }
            public static void Serialize(Stream stream, ViewModelSerializer obj)
            {
                SafeXml.Serialize<ViewModelSerializer>(stream, obj, new Type[] { typeof(SerializableViewModel.SerializationInfo) });
            }
            public static void Serialize(string path, ViewModelSerializer obj)
            {
                using (Stream st = new FileStream(path, FileMode.Create))
                {
                    SafeXml.Serialize<ViewModelSerializer>(st, obj, new Type[] { typeof(SerializableViewModel.SerializationInfo) });
                }
            }
        }
    }

}
