<Window
    x:Class="测量地面综合测控台上位机软件.WindowDownload"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Width="330"
    Height="145"
    mc:Ignorable="d"
    ResizeMode="CanResize"
    Title="数据下载"
    WindowStartupLocation="CenterOwner"
    WindowStyle="ToolWindow">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" MinHeight="20" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Orientation="Horizontal">
            <RadioButton
                x:Name="rdb1"
                Margin="10,10,10,5"
                Content="任意下载" />
            <RadioButton
                x:Name="rdb2"
                Margin="10,10,10,5"
                Content="固定容量/MB"
                IsChecked="True" />
            <hc:NumericUpDown
                x:Name="readNum"
                Width="90"
                Margin="10,10,10,5"
                Value="100"
                DecimalPlaces="0"
                IsEnabled="{Binding ElementName=rdb2, Path=IsChecked}"
                Maximum="102400"
                Minimum="1" />
        </StackPanel>

        <local:ProgressButton
            x:Name="CustomColorProgressButton"
            Grid.Row="1"
            Margin="10,5,10,10"
            Background="#FFF5F5F5"
            BorderBrush="#FF4CAF50"
            Click="CustomColorProgressButton_Click"
            DefaultText="开始下载"
            HoverText="停止下载"
            IsInProgress="{Binding IsDownloading}"
            Progress="{Binding DownloadPercent}"
            ProgressBackground="#FFE6E6E6"
            ProgressForeground="#FF4CAF50"
            ProgressText="{Binding DownloadedBytesStr}" />



        <StackPanel Grid.Row="2" Orientation="Horizontal">
            <TextBlock
                x:Name="savedFilePath"
                Margin="5,0,5,3"
                Text="📂" />
            <TextBlock>
                <Hyperlink
                    x:Name="FilePathHyperlink"
                    Click="FilePathHyperlink_Click"
                    TextDecorations="Underline"
                    ToolTip="点击打开文件所在文件夹">
                    <Run Text="{Binding SaveDataFileName}" />
                </Hyperlink>
            </TextBlock>
        </StackPanel>
    </Grid>
</Window>