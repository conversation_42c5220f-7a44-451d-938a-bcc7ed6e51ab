<UserControl x:Class="测量地面综合测控台上位机软件.Examples.AdaptiveChannelPanelExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 控制面板 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" Background="LightGray">
            <TextBlock Text="值绑定路径:" VerticalAlignment="Center" Margin="5"/>
            <ComboBox x:Name="cmbValuePath" Width="120" Margin="5" SelectedIndex="0">
                <ComboBoxItem Content="LastDecValue"/>
                <ComboBoxItem Content="LastHexValue"/>
                <ComboBoxItem Content="LastVolValue"/>
                <ComboBoxItem Content="LastPhyValue"/>
            </ComboBox>
            
            <TextBlock Text="格式化:" VerticalAlignment="Center" Margin="5"/>
            <ComboBox x:Name="cmbFormat" Width="80" Margin="5" SelectedIndex="0">
                <ComboBoxItem Content="F2"/>
                <ComboBoxItem Content="F3"/>
                <ComboBoxItem Content="F0"/>
                <ComboBoxItem Content=""/>
            </ComboBox>
            
            <TextBlock Text="最小宽度:" VerticalAlignment="Center" Margin="5"/>
            <Slider x:Name="sliderMinWidth" Width="100" Margin="5" 
                    Minimum="100" Maximum="300" Value="150" 
                    TickFrequency="25" IsSnapToTickEnabled="True"/>
            <TextBlock Text="{Binding ElementName=sliderMinWidth, Path=Value, StringFormat=F0}" 
                       VerticalAlignment="Center" Width="30"/>
            
            <TextBlock Text="项高度:" VerticalAlignment="Center" Margin="5"/>
            <Slider x:Name="sliderHeight" Width="100" Margin="5" 
                    Minimum="40" Maximum="100" Value="50" 
                    TickFrequency="10" IsSnapToTickEnabled="True"/>
            <TextBlock Text="{Binding ElementName=sliderHeight, Path=Value, StringFormat=F0}" 
                       VerticalAlignment="Center" Width="30"/>
        </StackPanel>
        
        <!-- 自适应通道面板 -->
        <Border Grid.Row="1" BorderBrush="DarkGray" BorderThickness="1" Margin="10">
            <local:AdaptiveChannelPanel x:Name="adaptivePanel"
                                        ValueBindingPath="{Binding ElementName=cmbValuePath, Path=SelectedItem.Content}"
                                        ValueFormat="{Binding ElementName=cmbFormat, Path=SelectedItem.Content}"
                                        ItemMinWidth="{Binding ElementName=sliderMinWidth, Path=Value}"
                                        ItemHeight="{Binding ElementName=sliderHeight, Path=Value}"/>
        </Border>
    </Grid>
</UserControl>
