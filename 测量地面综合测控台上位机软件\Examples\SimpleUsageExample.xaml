﻿<UserControl x:Class="测量地面综合测控台上位机软件.Examples.SimpleUsageExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="500">
    
    <StackPanel Margin="20" VerticalAlignment="Center">
        <TextBlock Text="简单使用示例" 
                   FontSize="16" 
                   FontWeight="Bold" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,30"/>

        <!-- 基本用法 -->
        <local:ProgressButton x:Name="DownloadButton"
                                 Width="200"
                                 Height="40"
                                 DefaultText="开始下载"
                                 ProgressText="{Binding DownloadedData}"
                                 HoverText="停止下载"
                                 Progress="{Binding DownloadProgress}"
                                 IsInProgress="{Binding IsDownloading}"
                                 Click="DownloadButton_Click"
                                 HorizontalAlignment="Center"/>

        <!-- 状态信息 -->
        <StackPanel Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <TextBlock Text="状态："/>
            <TextBlock Text="{Binding StatusText}" 
                       FontWeight="Bold" 
                       Margin="5,0,0,0"/>
        </StackPanel>

        <!-- 自定义样式示例 -->
        <local:ProgressButton x:Name="CustomButton"
                                 Width="200"
                                 Height="40"
                                 DefaultText="开始处理"
                                 ProgressText="{Binding ProcessedData}"
                                 HoverText="停止处理"
                                 Progress="{Binding ProcessProgress}"
                                 IsInProgress="{Binding IsProcessing}"
                                 ProgressBackground="#FFE8F5E8"
                                 ProgressForeground="#FF4CAF50"
                                 Background="#FFF8F8F8"
                                 BorderBrush="#FF4CAF50"
                                 BorderThickness="2"
                                 Click="CustomButton_Click"
                                 HorizontalAlignment="Center"
                                 Margin="0,30,0,0"/>
    </StackPanel>
</UserControl>
