﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// Window_Main_122.xaml 的交互逻辑
    /// </summary>
    public partial class Window_Main_122 : Window
    {
        public Window_Main_122()
        {
            InitializeComponent();


            var vm = App.ServiceProvider.GetRequiredService<MainWindowViewModel>();
            this.DataContext = vm;

            vm.LogsUpdated += () =>
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    if (LogList.Items.Count > 0)
                    {
                        if (LogList.SelectedIndex == -1)
                        {
                            LogList.ScrollIntoView(LogList.Items[LogList.Items.Count - 1]);
                        }
                        else if (LogList.SelectedIndex == LogList.Items.Count - 2)
                        {
                            LogList.ScrollIntoView(LogList.Items[LogList.Items.Count - 1]);
                            LogList.SelectedIndex = LogList.Items.Count - 1;
                        }
                    }
                });
            };


            vm.AddLog(LogHelper.LogLevel.INFO, "选择BYD68-122");
            Global.Init();
        }

        private void LogsOccured(object? sender, Channel.LogEventArg e)
        {
            if (e.Exception == null)
            {
                if (e.Level == "信息")
                    ((MainWindowViewModel)this.DataContext).AddLog(LogHelper.LogLevel.INFO, e.Message);
                else if (e.Level == "错误")
                    ((MainWindowViewModel)this.DataContext).AddLog(LogHelper.LogLevel.ERROR, e.Message);
                else
                    ((MainWindowViewModel)this.DataContext).AddLog(LogHelper.LogLevel.DEBUG, e.Message);
            }
            else
            {
                ((MainWindowViewModel)this.DataContext).AddLog(LogHelper.LogLevel.ERROR, $"{e.Message} - 异常: {e.Exception.Message}", null, e.Exception);
            }
        }

        private void LogList_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            GridView gv = (LogList.View as GridView);
            gv.Columns[2].Width = LogList.ActualWidth - gv.Columns[0].ActualWidth - gv.Columns[1].ActualWidth - 36;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Window_FrameConfig window_FrameConfig = new()
            {
                Owner = this
            };
            window_FrameConfig.Show();
        }

        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            Window_test  window1 = new Window_test() { Owner = this };
            window1.Show();
        }

        //private void ToggleSwitch_Click(object sender, RoutedEventArgs e)
        //{
        //    bool setState;
        //    string stateStr;
        //    if (this.testswc1.IsChecked == true)
        //    {
        //        setState = true;
        //        stateStr = "28V供电";
        //    }
        //    else
        //    {
        //        setState = false;
        //        stateStr = "28V断电";
        //    }

        //    SendFrame_PowerCard cmdFrame = new(0x01, Global.FrameCounter_PowerCard++, setState);
        //    App.ServiceProvider.GetRequiredService<PowerCardViewModel>().
        //    PowerCard.SendData(cmdFrame.ToBytes(), stateStr);
        //}

        //private void ToggleSwitch_Click_1(object sender, RoutedEventArgs e)
        //{
        //    bool setState;
        //    string stateStr;
        //    if (this.testswc2.IsChecked == true)
        //    {
        //        setState = true;
        //        stateStr = "5V供电";
        //    }
        //    else
        //    {
        //        setState = false;
        //        stateStr = "5V断电";
        //    }

        //    SendFrame_PowerCard cmdFrame = new(0x05, Global.FrameCounter_PowerCard++, setState);
        //    App.ServiceProvider.GetRequiredService<PowerCardViewModel>().
        //    PowerCard.SendData(cmdFrame.ToBytes(), stateStr);
        //}
    }
}
