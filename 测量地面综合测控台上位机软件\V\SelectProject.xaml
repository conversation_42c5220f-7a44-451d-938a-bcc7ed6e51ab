﻿<Window
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
        xmlns:hc="https://handyorg.github.io/handycontrol" x:Class="测量地面综合测控台上位机软件.SelectProject"
        mc:Ignorable="d"
        Title="选择软件" 
    
    MinHeight="{Binding RelativeSource={RelativeSource Self}, Path=Height}" 
    MaxHeight="{Binding RelativeSource={RelativeSource Self}, Path=Height}" 
    MinWidth="{Binding RelativeSource={RelativeSource Self}, Path=Width}"
    MaxWidth="{Binding RelativeSource={RelativeSource Self}, Path=Width}"
    WindowStyle="ToolWindow" ResizeMode="NoResize" Height="124" Width="351" WindowStartupLocation="CenterScreen" Topmost="True">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <WrapPanel Margin="10,10,10,10">
            <TextBlock HorizontalAlignment="Left" TextWrapping="NoWrap" Text="请选择要打开的软件" VerticalAlignment="Top" />
            <RadioButton x:Name="p121" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="10,0,10,0" 
                         IsChecked="True">BYD68-121</RadioButton>
            <RadioButton x:Name="p122" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="0,0,10,0">BYD68-122</RadioButton>
        </WrapPanel>

        <Button Grid.Row="1" Content="确定" Width="200" Height="Auto" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,10,0,10" Click="Button_Click"/>
    </Grid>
</Window>
