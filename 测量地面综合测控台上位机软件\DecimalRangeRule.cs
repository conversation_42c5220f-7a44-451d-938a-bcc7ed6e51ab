﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace 测量地面综合测控台上位机软件
{
    public class DecimalRangeRule : ValidationRule
    {
        public double Min { get; set; }
        public double Max { get; set; }

        public override ValidationResult Validate(object value, CultureInfo cultureInfo)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
                return new ValidationResult(false, "不能为空");
            if (!double.TryParse(value.ToString(), out double d))
                return new ValidationResult(false, "请输入数字");
            if (d < Min || d > Max)
                return new ValidationResult(false, $"范围：{Min}~{Max}");
            return ValidationResult.ValidResult;
        }
    }

    

}
