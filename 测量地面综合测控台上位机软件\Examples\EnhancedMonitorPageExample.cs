﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using DevExpress.Xpf.LayoutControl;
using DevExpress.Xpf.WindowsUI;
using Binding = System.Windows.Data.Binding;
using Button = System.Windows.Controls.Button;
using MessageBox = System.Windows.MessageBox;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 增强版监控页面示例，展示如何使用AutoChannelLayoutGroup和配置面板
    /// </summary>
    public partial class EnhancedMonitorPageExample : NavigationPage
    {
        private List<AutoChannelLayoutGroup> _channelGroups = new();
        private ChannelDisplayConfigPanel _configPanel;

        public EnhancedMonitorPageExample()
        {
            //InitializeComponent();
            InitializeUI();
        }

        private void InitializeUI()
        {
            // 创建主布局
            var mainGrid = new Grid();
            mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(300) });

            // 左侧：监控区域
            var monitorScrollViewer = new ScrollViewer
            {
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto
            };

            var monitorLayoutControl = new LayoutControl
            {
                Name = "monitorGroup",
                AllowAvailableItemsDuringCustomization = false,
                AllowNewItemsDuringCustomization = false,
                Background = System.Windows.Media.Brushes.White,
                IsCustomization = false,
                Orientation = System.Windows.Controls.Orientation.Horizontal
            };

            monitorScrollViewer.Content = monitorLayoutControl;
            Grid.SetColumn(monitorScrollViewer, 0);
            mainGrid.Children.Add(monitorScrollViewer);

            // 右侧：配置面板
            var configContainer = new StackPanel
            {
                Margin = new Thickness(10)
            };

            // 添加标题
            var titleTextBlock = new TextBlock
            {
                Text = "显示配置",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            configContainer.Children.Add(titleTextBlock);

            // 添加配置面板
            _configPanel = new ChannelDisplayConfigPanel();
            _configPanel.ConfigApplied += OnConfigApplied;
            configContainer.Children.Add(_configPanel);

            // 添加全局操作按钮
            var buttonPanel = new StackPanel
            {
                Margin = new Thickness(0, 20, 0, 0)
            };

            var refreshButton = new Button
            {
                Content = "刷新所有",
                Height = 30,
                Margin = new Thickness(0, 5, 0, 0),
                Background = System.Windows.Media.Brushes.LightBlue
            };
            refreshButton.Click += OnRefreshAll;
            buttonPanel.Children.Add(refreshButton);

            var resetButton = new Button
            {
                Content = "重置配置",
                Height = 30,
                Margin = new Thickness(0, 5, 0, 0),
                Background = System.Windows.Media.Brushes.LightCoral
            };
            resetButton.Click += OnResetConfig;
            buttonPanel.Children.Add(resetButton);

            configContainer.Children.Add(buttonPanel);

            Grid.SetColumn(configContainer, 1);
            mainGrid.Children.Add(configContainer);

            // 设置主内容
            Content = mainGrid;

            // 初始化监控组
            InitializeMonitorGroups(monitorLayoutControl);
        }

        /// <summary>
        /// 初始化监控组
        /// </summary>
        private void InitializeMonitorGroups(LayoutControl parentContainer)
        {
            // 这里应该从实际的数据源获取Frame数组
            // 为了示例，我们创建一些模拟数据
            var frames = GetSampleFrames();

            foreach (var frame in frames)
            {
                var autoLayoutGroup = new AutoChannelLayoutGroup
                {
                    Name = $"layCtl{frame.FrameName}",
                    IsCollapsible = true,
                    View = LayoutGroupView.GroupBox,
                    VerticalAlignment = VerticalAlignment.Stretch,
                    // 默认配置
                    ValueBindingPath = "LastDecValue",
                    ShowChannelDescription = true,
                    ValueFormat = "F2",
                    ChannelColumns=1,
                    RowHeight = 25
                };

                // 绑定Header
                var binding = new Binding("FrameName")
                {
                    Source = frame
                };
                autoLayoutGroup.SetBinding(LayoutGroup.HeaderProperty, binding);

                // 设置通道列表
                autoLayoutGroup.SetChannels(frame.CurrentDeviceChannels);

                // 添加到容器和跟踪列表
                parentContainer.Children.Add(autoLayoutGroup);
                _channelGroups.Add(autoLayoutGroup);
            }
        }

        /// <summary>
        /// 配置应用事件处理
        /// </summary>
        private void OnConfigApplied(object sender, ChannelDisplayConfigEventArgs e)
        {
            // 将配置应用到所有通道组
            foreach (var group in _channelGroups)
            {
                e.Config.ApplyTo(group);
            }

            MessageBox.Show("配置已应用到所有通道组", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新所有按钮点击事件
        /// </summary>
        private void OnRefreshAll(object sender, RoutedEventArgs e)
        {
            foreach (var group in _channelGroups)
            {
                group.RefreshLayout();
            }

            MessageBox.Show("所有通道组已刷新", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 重置配置按钮点击事件
        /// </summary>
        private void OnResetConfig(object sender, RoutedEventArgs e)
        {
            var defaultConfig = new ChannelDisplayConfig();
            _configPanel.SetConfig(defaultConfig);

            // 应用默认配置
            foreach (var group in _channelGroups)
            {
                defaultConfig.ApplyTo(group);
            }

            MessageBox.Show("配置已重置为默认值", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 获取示例Frame数据（实际使用时应该从真实数据源获取）
        /// </summary>
        private Frame[] GetSampleFrames()
        {
            // 这里应该返回实际的Frame数组
            // 为了编译通过，返回空数组
            //return new Frame[0];
            
             //实际使用时的代码示例：
            
            return new Frame[]{
                Global.Frame_CCQMNL,
                Global.Frame_CCQState,
                Global.Frame_FSJ,
                Global.Frame_GZ,
                Global.Frame_QSSLB,
                Global.Frame_SSKZXTZT,
                Global.Frame_TY,
                Global.Frame_XTZT
            };



            if (this.DataContext is MonitorViewModel vm)
            {
                return new Frame[] 
                { 
                    vm.Frame_CCQMNL, 
                    vm.Frame_CCQState, 
                    vm.Frame_FSJ, 
                    vm.Frame_GZ, 
                    vm.Frame_QSSLB, 
                    vm.Frame_SSKZXTZT, 
                    vm.Frame_TY, 
                    vm.Frame_XTZT 
                };
            }
            return new Frame[0];
            
        }

        /// <summary>
        /// 添加新的通道组
        /// </summary>
        public void AddChannelGroup(string groupName, IEnumerable<Channel> channels, string bindingPath = "LastDecValue")
        {
            var autoLayoutGroup = new AutoChannelLayoutGroup
            {
                Header = groupName,
                ValueBindingPath = bindingPath,
                ShowChannelDescription = true,
                ValueFormat = "F2"
            };

            autoLayoutGroup.SetChannels(channels);

            // 获取当前配置并应用
            var currentConfig = _configPanel.GetCurrentConfig();
            currentConfig.ApplyTo(autoLayoutGroup);

            // 添加到容器和跟踪列表
            var monitorGroup = FindName("monitorGroup") as LayoutControl;
            monitorGroup?.Children.Add(autoLayoutGroup);
            _channelGroups.Add(autoLayoutGroup);
        }

        /// <summary>
        /// 移除通道组
        /// </summary>
        public void RemoveChannelGroup(string groupName)
        {
            var groupToRemove = _channelGroups.FirstOrDefault(g => g.Header?.ToString() == groupName);
            if (groupToRemove != null)
            {
                var monitorGroup = FindName("monitorGroup") as LayoutControl;
                monitorGroup?.Children.Remove(groupToRemove);
                _channelGroups.Remove(groupToRemove);
            }
        }

        /// <summary>
        /// 获取指定组的配置
        /// </summary>
        public ChannelDisplayConfig GetGroupConfig(string groupName)
        {
            var group = _channelGroups.FirstOrDefault(g => g.Header?.ToString() == groupName);
            return group != null ? ChannelDisplayConfig.FromLayoutGroup(group) : new ChannelDisplayConfig();
        }

        /// <summary>
        /// 设置指定组的配置
        /// </summary>
        public void SetGroupConfig(string groupName, ChannelDisplayConfig config)
        {
            var group = _channelGroups.FirstOrDefault(g => g.Header?.ToString() == groupName);
            if (group != null)
            {
                config.ApplyTo(group);
            }
        }
    }
}
