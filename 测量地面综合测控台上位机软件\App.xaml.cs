﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Configuration;
using System.Data;
using System.Threading;
using System.Windows;


namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : System.Windows.Application
    {
        System.Threading.Mutex mutex;

        public static LogHelper Log = new();

        

        public static IServiceProvider ServiceProvider { get; private set; }



        private void Application_Startup(object sender, StartupEventArgs e)
        {

            #region 防止多开
            mutex = new System.Threading.Mutex(true, "H25JA01-10地面综合测控台上位机软件", out bool ret);
            if (!ret)
            {
                System.Windows.MessageBox.Show("软件已经在运行，请勿重复打开", "提示", MessageBoxButton.OK, MessageBoxImage.Exclamation);
                Environment.Exit(0);
            }
            #endregion

            #region 日志配置
            Log.MaxFileSize = 1024 * 1024;
            Log.UseThread = true;
            Log.StartThread();
            #endregion



            var services =new ServiceCollection();
            ConfigureServices(services);
            ServiceProvider = services.BuildServiceProvider();

            ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, "软件开启");
        }


        private void ConfigureServices(IServiceCollection services)
        {
            // 注册PowerInfo
            services.AddSingleton<PowerInfo>();

            // 注册ViewModel（自动注入PowerInfo）
            services.AddSingleton<PowerCardViewModel>();

            // 注册ViewModel
            services.AddSingleton<MainWindowViewModel>();

            services.AddSingleton<MonitorViewModel>();

        }



        private void Application_Exit(object sender, ExitEventArgs e)
        {
            Log.Info("软件关闭");
        }

        private void Application_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            Log.Error("发生未处理的异常");
            Log.ErrorException(e.Exception);
            MessageBox.Show(e.Exception.Message);
        }
    }

}
