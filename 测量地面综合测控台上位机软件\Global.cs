﻿using CommunityToolkit.Mvvm.ComponentModel;
using DevExpress.XtraRichEdit.Fields;
using DevExpress.XtraRichEdit.Import.Html;
using IniParser;
using IniParser.Model;
using IniParser.Parser;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Xml;

namespace 测量地面综合测控台上位机软件
{
    public static class Global
    {

        public static readonly object PciBusLock = new();

        /// <summary>
        /// 是否为BYD68-121
        /// </summary>
        public static bool Is121 { get; set; } = true;

        /// <summary>
        /// 电源卡帧计数
        /// </summary>
        public static byte FrameCounter_PowerCard = 0x00;

        /// <summary>
        /// 数字量卡帧计数
        /// </summary>
        public static byte FrameCounter_DigitalCard = 0x00;

        /// <summary>
        /// LVDS卡帧计数
        /// </summary>
        public static byte FrameCounter_LVDSCard = 0x00;

        #region 板卡地址配置信息
        /// <summary>
        /// 电源卡地址
        /// </summary>
        public static uint CardAddress_PowerCard;

        /// <summary>
        /// 数字量卡地址
        /// </summary>
        public static uint CardAddress_DigitalCard;

        /// <summary>
        /// LVDS卡地址
        /// </summary>
        public static uint CardAddress_LVDSCard;

        /// <summary>
        /// 信源卡地址
        /// </summary>
        public static uint CardAddress_SignalCard;
        #endregion


        /// <summary>
        /// 数字量卡通道模拟参数
        /// </summary>
        public struct SimulatePara
        {
            public byte Cycle;
            public uint BaudRate;
            public byte Parity;
            public ushort DataLen;
        }

        public static SimulatePara[] DigitalCardSimulatePara = new SimulatePara[16];





        #region 板卡

        /// <summary>
        /// 电源卡
        /// </summary>
        public static PCICard Card_Power { get; private set; }

        /// <summary>
        /// 数字量卡
        /// </summary>
        public static DigitalCard Card_Digital { get; private set; }

        /// <summary>
        /// LVDS卡
        /// </summary>
        public static LVDSCard? Card_LVDS { get; private set; }

        /// <summary>
        /// 信源卡
        /// </summary>
        public static PCICard? Card_Signal { get; private set; }

        #endregion

        #region 帧结构

        /// <summary>
        /// 存储器模拟量
        /// </summary>
        public static Frame? Frame_CCQMNL;

        /// <summary>
        /// 存储器状态
        /// </summary>
        public static Frame? Frame_CCQState;

        /// <summary>
        /// 存储器状态
        /// </summary>
        public static Frame? Frame_CCQState422;

        /// <summary>
        /// 发射机
        /// </summary>
        public static Frame? Frame_FSJ;

        /// <summary>
        /// 惯组
        /// </summary>
        public static Frame? Frame_GZ;

        /// <summary>
        /// 七室水冷泵
        /// </summary>
        public static Frame? Frame_QSSLB;

        /// <summary>
        /// 三室控制系统状态
        /// </summary>
        public static Frame? Frame_SSKZXTZT;



        /// <summary>
        /// 系统状态
        /// </summary>
        public static Frame? Frame_XTZT;

        public static Frame? Frame_121WX;

        public static Frame? Frame_122WX;



        /// <summary>
        /// 体遥
        /// </summary>
        public static Frame? Frame_TY;
        #endregion

        #region 数据解析
        public static DigitalCardFrameAnalyzer DigitalFrameAnalyzer;
        public static PackageAnalyzer LVDSPackageAnalyzer;
        public static FrameAnalyzer DataAnalyzer_CCQMNL;
        public static FrameAnalyzer DataAnalyzer_CCQState;
        public static FrameAnalyzer DataAnalyzer_CCQState422;
        public static FrameAnalyzer DataAnalyzer_FSJ;
        public static FrameAnalyzer DataAnalyzer_GZ;
        public static FrameAnalyzer DataAnalyzer_QSSLB;
        public static FrameAnalyzer DataAnalyzer_SSKZXTZT;
        public static FrameAnalyzer DataAnalyzer_TY;
        public static FrameAnalyzer DataAnalyzer_XTZT;

        public static FrameAnalyzer DataAnalyzer_121WX;
        public static FrameAnalyzer DataAnalyzer_122WX;

        #endregion


        #region 模拟数据
        static DataSimulator Simulator1;
        static DataSimulator Simulator2;
        static DataSimulator Simulator3;
        static DataSimulator Simulator4;
        static DataSimulator Simulator5;
        static DataSimulator Simulator6;
        static DataSimulator Simulator7;
        static DataSimulator Simulator8;
        #endregion



        public static bool Init()
        {
            bool result = false;
            result = ReadConfigFile();
            result &= ReadFrameStructure();
            result &= InitCard();
            InitAnalyser();

            if (Is121)
            {
                StartAnalyse422();
//#if DEBUG
//                InitSimulator();
//#endif
            }


            return result;
        }

        /// <summary>
        /// 从ini配置文件读取配置
        /// </summary>
        /// <returns></returns>
        private static bool ReadConfigFile()
        {
            try
            {
                var parser = new FileIniDataParser();
                IniData data = parser.ReadFile(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\配置文件.ini", System.Text.Encoding.UTF8);

                CardAddress_PowerCard = Convert.ToUInt32(data["板卡地址"]["电源卡"], 16);
                CardAddress_DigitalCard = Convert.ToUInt32(data["板卡地址"]["数字量卡"], 16);
                CardAddress_LVDSCard = Convert.ToUInt32(data["板卡地址"]["LVDS卡"], 16);
                CardAddress_SignalCard = Convert.ToUInt32(data["板卡地址"]["信源卡"], 16);


                PowerInfo pi = App.ServiceProvider.GetRequiredService<PowerInfo>();

                pi.Voltage28K = Convert.ToDecimal(data["电源卡配置"]["28V电压K"]);
                pi.Voltage28B = Convert.ToDecimal(data["电源卡配置"]["28V电压B"]);
                pi.Current28K = Convert.ToDecimal(data["电源卡配置"]["28V电流K"]);
                pi.Current28B = Convert.ToDecimal(data["电源卡配置"]["28V电流B"]);

                pi.Voltage5K = Convert.ToDecimal(data["电源卡配置"]["5V电压K"]);
                pi.Voltage5B = Convert.ToDecimal(data["电源卡配置"]["5V电压B"]);
                pi.Current5K = Convert.ToDecimal(data["电源卡配置"]["5V电流K"]);
                pi.Current5B = Convert.ToDecimal(data["电源卡配置"]["5V电流B"]);

                for (int i = 0; i < 16; i++)
                {
                    DigitalCardSimulatePara[i] = new SimulatePara();
                    DigitalCardSimulatePara[i].Cycle = Convert.ToByte(data[$"数字量卡通道{i + 1}模拟配置"]["周期"]);
                    DigitalCardSimulatePara[i].Parity = data[$"数字量卡通道{i + 1}模拟配置"]["校验"] switch
                    {
                        "无校验" => 0x00,
                        "奇校验" => 0x01,
                        "偶校验" => 0x02,
                        _ => 0x00
                    };
                    DigitalCardSimulatePara[i].BaudRate = Convert.ToUInt32(data[$"数字量卡通道{i + 1}模拟配置"]["波特率"]);
                    DigitalCardSimulatePara[i].DataLen = Convert.ToUInt16(data[$"数字量卡通道{i + 1}模拟配置"]["帧长"]);
                }



                return true;
            }
            catch (Exception ex)
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"读取配置失败:{ex.Message}", null, ex);
                return false;
            }

        }

        /// <summary>
        /// 保存电源卡配置(KB系数)到ini配置文件
        /// </summary>
        /// <returns></returns>
        public static bool SavePowerCardConfig()
        {
            try
            {
                var parser = new FileIniDataParser();
                IniData data = parser.ReadFile(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\配置文件.ini", System.Text.Encoding.UTF8);

                PowerInfo pi = App.ServiceProvider.GetRequiredService<PowerInfo>();

                data["电源卡配置"]["28V电压K"] = pi.Voltage28K.ToString();
                data["电源卡配置"]["28V电压B"] = pi.Voltage28B.ToString();
                data["电源卡配置"]["28V电流K"] = pi.Current28K.ToString();
                data["电源卡配置"]["28V电流B"] = pi.Current28B.ToString();

                data["电源卡配置"]["5V电压K"] = pi.Voltage5K.ToString();
                data["电源卡配置"]["5V电压B"] = pi.Voltage5B.ToString();
                data["电源卡配置"]["5V电流K"] = pi.Current5K.ToString();
                data["电源卡配置"]["5V电流B"] = pi.Current5B.ToString();

                parser.WriteFile(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\配置文件.ini", data, System.Text.Encoding.UTF8);

                return true;
            }
            catch (Exception ex)
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"保存电源卡配置失败:{ex.Message}", null, ex);
                return false;
            }
        }

        /// <summary>
        /// 从XML文件读取帧结构
        /// </summary>
        /// <returns></returns>
        private static bool ReadFrameStructure()
        {
            if (Is121)
            {
                Frame_CCQMNL = new();
                Frame_CCQState = new();
                Frame_CCQState422 = new();
                Frame_FSJ = new();
                Frame_GZ = new();
                Frame_QSSLB = new();
                Frame_SSKZXTZT = new();
                Frame_TY = new();
                Frame_XTZT = new();
                Frame_121WX = new();
                Frame_122WX = new();

                Frame_CCQMNL.LogOccurred += LogsOccured;
                Frame_CCQState.LogOccurred += LogsOccured;
                Frame_CCQState422.LogOccurred += LogsOccured;
                Frame_FSJ.LogOccurred += LogsOccured;
                Frame_GZ.LogOccurred += LogsOccured;
                Frame_QSSLB.LogOccurred += LogsOccured;
                Frame_SSKZXTZT.LogOccurred += LogsOccured;
                Frame_TY.LogOccurred += LogsOccured;
                Frame_XTZT.LogOccurred += LogsOccured;
                Frame_121WX.LogOccurred += LogsOccured;
                Frame_122WX.LogOccurred += LogsOccured;

                bool ret = Frame_CCQMNL.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\存储器模拟量.xml");
                ret &= Frame_CCQState.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\存储器状态.xml");
                Frame_CCQState.FrameName = "存储器状态(LVDS)";
                ret &= Frame_CCQState422.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\存储器状态.xml");
                Frame_CCQState422.FrameName = "存储器状态(422)";
                ret &= Frame_FSJ.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\发射机.xml");
                ret &= Frame_GZ.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\惯组.xml");
                ret &= Frame_QSSLB.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\七室水冷泵.xml");
                ret &= Frame_SSKZXTZT.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\三室控制系统状态.xml");
                ret &= Frame_TY.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\体遥.xml");
                ret &= Frame_XTZT.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\系统状态.xml");
                ret &= Frame_121WX.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\121无线.xml");
                ret &= Frame_122WX.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\122无线.xml");

                foreach (var a in Frame_FSJ.Devices)
                {
                    Channel ch = a.Channels.Find(o => o.ChannelName == "PCM码率");
                    if (ch != null)
                        ch.Function_ConvertToDecimal = ConvertPCMBitrate;
                }


                return ret;
            }

            return true;
        }

        /// <summary>
        /// 帧：发射机  通道：PCM码率 数字量转电压量
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private static decimal ConvertPCMBitrate(byte[] data)
        {
            decimal ret = 0;

            ret = ((data[0] >> 4) + (data[0] & 0x0F) * 0.1m + (data[1] >> 4) * 0.01m + (data[1] & 0x0F) * 0.001m);

            return ret;
        }


        private static bool InitCard()
        {
            Card_Power = new("电源卡", CardAddress_PowerCard);
            Card_Power.ErrorOccurred += Card_ErrorOccurred;
            Card_Power.InfoOccurred += Card_InfoOccurred;
            Card_Power.WarningOccurred += Card_WarnOccurred;
            bool res = Card_Power.Init();

            Card_Digital = new("数字量卡", CardAddress_DigitalCard);
            Card_Digital.ErrorOccurred += Card_ErrorOccurred;
            Card_Digital.InfoOccurred += Card_InfoOccurred;
            Card_Digital.WarningOccurred += Card_WarnOccurred;
            res &= Card_Digital.Init();


            Card_LVDS = new("LVDS卡", CardAddress_LVDSCard);
            Card_LVDS.ErrorOccurred += Card_ErrorOccurred;
            Card_LVDS.InfoOccurred += Card_InfoOccurred;
            Card_LVDS.WarningOccurred += Card_WarnOccurred;
            res &= Card_LVDS.Init();

            //if (LVDSCard.Online)
            //    ConfigLVDSCard();

            if (Is121)
            {
                Card_Signal = new("信源卡", CardAddress_SignalCard);
                Card_Signal.ErrorOccurred += Card_ErrorOccurred;
                Card_Signal.InfoOccurred += Card_InfoOccurred;
                Card_Signal.WarningOccurred += Card_WarnOccurred;
                res &= Card_Signal.Init();

            }

            return res;
        }

        private static void Card_ErrorOccurred(object? sender, string e)
        {
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"{e}");
        }
        private static void Card_InfoOccurred(object? sender, string e)
        {
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, $"{e}");
        }

        private static void Card_WarnOccurred(object? sender, string e)
        {
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.WARN, $"{e}");
        }


        private static void LogsOccured(object? sender, Channel.LogEventArg e)
        {
            if (e.Exception == null)
            {
                if (e.Level == "信息")
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, e.Message);
                else if (e.Level == "错误")
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, e.Message);
                else
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.DEBUG, e.Message);
            }
            else
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"{e.Message} - 异常: {e.Exception.Message}", null, e.Exception);
            }
        }

        /// <summary>
        /// 配置LVDS板卡  板卡初始化后调用一次即可
        /// </summary>
        public static void ConfigLVDSCard()
        {
            string filePath = AppDomain.CurrentDomain.BaseDirectory + "配置文件\\LVDS板卡配置数据.txt";
            if (File.Exists(filePath))
            {
                string raw = File.ReadAllText(filePath).Trim();
                string[] byteStrs;

                // 判断是否有分隔符
                if (raw.Contains(',') || raw.Contains('，') || raw.Contains(' '))
                {
                    byteStrs = [.. raw
                        .Split([',', '，', ' ', '\r', '\n'], StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim())
                        .Where(s => !string.IsNullOrWhiteSpace(s))];
                }
                else
                {
                    // 无分隔符，每两个字符为一个字节
                    if (raw.Length != 256 * 2)
                    {
                        App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"LVDS板卡配置数据长度不正确: 期望{256 * 2}字符，实际{raw.Length}字符");
                        return;
                    }
                    byteStrs = new string[256];
                    for (int i = 0; i < 256; i++)
                    {
                        byteStrs[i] = raw.Substring(i * 2, 2);
                    }
                }

                if (byteStrs.Length != 256)
                {
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"LVDS板卡配置数据不完整");
                    return;
                }

                byte[] data = new byte[256];
                for (int i = 0; i < 256; i++)
                {
                    if (!byte.TryParse(byteStrs[i], System.Globalization.NumberStyles.HexNumber, null, out data[i]))
                    {
                        App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"LVDS板卡配置数据格式错误: 位置 {i}, 值 \"{byteStrs[i]}\" 不是有效的十六进制字节");
                        return;
                    }
                }



                int ret = Card_LVDS?.SendData(data, "配置LVDS板卡") ?? -1;

                if (ret != data.Length)
                {
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, $"LVDS板卡配置失败:{Card_LVDS?.ErrorString}");

                    return;
                }
                else
                {
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, "LVDS板卡配置成功");
                }
            }
            else
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, "LVDS板卡配置数据.txt不存在");
            }
        }


        private static void InitSimulator()
        {
            Simulator1 = new DataSimulator(Frame_CCQMNL);
            Simulator2 = new DataSimulator(Frame_CCQState);
            Simulator3 = new DataSimulator(Frame_FSJ);
            Simulator4 = new DataSimulator(Frame_GZ);
            Simulator5 = new DataSimulator(Frame_QSSLB);
            Simulator6 = new DataSimulator(Frame_SSKZXTZT);
            Simulator7 = new DataSimulator(Frame_TY);
            Simulator8 = new DataSimulator(Frame_XTZT);

            Simulator1.Init();
            Simulator2.Init();
            Simulator3.Init();
            Simulator4.Init();
            Simulator5.Init();
            Simulator6.Init();
            Simulator7.Init();
            Simulator8.Init();

            Simulator1.OnFrameGenerated += DataAnalyzer_CCQMNL.AddRawData;
            Simulator2.OnFrameGenerated += DataAnalyzer_CCQState.AddRawData;
            Simulator3.OnFrameGenerated += DataAnalyzer_FSJ.AddRawData;
            Simulator4.OnFrameGenerated += DataAnalyzer_GZ.AddRawData;
            Simulator5.OnFrameGenerated += DataAnalyzer_QSSLB.AddRawData;
            Simulator6.OnFrameGenerated += DataAnalyzer_SSKZXTZT.AddRawData;
            Simulator7.OnFrameGenerated += DataAnalyzer_TY.AddRawData;
            Simulator8.OnFrameGenerated += DataAnalyzer_XTZT.AddRawData;


        }

        private static void InitAnalyser()
        {
            //Trace.WriteLine("存储器模拟量帧的地址"+ Function.getMemory(Frame_CCQMNL));
            DigitalFrameAnalyzer = new DigitalCardFrameAnalyzer();
            LVDSPackageAnalyzer = new PackageAnalyzer();
            DataAnalyzer_CCQMNL = new FrameAnalyzer(Frame_CCQMNL) { AnalyseChannel = true };
            DataAnalyzer_CCQState = new FrameAnalyzer(Frame_CCQState) { AnalyseChannel = true };
            DataAnalyzer_CCQState422 = new FrameAnalyzer(Frame_CCQState422) { AnalyseChannel = true };
            DataAnalyzer_FSJ = new FrameAnalyzer(Frame_FSJ) { AnalyseChannel = true };
            DataAnalyzer_GZ = new FrameAnalyzer(Frame_GZ) { AnalyseChannel = true };
            DataAnalyzer_QSSLB = new FrameAnalyzer(Frame_QSSLB) { AnalyseChannel = true };
            DataAnalyzer_SSKZXTZT = new FrameAnalyzer(Frame_SSKZXTZT) { AnalyseChannel = true };
            DataAnalyzer_TY = new FrameAnalyzer(Frame_TY) { AnalyseChannel = true };
            DataAnalyzer_XTZT = new FrameAnalyzer(Frame_XTZT) { AnalyseChannel = true };
            DataAnalyzer_121WX = new FrameAnalyzer(Frame_121WX) { AnalyseChannel = true };
            DataAnalyzer_122WX = new FrameAnalyzer(Frame_122WX) { AnalyseChannel = true };

            //默认只处理存储器状态数据  开始实时监测后再处理其它数据
            DigitalFrameAnalyzer.OnCCQStateReceived+= (sender, data) => DataAnalyzer_CCQState422.AddRawData(data);


            LVDSPackageAnalyzer.OnCCQMNLReceived += (sender, data) => DataAnalyzer_CCQMNL.AddRawData(data);
            LVDSPackageAnalyzer.OnCCQStateReceived += (sender, data) => DataAnalyzer_CCQState.AddRawData(data);
            LVDSPackageAnalyzer.OnFSJReceived += (sender, data) => DataAnalyzer_FSJ.AddRawData(data);
            LVDSPackageAnalyzer.OnGZReceived += (sender, data) => DataAnalyzer_GZ.AddRawData(data);
            LVDSPackageAnalyzer.OnQSSLBReceived += (sender, data) => DataAnalyzer_QSSLB.AddRawData(data);
            LVDSPackageAnalyzer.OnSSKZXTZTReceived += (sender, data) => DataAnalyzer_SSKZXTZT.AddRawData(data);

            LVDSPackageAnalyzer.OnXTZTReceived += (sender, data) => DataAnalyzer_XTZT.AddRawData(data);
            LVDSPackageAnalyzer.OnWXReceived += (sender, data) => DataAnalyzer_121WX.AddRawData(data);
        }



        public static void StartSimulate()
        {
            Simulator1.StartSimulator();
            Simulator2.StartSimulator();
            Simulator3.StartSimulator();
            Simulator4.StartSimulator();
            Simulator5.StartSimulator();
            Simulator6.StartSimulator();
            Simulator7.StartSimulator();
            Simulator8.StartSimulator();
        }

        public static void StartMonitorLVDS()
        {
            Frame_CCQMNL.ClearSamples();
            Frame_CCQState.ClearSamples();
            Frame_FSJ.ClearSamples();
            Frame_GZ.ClearSamples();
            Frame_QSSLB.ClearSamples();
            Frame_SSKZXTZT.ClearSamples();
            Frame_121WX.ClearSamples();
            Frame_XTZT.ClearSamples();

            Card_LVDS.OnMonitorDataReceived += LVDSCard_OnMonitorDataReceived;

            LVDSPackageAnalyzer.StartAnalyse();
            DataAnalyzer_CCQMNL.StartAnalyse();
            DataAnalyzer_CCQState.StartAnalyse();
            DataAnalyzer_FSJ.StartAnalyse();
            DataAnalyzer_GZ.StartAnalyse();
            DataAnalyzer_QSSLB.StartAnalyse();
            DataAnalyzer_SSKZXTZT.StartAnalyse();
            DataAnalyzer_121WX.StartAnalyse();
            DataAnalyzer_XTZT.StartAnalyse();
        }

        private static void LVDSCard_OnMonitorDataReceived(object? sender, byte[] e)
        {
            LVDSPackageAnalyzer.AddRawData(e);
        }

        /// <summary>
        /// 软件开启(121)即调用
        /// </summary>
        private static void StartAnalyse422()
        {
            Frame_CCQState422.ClearSamples();
            Card_Digital.StartMonitor();
            DigitalFrameAnalyzer.StartAnalyse();
        }

        public static void StartMonitor422()
        {
            Frame_TY.ClearSamples();

            DigitalFrameAnalyzer.OnTYReceived += DigitalFrameAnalyzer_OnTYReceived;

            DataAnalyzer_TY.StartAnalyse();
        }

        public static void StopMonitor422()
        {
            DigitalFrameAnalyzer.OnTYReceived -= DigitalFrameAnalyzer_OnTYReceived;
            DataAnalyzer_TY.StopAnalyse();
        }

        private static void DigitalFrameAnalyzer_OnTYReceived(object? sender, byte[] e)
        {
           DataAnalyzer_TY.AddRawData(e);
        }

        public static void StopSimulate()
        {
            Simulator1.StopSimulator();
            Simulator2.StopSimulator();
            Simulator3.StopSimulator();
            Simulator4.StopSimulator();
            Simulator5.StopSimulator();
            Simulator6.StopSimulator();
            Simulator7.StopSimulator();
            Simulator8.StopSimulator();
        }

        public static void StopMonitorLVDS()
        {
            Card_LVDS.OnMonitorDataReceived -= LVDSCard_OnMonitorDataReceived;

            LVDSPackageAnalyzer.StopAnalyse();
            DataAnalyzer_CCQMNL.StopAnalyse();
            DataAnalyzer_CCQState.StopAnalyse();
            DataAnalyzer_CCQState422.StopAnalyse();
            DataAnalyzer_FSJ.StopAnalyse();
            DataAnalyzer_GZ.StopAnalyse();
            DataAnalyzer_QSSLB.StopAnalyse();
            DataAnalyzer_SSKZXTZT.StopAnalyse();
            DataAnalyzer_TY.StopAnalyse();
            DataAnalyzer_XTZT.StopAnalyse();
        }




    }
}
