using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using System.Linq;

namespace 测量地面综合测控台上位机软件.VM
{
    /// <summary>
    /// 树节点基类
    /// </summary>
    public abstract partial class TreeNodeViewModel : ObservableObject
    {
        [ObservableProperty]
        private bool isExpanded = false;

        [ObservableProperty]
        private bool isVisible = true;

        public abstract string DisplayName { get; }
        public abstract string NodeType { get; }

        /// <summary>
        /// 应用搜索过滤
        /// </summary>
        public abstract void ApplyFilter(string searchText);
    }

    /// <summary>
    /// Frame节点ViewModel
    /// </summary>
    public partial class FrameNodeViewModel : TreeNodeViewModel
    {
        public Frame Frame { get; }
        public ObservableCollection<DeviceNodeViewModel> Devices { get; } = new();

        public override string DisplayName => $"📁 {Frame.FrameName} ({Devices.Count} 设备)";
        public override string NodeType => "Frame";

        public FrameNodeViewModel(Frame frame)
        {
            Frame = frame;
            LoadDevices();
        }

        private void LoadDevices()
        {
            Devices.Clear();
            foreach (var device in Frame.Devices)
            {
                var deviceNode = new DeviceNodeViewModel(device, this);
                Devices.Add(deviceNode);
            }
        }

        public override void ApplyFilter(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                IsVisible = true;
                foreach (var device in Devices)
                {
                    device.ApplyFilter(searchText);
                }
                return;
            }

            var hasVisibleChildren = false;
            foreach (var device in Devices)
            {
                device.ApplyFilter(searchText);
                if (device.IsVisible)
                    hasVisibleChildren = true;
            }

            // Frame节点可见条件：名称匹配或有可见的子节点
            IsVisible = Frame.FrameName.Contains(searchText, System.StringComparison.OrdinalIgnoreCase) || hasVisibleChildren;
            
            // 如果有匹配的子节点，自动展开
            if (hasVisibleChildren && !string.IsNullOrWhiteSpace(searchText))
            {
                IsExpanded = true;
            }
        }
    }

    /// <summary>
    /// Device节点ViewModel
    /// </summary>
    public partial class DeviceNodeViewModel : TreeNodeViewModel
    {
        public Device Device { get; }
        public FrameNodeViewModel Parent { get; }
        public ObservableCollection<ChannelNodeViewModel> Channels { get; } = new();

        public override string DisplayName => GetDeviceDisplayName();
        public override string NodeType => "Device";

        public DeviceNodeViewModel(Device device, FrameNodeViewModel parent)
        {
            Device = device;
            Parent = parent;
            LoadChannels();

            // 监听Device属性变化以更新显示名称
            Device.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(Device.DeviceName) ||
                    e.PropertyName == nameof(Device.IsCurrent))
                {
                    OnPropertyChanged(nameof(DisplayName));
                }
            };
        }

        private void LoadChannels()
        {
            Channels.Clear();
            foreach (var channel in Device.Channels)
            {
                var channelNode = new ChannelNodeViewModel(channel, this);
                Channels.Add(channelNode);
            }
        }

        public override void ApplyFilter(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                IsVisible = true;
                foreach (var channel in Channels)
                {
                    channel.ApplyFilter(searchText);
                }
                return;
            }

            var hasVisibleChildren = false;
            foreach (var channel in Channels)
            {
                channel.ApplyFilter(searchText);
                if (channel.IsVisible)
                    hasVisibleChildren = true;
            }

            // Device节点可见条件：名称匹配或有可见的子节点
            IsVisible = Device.DeviceName.Contains(searchText, System.StringComparison.OrdinalIgnoreCase) || hasVisibleChildren;
            
            // 如果有匹配的子节点，自动展开
            if (hasVisibleChildren && !string.IsNullOrWhiteSpace(searchText))
            {
                IsExpanded = true;
            }
        }

        /// <summary>
        /// 获取设备显示名称
        /// </summary>
        private string GetDeviceDisplayName()
        {
            var icon = "🔧";
            var currentStatus = Device.IsCurrent ? " [当前]" : "";
            return $"{icon} {Device.DeviceName} ({Channels.Count} 通道){currentStatus}";
        }

        /// <summary>
        /// 刷新显示名称
        /// </summary>
        public void RefreshDisplayName()
        {
            OnPropertyChanged(nameof(DisplayName));
        }
    }

    /// <summary>
    /// Channel节点ViewModel
    /// </summary>
    public partial class ChannelNodeViewModel : TreeNodeViewModel
    {
        public Channel Channel { get; }
        public DeviceNodeViewModel Parent { get; }

        public override string DisplayName => GetChannelDisplayName();
        public override string NodeType => "Channel";

        public ChannelNodeViewModel(Channel channel, DeviceNodeViewModel parent)
        {
            Channel = channel;
            Parent = parent;
            
            // 监听Channel属性变化以更新显示名称
            Channel.PropertyChanged += (s, e) => 
            {
                if (e.PropertyName == nameof(Channel.ChannelName) || 
                    e.PropertyName == nameof(Channel.Monitor) || 
                    e.PropertyName == nameof(Channel.ShowCurve))
                {
                    OnPropertyChanged(nameof(DisplayName));
                }
            };
        }

        private string GetChannelDisplayName()
        {
            var icon = "📊";
            var status = "";
            
            if (Channel.Monitor && Channel.ShowCurve)
                status = " [监控+曲线]";
            else if (Channel.Monitor)
                status = " [监控]";
            else if (Channel.ShowCurve)
                status = " [曲线]";

            var description = string.IsNullOrWhiteSpace(Channel.ChannelDescription) 
                ? "" 
                : $" - {Channel.ChannelDescription}";

            return $"{icon} {Channel.ChannelName}{description}{status}";
        }

        public override void ApplyFilter(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                IsVisible = true;
                return;
            }

            // Channel节点可见条件：名称或描述匹配
            IsVisible = Channel.ChannelName.Contains(searchText, System.StringComparison.OrdinalIgnoreCase) ||
                       (!string.IsNullOrWhiteSpace(Channel.ChannelDescription) && 
                        Channel.ChannelDescription.Contains(searchText, System.StringComparison.OrdinalIgnoreCase));
        }
    }
}
