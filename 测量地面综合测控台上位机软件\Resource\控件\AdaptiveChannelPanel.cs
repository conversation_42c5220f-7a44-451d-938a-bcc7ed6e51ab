using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Media;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 自适应通道面板，支持滚动和自适应列数
    /// </summary>
    public class AdaptiveChannelPanel : System.Windows.Controls.UserControl
    {
        private ScrollViewer _scrollViewer;
        private WrapPanel _wrapPanel;
        private double _itemMinWidth = 150; // 每个通道项的最小宽度

        static AdaptiveChannelPanel()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(AdaptiveChannelPanel),
                new FrameworkPropertyMetadata(typeof(System.Windows.Controls.UserControl)));
        }

        public AdaptiveChannelPanel()
        {
            InitializeComponent();
            SizeChanged += OnSizeChanged;
        }

        private void InitializeComponent()
        {
            // 创建ScrollViewer作为根容器
            _scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = System.Windows.Media.Brushes.Transparent
            };

            // 创建WrapPanel作为内容容器
            _wrapPanel = new WrapPanel
            {
                Orientation = System.Windows.Controls.Orientation.Horizontal,
                Margin = new Thickness(5)
            };

            _scrollViewer.Content = _wrapPanel;
            Content = _scrollViewer;
        }

        #region 依赖属性

        /// <summary>
        /// 通道列表依赖属性
        /// </summary>
        public static readonly DependencyProperty ChannelsProperty =
            DependencyProperty.Register(nameof(Channels), typeof(IEnumerable<Channel>),
                typeof(AdaptiveChannelPanel),
                new FrameworkPropertyMetadata(null, OnChannelsChanged));

        /// <summary>
        /// 通道列表
        /// </summary>
        public IEnumerable<Channel>? Channels
        {
            get => (IEnumerable<Channel>?)GetValue(ChannelsProperty);
            set => SetValue(ChannelsProperty, value);
        }

        /// <summary>
        /// 值绑定路径依赖属性
        /// </summary>
        public static readonly DependencyProperty ValueBindingPathProperty =
            DependencyProperty.Register(nameof(ValueBindingPath), typeof(string),
                typeof(AdaptiveChannelPanel),
                new FrameworkPropertyMetadata("LastDecValue", OnValueBindingPathChanged));

        /// <summary>
        /// 值绑定路径
        /// </summary>
        public string ValueBindingPath
        {
            get => (string)GetValue(ValueBindingPathProperty);
            set => SetValue(ValueBindingPathProperty, value);
        }

        /// <summary>
        /// 数值格式化字符串依赖属性
        /// </summary>
        public static readonly DependencyProperty ValueFormatProperty =
            DependencyProperty.Register(nameof(ValueFormat), typeof(string),
                typeof(AdaptiveChannelPanel),
                new FrameworkPropertyMetadata("F2", OnValueFormatChanged));

        /// <summary>
        /// 数值格式化字符串
        /// </summary>
        public string ValueFormat
        {
            get => (string)GetValue(ValueFormatProperty);
            set => SetValue(ValueFormatProperty, value);
        }

        /// <summary>
        /// 通道项最小宽度依赖属性
        /// </summary>
        public static readonly DependencyProperty ItemMinWidthProperty =
            DependencyProperty.Register(nameof(ItemMinWidth), typeof(double),
                typeof(AdaptiveChannelPanel),
                new FrameworkPropertyMetadata(150.0, OnItemMinWidthChanged));

        /// <summary>
        /// 通道项最小宽度
        /// </summary>
        public double ItemMinWidth
        {
            get => (double)GetValue(ItemMinWidthProperty);
            set => SetValue(ItemMinWidthProperty, value);
        }

        /// <summary>
        /// 通道项高度依赖属性
        /// </summary>
        public static readonly DependencyProperty ItemHeightProperty =
            DependencyProperty.Register(nameof(ItemHeight), typeof(double),
                typeof(AdaptiveChannelPanel),
                new FrameworkPropertyMetadata(50.0, OnItemHeightChanged));

        /// <summary>
        /// 通道项高度
        /// </summary>
        public double ItemHeight
        {
            get => (double)GetValue(ItemHeightProperty);
            set => SetValue(ItemHeightProperty, value);
        }

        #endregion

        #region 依赖属性变化处理

        private static void OnChannelsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AdaptiveChannelPanel panel)
            {
                panel.RefreshLayout();
            }
        }

        private static void OnValueBindingPathChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AdaptiveChannelPanel panel)
            {
                panel.RefreshLayout();
            }
        }

        private static void OnValueFormatChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AdaptiveChannelPanel panel)
            {
                panel.RefreshLayout();
            }
        }

        private static void OnItemMinWidthChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AdaptiveChannelPanel panel)
            {
                panel._itemMinWidth = (double)e.NewValue;
                panel.UpdateColumnCount();
                panel.RefreshLayout();
            }
        }

        private static void OnItemHeightChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AdaptiveChannelPanel panel)
            {
                panel.RefreshLayout();
            }
        }

        #endregion

        #region 布局管理

        /// <summary>
        /// 容器大小变化时更新列数
        /// </summary>
        private void OnSizeChanged(object sender, SizeChangedEventArgs e)
        {
            UpdateColumnCount();
        }

        /// <summary>
        /// 更新WrapPanel的布局（WrapPanel会自动换行，不需要手动设置列数）
        /// </summary>
        private void UpdateColumnCount()
        {
            if (_wrapPanel == null) return;

            // WrapPanel会根据子控件的大小和容器宽度自动换行
            // 这里可以设置子控件的宽度
            foreach (FrameworkElement element in _wrapPanel.Children)
            {
                element.Width = _itemMinWidth;
                element.Height = ItemHeight;
            }
        }

        /// <summary>
        /// 刷新布局
        /// </summary>
        public void RefreshLayout()
        {
            if (_wrapPanel == null) return;

            // 清除现有内容
            _wrapPanel.Children.Clear();

            if (Channels == null || !Channels.Any()) return;

            // 为每个通道创建显示项
            foreach (var channel in Channels)
            {
                var channelItem = CreateChannelItem(channel);
                _wrapPanel.Children.Add(channelItem);
            }

            // 更新布局
            UpdateColumnCount();
        }

        /// <summary>
        /// 创建单个通道显示项
        /// </summary>
        private FrameworkElement CreateChannelItem(Channel channel)
        {
            // 创建容器
            var border = new Border
            {
                BorderBrush = System.Windows.Media.Brushes.LightGray,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(2),
                Padding = new Thickness(5),
                Background = System.Windows.Media.Brushes.White,
                Width = _itemMinWidth,
                Height = ItemHeight
            };

            // 创建内容面板
            var stackPanel = new StackPanel
            {
                Orientation = System.Windows.Controls.Orientation.Vertical,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 创建通道描述标签
            var labelTextBlock = new TextBlock
            {
                Text = channel.ChannelDescription ?? channel.ChannelName,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                TextTrimming = TextTrimming.CharacterEllipsis,
                Margin = new Thickness(0, 0, 0, 2)
            };

            // 创建通道值显示
            var valueTextBlock = new TextBlock
            {
                Text = "未知",
                TextAlignment = TextAlignment.Center,
                FontSize = 12,
                Foreground = System.Windows.Media.Brushes.Blue
            };

            // 绑定通道值
            var binding = new System.Windows.Data.Binding(ValueBindingPath)
            {
                Source = channel,
                StringFormat = !string.IsNullOrEmpty(ValueFormat) ? $"{{0:{ValueFormat}}}" : null
            };
            valueTextBlock.SetBinding(TextBlock.TextProperty, binding);

            // 添加单位显示（如果需要）
            var unitTextBlock = new TextBlock
            {
                TextAlignment = TextAlignment.Center,
                FontSize = 10,
                Foreground = System.Windows.Media.Brushes.Gray
            };

            // 根据绑定路径显示对应单位
            unitTextBlock.Text = ValueBindingPath switch
            {
                "LastVolValue" => channel.Unit1 ?? "",
                "LastPhyValue" => channel.Unit2 ?? "",
                _ => ""
            };

            // 组装控件
            stackPanel.Children.Add(labelTextBlock);
            stackPanel.Children.Add(valueTextBlock);
            if (!string.IsNullOrEmpty(unitTextBlock.Text))
            {
                stackPanel.Children.Add(unitTextBlock);
            }

            border.Child = stackPanel;
            return border;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置通道列表的便捷方法
        /// </summary>
        /// <param name="channels">通道列表</param>
        /// <param name="bindingPath">绑定路径</param>
        /// <param name="format">格式化字符串</param>
        public void SetChannels(IEnumerable<Channel>? channels, string bindingPath = "LastDecValue", string format = "F2")
        {
            Channels = channels;
            ValueBindingPath = bindingPath;
            ValueFormat = format;
        }

        #endregion
    }
}
