﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DevExpress.Mvvm;
using DevExpress.Mvvm.DataAnnotations;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Window;


namespace 测量地面综合测控台上位机软件
{
    public partial class DataProcessViewModel : ObservableObject
    {
        [ObservableProperty]
        public string? fileName;

        [ObservableProperty]
        public double percentProgress = 0;



        [ObservableProperty]
        public bool isProcessing = false;

        public event EventHandler<string>? MsgReceived;

        private CancellationTokenSource cancellationTokenSource = new();

        [ObservableProperty]
        public AsyncCommand startProcessCommand;

        public DataProcessViewModel()
        {
            cancellationTokenSource.Token.Register(() => { MsgReceived?.Invoke(this, "已取消处理"); });
            StartProcessCommand = new AsyncCommand(StartProcess);
        }


        public async Task StartProcess()
        {
            if (cancellationTokenSource.IsCancellationRequested)
            {
                cancellationTokenSource.Dispose();
                cancellationTokenSource = new CancellationTokenSource();
                cancellationTokenSource.Token.Register(() => { MsgReceived?.Invoke(this, "已取消处理"); });
            }

            OpenFileDialog fileDialog = new OpenFileDialog()
            {
                Filter = "dat|*.dat|bin|*.bin|All|*.*",
                DefaultDirectory = AppDomain.CurrentDomain.BaseDirectory
            };

            if (fileDialog.ShowDialog() == false)
                return;


            FileName = fileDialog.FileName;

            //if (string.IsNullOrEmpty(FileName))
            //{
            //    MsgReceived?.Invoke(this, "请先选择文件！");
            //    return;
            //}

            try
            {
                IsProcessing = true;
                MsgReceived?.Invoke(this, "开始处理数据文件...");

                // 调用ExtractPackage方法进行数据解析
                DataProcessOutput report = await ExtractPackage(FileName);
                MsgReceived?.Invoke(this, "\r\n=== LVDS卡数据校验报告 ===");
                MsgReceived?.Invoke(this, report.Reports[0].Report);

                DataProcessOutput report1 = await ExtractStorageData(report.OutFiles[0]);
                MsgReceived?.Invoke(this, "\r\n=== 存储卡数据校验报告 ===");
                MsgReceived?.Invoke(this, report1.Reports[0].Report);

                // 处理完成，显示报告
                MsgReceived?.Invoke(this, "数据处理完成！");

            }
            catch (OperationCanceledException ce)
            {
                MsgReceived?.Invoke(this, "处理已取消");
            }
            catch (Exception ex)
            {
                MsgReceived?.Invoke(this, $"处理失败：{ex.Message}");
            }
            finally
            {
                IsProcessing = false;
            }
        }

        [RelayCommand]
        public void StopProcess()
        {
            if (!cancellationTokenSource.IsCancellationRequested)
            {
                cancellationTokenSource.Cancel();
            }
        }


        public struct DataProcessOutput
        {
            public CounterChecker[] Reports;

            public string[] OutFiles;

        }

        /// <summary>
        /// 分解LVDS板卡原始数据，提取出数据区
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private async Task<DataProcessOutput> ExtractPackage(string fileName)
        {
            // 创建帧计数检查器，用于生成报告
            CounterChecker report = new();
            FileStream? fileOut = null;
            FileStream? fileIn = null;
            string outputFileName = "";
            try
            {
                // 创建输出目录
                string baseFileName = Path.GetFileNameWithoutExtension(fileName);
                string outputDir = Path.Combine(Path.GetDirectoryName(fileName) ?? "", baseFileName);
                Directory.CreateDirectory(outputDir);

                // 生成输出文件名
                outputFileName = Path.Combine(outputDir, $"{baseFileName}_数据区.dat");

                // 打开输入和输出文件流
                fileIn = new(fileName, FileMode.Open, FileAccess.Read, FileShare.Read, 81920, true);
                fileOut = new FileStream(outputFileName, FileMode.Create, FileAccess.Write, FileShare.None, 81920, true);

                // 创建缓冲区
                byte[] readBuffer = new byte[8192000];
                byte[] writeBuffer = new byte[8192000];
                int writeBufferLen = 0;


                MsgReceived?.Invoke(this, $"开始校验LVDS数据：{fileName}");
                MsgReceived?.Invoke(this, $"输出文件：{outputFileName}");

                // 读取并处理文件
                while (fileIn.Position < fileIn.Length)
                {
                    // 检查取消令牌
                    cancellationTokenSource.Token.ThrowIfCancellationRequested();

                    // 读取数据
                    int bytesRead = await fileIn.ReadAsync(readBuffer, cancellationTokenSource.Token);
                    if (bytesRead == 0)
                        break;

                    // 解析数据中的帧
                    for (int i = 0; i < bytesRead - 4; i++)
                    {
                        // 检查帧头标识 (0xEB 0x90)
                        if (readBuffer[i] == 0xEB && readBuffer[i + 1] == 0x90 && readBuffer[i + 4] == 0x01)
                        {
                            // 获取帧长度
                            int frameLen = (readBuffer[i + 2] << 8) | readBuffer[i + 3];

                            // 确保帧完整
                            if (i + frameLen <= bytesRead)
                            {
                                // 检查帧尾标识 (0x14 0x6F)
                                if (readBuffer[i + frameLen - 2] == 0x14 && readBuffer[i + frameLen - 1] == 0x6F)
                                {
                                    // 提取帧计数并添加到报告
                                    ushort frameCounter = (ushort)((readBuffer[i + 5] << 8) | readBuffer[i + 6]);
                                    report.AddCounter(frameCounter, fileIn.Position - bytesRead + i);

                                    // 提取数据区
                                    int dataLen = frameLen - 11; // 帧长度减去帧头(4)、类型(1)、计数(2)、帧尾(2)、校验(2)

                                    // 如果写缓冲区将满，先写入文件
                                    if (writeBufferLen + dataLen > writeBuffer.Length)
                                    {
                                        await fileOut.WriteAsync(writeBuffer.AsMemory(0, writeBufferLen), cancellationTokenSource.Token);
                                        await fileOut.FlushAsync(cancellationTokenSource.Token);
                                        writeBufferLen = 0;
                                    }

                                    // 复制数据区到写缓冲区
                                    Buffer.BlockCopy(readBuffer, i + 9, writeBuffer, writeBufferLen, dataLen);
                                    writeBufferLen += dataLen;

                                    // 跳过已处理的帧
                                    i += frameLen - 1;
                                }
                            }
                            else
                            {
                                // 如果帧不完整，回退文件指针以便下次读取完整帧
                                if (fileIn.Position < fileIn.Length)
                                    fileIn.Seek(i - bytesRead, SeekOrigin.Current);
                                break;
                            }
                        }
                    }

                    // 更新进度
                    PercentProgress = (double)fileIn.Position / fileIn.Length * 100;

                    // 定期报告进度
                    //if (fileIn.Position - lastReportPosition >= reportInterval)
                    //{
                    //    lastReportPosition = fileIn.Position;
                    //    MsgReceived?.Invoke(this, $"已处理：{fileIn.Position / 1024.0 / 1024.0:F2} MB ({PercentProgress:F1}%)");
                    //    MsgReceived?.Invoke(this, $"已解析帧数：{report.TotalCount}");
                    //}
                }

                PercentProgress = 100;

                // 写入剩余数据
                if (writeBufferLen > 0)
                {
                    await fileOut.WriteAsync(writeBuffer.AsMemory(0, writeBufferLen), cancellationTokenSource.Token);
                    await fileOut.FlushAsync(cancellationTokenSource.Token);
                }

                // 报告处理结果
                MsgReceived?.Invoke(this, $"LVDS数据校验完成");

                return new DataProcessOutput() { Reports = [report], OutFiles = [outputFileName] };
            }
            catch (OperationCanceledException)
            {
                MsgReceived?.Invoke(this, "LVDS数据校验已取消");
                return new DataProcessOutput() { Reports = [report], OutFiles = [outputFileName] };
            }
            catch (Exception ex)
            {
                MsgReceived?.Invoke(this, $"LVDS数据校验失败：{ex.Message}");
                return new DataProcessOutput() { Reports = [report], OutFiles = [outputFileName] };
            }
            finally
            {
                // 确保文件流被正确关闭
                fileIn?.Close();
                fileOut?.Flush();
                fileOut?.Close();
            }
        }




        /// <summary>
        /// 分解存储器数据，拆分出8种帧
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private async Task<DataProcessOutput> ExtractStorageData(string fileName)
        {
            DataProcessOutput retVal = new DataProcessOutput();

            retVal.Reports =
            [
                new CounterChecker(1,"X6",0xFFFFFF),        //存储器数据包
                //new CounterChecker(1,"X12",0xFFFFFFFFFFFF), //存储器模拟量
                //new CounterChecker(1,"X6",0xFFFFFF),        //121无线
                //new CounterChecker(1,"X6",0xFFFFFF),        //惯阻
                //new CounterChecker(1,"X6",0xFFFFFF),        //发射机
                //new CounterChecker(1,"X6",0xFFFFFF),        //存储器状态
                //new CounterChecker(1,"X6",0xFFFFFF),        //七室水冷泵
                //new CounterChecker(1,"X6",0xFFFFFF),        //系统状态
                //new CounterChecker(1,"X6",0xFFFFFF)         //三室控制系统状态
            ];

            string dir = new FileInfo(fileName).Directory.FullName;


            retVal.OutFiles =
                [ $"{dir}存储器模拟量.dat",
                  $"{dir}121无线.dat",
                  $"{dir}惯阻.dat",
                  $"{dir}发射机.dat",
                  $"{dir}存储器状态.dat",
                  $"{dir}七室水冷泵.dat",
                  $"{dir}系统状态.dat",
                  $"{dir}三室控制系统状态.dat"
                ];

            FileStream? fileIn = null;
            FileStream[] fileOut = new FileStream[retVal.OutFiles.Length];
            BufferedStream[] streamWriters = new BufferedStream[retVal.OutFiles.Length];
            try
            {
                // 打开输入和输出文件流
                fileIn = new(fileName, FileMode.Open, FileAccess.Read, FileShare.Read, 81920, true);
                for (int i = 0; i < retVal.OutFiles.Length; i++)
                {
                    fileOut[i] = new FileStream(retVal.OutFiles[i], FileMode.Create, FileAccess.Write, FileShare.None, 81920, true);
                    streamWriters[i] = new BufferedStream(fileOut[i], 2 * 1024 * 1024);
                }

                // 创建缓冲区
                byte[] readBuffer = new byte[8192000];

                // 读取并处理文件
                while (fileIn.Position < fileIn.Length)
                {
                    // 检查取消令牌
                    cancellationTokenSource.Token.ThrowIfCancellationRequested();

                    // 读取数据
                    int bytesRead = await fileIn.ReadAsync(readBuffer, cancellationTokenSource.Token);
                    if (bytesRead == 0)
                        break;


                    int offset = 0;
                    for (int i = 0; i < bytesRead - 307; i++)
                    {
                        // 检查帧标识 
                        if (readBuffer[i] == 0xFD && readBuffer[i + 1] == 0xB1 && readBuffer[i + 306] == 0x85 && readBuffer[i + 307] == 0x40)
                        {
                            // 提取帧计数
                            retVal.Reports[0].AddCounter(readBuffer.BlockCopy(i + 3, 3), fileIn.Position - bytesRead + i);

                            // 提取数据类型
                            int dataIndex = readBuffer[i + 2] switch
                            {
                                0xA1 => 1, // 存储器模拟量
                                0xB2 => 2, // 121无线
                                0xC3 => 3, // 惯阻
                                0xD4 => 4, // 发射机
                                0xE5 => 5, // 存储器状态
                                0xF6 => 6, // 七室水冷泵
                                0x97 => 7, // 系统状态
                                0x88 => 8, // 三室控制系统状态
                                _ => -1   // 未知类型
                            };

                            // 提取数据区
                            if (dataIndex != -1)
                            {
                                // 写入数据区
                                await streamWriters[dataIndex].WriteAsync(readBuffer.AsMemory(i + 6, 300), cancellationTokenSource.Token);
                            }

                            i += 307; // 跳过已处理的帧
                        }
                        offset = i + 1;

                        if (bytesRead - offset < 307)
                        {
                            // 如果剩余数据不足一帧，重新定位文件指针，退出循环并继续读数
                            fileIn.Seek(offset - bytesRead, SeekOrigin.Current);
                            break;
                        }

                    }


                    // 更新进度
                    PercentProgress = (double)fileIn.Position / fileIn.Length * 100;
                }
                PercentProgress = 100;

                // 写入剩余数据并刷新所有输出流
                for (int i = 0; i < retVal.OutFiles.Length; i++)
                {
                    if (streamWriters[i].Length > 0)
                    {
                        await streamWriters[i].FlushAsync(cancellationTokenSource.Token);
                    }
                }

                MsgReceived?.Invoke(this, $"存储器数据校验完成");
                return retVal;
            }
            catch (OperationCanceledException)
            {
                MsgReceived?.Invoke(this, "存储器数据校验已取消");
                return retVal;
            }
            catch (Exception ex)
            {
                MsgReceived?.Invoke(this, $"存储器数据校验失败：{ex.Message}");
                return retVal;
            }
            finally
            {
                fileIn?.Close();
                foreach (var f in fileOut)
                {
                    f?.Flush();
                    f?.Close();
                }
            }




        }


        private async Task<DataProcessOutput> CheckFrame(string fileName, Frame frameStructure)
        {
            DataProcessOutput retVal = new DataProcessOutput();

            return retVal;
        }


    }
}
