﻿using DevExpress.Xpf.Charts;
using DevExpress.Xpf.Docking;
using DevExpress.Xpf.LayoutControl;
using DevExpress.Xpf.WindowsUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Binding = System.Windows.Data.Binding;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// MonitorPage1.xaml 的交互逻辑
    /// </summary>
    public partial class MonitorPage2 : NavigationPage
    {
        private List<AutoChannelLayoutGroup> _channelGroups = new();


        public MonitorPage2()
        {
            InitializeComponent();
            this.DataContext = new MonitorViewModel();

            

            //if (this.DataContext is MonitorViewModel vm)
            //    InitUI([vm.Frame_CCQMNL, vm.Frame_CCQState, vm.Frame_FSJ, vm.Frame_GZ, vm.Frame_QSSLB, vm.Frame_SSKZXTZT, vm.Frame_TY, vm.Frame_XTZT]);

        }

//        private void InitUI(Frame[] frames)
//        {
//            // 清空现有内容

//            monitorGroup1.Children.Clear();
//            _channelGroups.Clear();

//            #region 初始化LayoutControl
//            foreach (var frame in frames)
//            {
//                // 使用新的AutoChannelLayoutGroup替代原来的手动创建方式
//                AutoChannelLayoutGroup autoLayoutGroup = new()
//                {
//                    Name = $"layCtlBackup{frame.FrameName}",
//                    IsCollapsible = true,
//                    View = LayoutGroupView.GroupBox,
//                    VerticalAlignment = VerticalAlignment.Stretch,
//                    Orientation = System.Windows.Controls.Orientation.Vertical,
//                    // 配置显示属性
//                    ValueBindingPath = "LastDecValue",
//                    ShowChannelDescription = true,
//                    ValueFormat = "",
//                    // 布局配置
//                    ChannelColumns = 1,
//                    RowHeight = 25,
//                };

//                // 绑定Header
//                Binding binding = new()
//                {
//                    Source = frame,
//                    Path = new PropertyPath("FrameName")
//                };
//                autoLayoutGroup.SetBinding(DevExpress.Xpf.LayoutControl.LayoutGroup.HeaderProperty, binding);

//                // 设置通道列表
//                List<Channel> chs = frame.CurrentDeviceChannels;
//                autoLayoutGroup.SetChannels(chs);

//                // 添加到主容器（备用）
//                this.monitorGroup1.Children.Add(autoLayoutGroup);
//            }
//            #endregion

//            #region 原来的手动网格布局代码（保留作为备用）
//            /*
//            foreach (var frame in frames)
//            {
//                LayoutGroup layoutGroup = new() { Name = $"layCtl{frame.FrameName}", IsCollapsible = true, View = LayoutGroupView.GroupBox, VerticalAlignment = VerticalAlignment.Stretch };
//                Binding binding = new()
//                {
//                    Source = frame,
//                    Path = new PropertyPath("FrameName")
//                };
//                layoutGroup.SetBinding(LayoutGroup.HeaderProperty, binding);

//                List<Channel> chs = frame.CurrentDeviceChannels;

//                #region 网格布局
//                Grid grid = new() { Name = $"grid{frame.FrameName}" };

//                if (chs.Count >= 20)
//                {
//                    ColumnDefinition columnDefinition = new() { Width = GridLength.Auto };
//                    ColumnDefinition columnDefinition2 = new() { Width = new GridLength(50, GridUnitType.Star) };
//                    ColumnDefinition columnDefinition3 = new() { Width = GridLength.Auto };
//                    ColumnDefinition columnDefinition4 = new() { Width = new GridLength(50, GridUnitType.Star) };

//                    grid.ColumnDefinitions.Add(columnDefinition);
//                    grid.ColumnDefinitions.Add(columnDefinition2);
//                    grid.ColumnDefinitions.Add(columnDefinition3);
//                    grid.ColumnDefinitions.Add(columnDefinition4);

//                    int row = 0, col = 0;
//                    foreach (Channel ch in chs)
//                    {
//                        RowDefinition rowDefinition = new() { Height = GridLength.Auto };
//                        grid.RowDefinitions.Add(rowDefinition);

//                        TextBlock chName = new() { TextAlignment = TextAlignment.Left ,Margin=new Thickness(5,0,0,0)  };
//                        Binding binding2 = new()
//                        {
//                            Source = ch,
//                            Path = new PropertyPath("ChannelDescription")
//                        };
//                        chName.SetBinding(TextBlock.TextProperty, binding2);

//                        grid.Children.Add(chName);
//                        Grid.SetRow(chName, row);
//                        Grid.SetColumn(chName, 2 * col);

//                        TextBlock chValue = new() { Name = $"chV_{ch.ChannelName}", Text = "未知", Margin = new Thickness(5, 0, 5, 0) };

////#if EnableLastValue
//                        Binding binding3 = new()
//                        {
//                            Source = ch,
//                            Path = new PropertyPath("LastHexValue")
//                        };

//                       // Trace.WriteLine($"初始化界面时{ch.ChannelName}的地址为{Function.getMemory(ch)}");

//                        chValue.SetBinding(TextBlock.TextProperty, binding3); 
////#endif

//                        grid.Children.Add(chValue);
//                        Grid.SetRow(chValue, row);
//                        Grid.SetColumn(chValue, 2 * col + 1);

//                        col++;
//                        if (col == 2)
//                        {
//                            col = 0;
//                            row++;
//                        }

//                    }

//                }
//                else
//                {
//                    ColumnDefinition columnDefinition = new() { Width = new GridLength(70, GridUnitType.Star) };
//                    ColumnDefinition columnDefinition2 = new() { Width = new GridLength(30, GridUnitType.Star) };

//                    grid.ColumnDefinitions.Add(columnDefinition);
//                    grid.ColumnDefinitions.Add(columnDefinition2);

//                    int row = 0;
//                    foreach (Channel ch in chs)
//                    {
//                        RowDefinition rowDefinition = new() { Height = GridLength.Auto };
//                        grid.RowDefinitions.Add(rowDefinition);

//                        TextBlock chName = new() { TextAlignment = TextAlignment.Left };
//                        Binding binding2 = new()
//                        {
//                            Source = ch,
//                            Path = new PropertyPath("ChannelDescription")
//                        };
//                        chName.SetBinding(TextBlock.TextProperty, binding2);

//                        grid.Children.Add(chName);
//                        Grid.SetRow(chName, row);
//                        Grid.SetColumn(chName, 0);

//                        TextBlock chValue = new() { Name = $"chV_{ch.ChannelName}", Text = "未知", Margin = new Thickness(10, 0, 5, 0) };

//                        Binding binding3 = new()
//                        {
//                            Source = ch,
//                            Path = new PropertyPath("LastHexValue")
//                        };
//                        chValue.SetBinding(TextBlock.TextProperty, binding3);

//                        grid.Children.Add(chValue);
//                        Grid.SetRow(chValue, row++);
//                        Grid.SetColumn(chValue, 1);
//                    }
//                }
//                layoutGroup.Children.Add(grid);
//                #endregion

//                #region 瀑布流布局

//                //WrapPanel wrapPanel = new WrapPanel();
//                //foreach (Channel ch in chs)
//                //{
//                //    ChannelDisplayBox box = new ChannelDisplayBox() { Width=150};
//                //    Binding binding2 = new()
//                //    {
//                //        Source = ch,
//                //        Path = new PropertyPath("ChannelDescription")
//                //    };
//                //    box.SetBinding(ChannelDisplayBox.ChannelNameProperty, binding2);

//                //    wrapPanel.Children.Add(box);
//                //}

//                //layoutGroup.Children.Add(wrapPanel);
//                #endregion


//                this.monitorGroup.Children.Add(layoutGroup);

//            }
//            */
//            #endregion



//            #region 初始化绘图区域
//            //foreach (var frame in frames)
//            //{
//            //    List<Channel> chs = frame.Devices.First(o => o.DeviceName == frame.CurrentDeviceName).Channels.FindAll(o => o.ShowCurve == true);

//            //    if (chs.Count == 0)
//            //        continue;

//            //    ChartControl chartControl = new() { Name = $"chart{frame.FrameName}", IsDirectXSurfaceRendering = true  };

//            //    XYDiagram2D diagram = new() { PaneOrientation = System.Windows.Controls.Orientation.Vertical };

//            //    //ChartViewModel chartViewModel = new ChartViewModel();



//            //    chartControl.Diagram = diagram;


//            //cc1.Visibility


//            //    //foreach (Channel ch in chs)
//            //    //{
//            //    //    Pane pane = new();
//            //    //    diagram.Panes.Add(pane);

//            //    //    SecondaryAxisY2D secondaryAxisY2D = new SecondaryAxisY2D()
//            //    //    {
//            //    //        Alignment = AxisAlignment.Near,
//            //    //        GridLinesMinorVisible = false,
//            //    //        GridLinesVisible = true,
//            //    //        Interlaced = true,
//            //    //        LabelAlignment = AxisLabelAlignment.Auto,
//            //    //        LabelPosition = AxisLabelPosition.Outside,
//            //    //        TitlePosition = AxisTitlePosition.Outside
//            //    //    };

//            //    //    SecondaryAxisX2D secondaryAxisX2D = new SecondaryAxisX2D()
//            //    //    {
//            //    //        Alignment = AxisAlignment.Near,
//            //    //        LabelAlignment = AxisLabelAlignment.Auto,
//            //    //        LabelPosition = AxisLabelPosition.Outside
//            //    //    };

//            //    //    AxisTitle title = new AxisTitle() { Content = ch.ChannelDescription, Visible = true, Alignment = TitleAlignment.Center };
//            //    //    secondaryAxisY2D.Title = title;
//            //    //    diagram.SecondaryAxesY.Add(secondaryAxisY2D);

//            //    //    LineSeries2D lineSeries = new LineSeries2D
//            //    //    {
//            //    //        DisplayName = ch.ChannelDescription,
//            //    //        Pane = pane,
//            //    //        AxisY = secondaryAxisY2D,
//            //    //        AxisX = secondaryAxisX2D
//            //    //    };

//            //    //}


//            //    gridChart.Children.Add(chartControl);



//            //}


//            #endregion
//        }
    
    
    
    }
}
