﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 解析从数字量卡接收到的数据（仅121使用） 仅处理通道号为2(记录仪状态)、3(体遥数据)、7(同步422)的帧，其他的跳过
    /// </summary>
    public class DigitalCardFrameAnalyzer
    {
        /// <summary>
        /// 存储原始数据的缓冲区
        /// </summary>
        private readonly ConcurrentQueue<byte[]> rawDataBuffer = new();

        /// <summary>
        /// 数据缓冲区最大容量
        /// </summary>
        public int MaxBufferSize { get; set; } = 10485760;

        /// <summary>
        /// 原始数据缓冲区当前字节数
        /// </summary>
        private int bufferCurrentSize = 0;



        /// <summary>
        /// 数据到达通知信号（唤醒解析线程）
        /// </summary>
        private readonly ManualResetEventSlim dataAvailableEvent = new(false);

        /// <summary>
        /// 解析线程控制标志
        /// </summary>
        private volatile bool isProcessing = true;

        /// <summary>
        /// 解析线程
        /// </summary>
        private Thread? processingThread;

        public event EventHandler<byte[]>? OnCCQStateReceived;
        public event EventHandler<byte[]>? OnTYReceived;
        public event EventHandler<byte[]>? OnTB422Received;

        public void Reset()
        {
            rawDataBuffer.Clear();

            bufferCurrentSize = 0;
        }
        /// <summary>
        /// 获取缓冲区中原始数据大小/字节
        /// </summary>
        /// <returns></returns>
        public int GetRawDataSize()
        {
            return bufferCurrentSize;
        }

        /// <summary>
        /// 获取缓冲区中帧数/帧
        /// </summary>
        /// <returns></returns>
        public int GetFrameCount()
        {
            return rawDataBuffer.Count;
        }

        public void ClearRawDataBuffer()
        {
            rawDataBuffer.Clear();
            bufferCurrentSize = 0;
        }

        public void AddRawData(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return;
            }

            if (bufferCurrentSize + data.Length < MaxBufferSize)
            {
                rawDataBuffer.Enqueue(data);
                bufferCurrentSize += data.Length;
            }
            else
            {
                while (!rawDataBuffer.IsEmpty && bufferCurrentSize + data.Length > MaxBufferSize)
                {
                    if (rawDataBuffer.TryDequeue(out byte[] dataDisposed))
                    {
                        bufferCurrentSize -= dataDisposed.Length;
                        Trace.TraceWarning($"422数据:原始数据缓冲区堆积过多数据");
                    }
                }

                rawDataBuffer.Enqueue(data);
                bufferCurrentSize += data.Length;
            }
            dataAvailableEvent.Set();
        }


        private void ProcessingLoop()
        {
            List<byte> buffer = [];
            isProcessing = true;
            while (isProcessing)
            {
                dataAvailableEvent.Wait(TimeSpan.FromMilliseconds(20));
                dataAvailableEvent.Reset();
                while (rawDataBuffer.TryDequeue(out byte[] rawData))
                {
                    buffer.AddRange(rawData);

                    while (buffer.Count > 3)
                    {
                        if (buffer[0] != 0xEB || buffer[1] != 0x90)
                        {
                            buffer.RemoveRange(0, 2);
                            continue;
                        }
                        int frameLen = (buffer[2] << 8) | buffer[3];
                        if (buffer.Count >= frameLen)
                        {
                            if (buffer[frameLen - 2] == 0x14 && buffer[frameLen - 1] == 0x6F)
                            {
                                int ch = buffer[4];
                                switch (ch)
                                {
                                    case 2:
                                        OnCCQStateReceived?.Invoke(this, [.. buffer.Skip(9).Take(frameLen - 11)]);
                                        break;
                                    case 3:
                                        OnTYReceived?.Invoke(this, [.. buffer.Skip(9).Take(frameLen - 11)]);
                                        break;
                                    case 7:
                                        OnTB422Received?.Invoke(this, [.. buffer.Skip(9).Take(frameLen - 11)]);
                                        break;
                                    default:
                                        break;
                                }
                                buffer.RemoveRange(0, frameLen);
                            }
                            else
                            {
                                buffer.RemoveRange(0, 2);
                                continue;
                            }
                        }
                        else
                            break;
                    }

                }
            }
        }

        public bool StartAnalyse(ThreadPriority priority = ThreadPriority.Normal)
        {
            if (processingThread != null)
                return false;

            Reset();

            processingThread = new Thread(ProcessingLoop)
            {
                IsBackground = true,
                Name = $"解析422帧",
                Priority = priority
            };
            processingThread.Start();
            return true;
        }

        public void StopAnalyse()
        {
            isProcessing = false;
            processingThread?.Join();
            processingThread = null;
        }

        protected virtual void Dispose(bool disposing)
        {
            isProcessing = false;
            processingThread?.Join();  // 等待线程退出
            dataAvailableEvent.Dispose();
            GC.SuppressFinalize(this);
        }

        ~DigitalCardFrameAnalyzer()
        {
            Dispose(disposing: false);
        }

        public void Dispose()
        {
            Dispose(disposing: true);
        }
    }
}
