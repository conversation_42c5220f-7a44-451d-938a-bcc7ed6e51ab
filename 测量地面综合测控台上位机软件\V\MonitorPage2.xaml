﻿<dxwui:NavigationPage
    x:Class="测量地面综合测控台上位机软件.MonitorPage2"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
    xmlns:dxc="http://schemas.devexpress.com/winfx/2008/xaml/charts"
    xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
    xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
    xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    d:DesignHeight="450"
    d:DesignWidth="800">


   
    
    
    <DockPanel>

        <DockPanel.Resources>
            <DataTemplate DataType="{x:Type local:SerializableViewModel}">
                <ContentPresenter Content="{Binding Content}" />
            </DataTemplate>
            <Style TargetType="{x:Type dxdo:LayoutPanel}">
                <Setter Property="BindableName" Value="{Binding Name}" />
                <Setter Property="Caption" Value="{Binding DisplayName}" />
            </Style>
            <Thickness x:Key="ElementMargin">0,4,0,0</Thickness>
        </DockPanel.Resources>
        
        
        <dxlc:LayoutControl
            x:Name="layoutControl"
            AllowAvailableItemsDuringCustomization="False"
            AllowNewItemsDuringCustomization="False"
            Background="White"
            IsCustomization="False"
            Orientation="Horizontal">

            <dxdo:DockLayoutManager
                x:Name="dockManager"
                Background="White"
                ItemsSource="{Binding SerializationViewModel.Items}">
                <dxmvvm:Interaction.Behaviors>
                    <local:DockLayoutManagerSerializationService />
                </dxmvvm:Interaction.Behaviors>
                <dxdo:MVVMHelper.LayoutAdapter>
                    <local:MVVMSerializationLayoutAdapter />
                </dxdo:MVVMHelper.LayoutAdapter>
                
                    <dxdo:LayoutGroup x:Name="monitorGroup" />
                    
            </dxdo:DockLayoutManager>

            <dxlc:LayoutGroup
                Width="300"
                dxlc:LayoutControl.AllowHorizontalSizing="True"
                IsCollapsible="True"
                View="Group">
                <ScrollViewer>
                   
                    
                    
                    <dxlc:LayoutGroup Orientation="Vertical" View="Group">
                        <UniformGrid Columns="1" Name="gridChart">
                            <dxc:ChartControl Name="cc1" Visibility="{Binding chart1.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart1.ChPanes}" SeriesItemsSource="{Binding chart1.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane dxc:GridLayout.RowSpan="{Binding Weight}">
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart1.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                            <dxc:ChartControl Name="cc2" Visibility="{Binding chart2.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart2.ChPanes}" SeriesItemsSource="{Binding chart2.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane dxc:GridLayout.RowSpan="{Binding Weight}">
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart2.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>

                            <dxc:ChartControl Name="cc3" Visibility="{Binding chart3.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart3.ChPanes}" SeriesItemsSource="{Binding chart3.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane dxc:GridLayout.RowSpan="{Binding Weight}">
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart3.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                            <dxc:ChartControl Name="cc4" Visibility="{Binding chart4.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart4.ChPanes}" SeriesItemsSource="{Binding chart4.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane dxc:GridLayout.RowSpan="{Binding Weight}">
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart4.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                            <dxc:ChartControl Name="cc5" Visibility="{Binding chart5.Visibility}">
                                <dxc:ChartControl.Titles>
                                    <dxc:Title
                                        Margin="0"
                                        HorizontalAlignment="Center"
                                        Content="{Binding chart5.FrameName}"
                                        FontSize="18"
                                        FontWeight="Bold" />
                                </dxc:ChartControl.Titles>


                                <dxc:XYDiagram2D
                                    EnableAxisXNavigation="False"
                                    PaneItemsSource="{Binding chart5.ChPanes}"
                                    SecondaryAxisXItemsSource="{Binding chart5.ChPanes}"
                                    SeriesItemsSource="{Binding chart5.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                ArgumentScaleType="Numerical"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane>
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D
                                            StickToEdge="True"
                                            VisibilityInPaneItemsSource="{Binding chart5.ChPanes}"
                                            Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>

                                    <dxc:XYDiagram2D.AxisY>
                                        <dxc:AxisY2D
                                            GridLinesVisible="True"
                                            Interlaced="True"
                                            Visible="True" />
                                    </dxc:XYDiagram2D.AxisY>

                                    <dxc:XYDiagram2D.SecondaryAxisYItemTemplate>
                                        <DataTemplate>
                                            <dxc:SecondaryAxisY2D GridLinesVisible="True">
                                                <dxc:SecondaryAxisY2D.NumericScaleOptions>
                                                    <dxc:ContinuousNumericScaleOptions AutoGrid="True" GridSpacing="20" />
                                                </dxc:SecondaryAxisY2D.NumericScaleOptions>
                                                <dxc:SecondaryAxisY2D.WholeRange>
                                                    <dxc:Range dxc:AxisY2D.AlwaysShowZeroLevel="False" />
                                                </dxc:SecondaryAxisY2D.WholeRange>
                                            </dxc:SecondaryAxisY2D>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SecondaryAxisYItemTemplate>


                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                            <dxc:ChartControl Name="cc6" Visibility="{Binding chart6.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart6.ChPanes}" SeriesItemsSource="{Binding chart6.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane>
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart6.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                            <dxc:ChartControl Name="cc7" Visibility="{Binding chart7.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart7.ChPanes}" SeriesItemsSource="{Binding chart7.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane>
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart7.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                            <dxc:ChartControl Name="cc8" Visibility="{Binding chart8.Visibility}">
                                <dxc:XYDiagram2D PaneItemsSource="{Binding chart8.ChPanes}" SeriesItemsSource="{Binding chart8.ChSeries}">
                                    <dxc:XYDiagram2D.SeriesItemTemplate>
                                        <DataTemplate>
                                            <dxc:LineSeries2D
                                                ArgumentDataMember="Time"
                                                DataSource="{Binding Values}"
                                                DisplayName="{Binding ChannelName}"
                                                MarkerVisible="True"
                                                Pane="{Binding Pane}"
                                                ValueDataMember="Value" />
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.SeriesItemTemplate>
                                    <dxc:XYDiagram2D.DefaultPane>
                                        <dxc:Pane Visibility="Collapsed" />
                                    </dxc:XYDiagram2D.DefaultPane>
                                    <dxc:XYDiagram2D.PaneItemTemplate>
                                        <DataTemplate>
                                            <dxc:Pane>
                                                <dxc:Pane.Title>
                                                    <dxc:PaneTitle
                                                        Content="{Binding Title}"
                                                        FontSize="12"
                                                        FontWeight="Bold"
                                                        Visible="True" />
                                                </dxc:Pane.Title>
                                            </dxc:Pane>
                                        </DataTemplate>
                                    </dxc:XYDiagram2D.PaneItemTemplate>
                                    <dxc:XYDiagram2D.PaneLayout>
                                        <dxc:GridLayout>
                                            <dxc:GridLayout.ColumnDefinitions>
                                                <dxc:LayoutDefinition />
                                            </dxc:GridLayout.ColumnDefinitions>
                                        </dxc:GridLayout>
                                    </dxc:XYDiagram2D.PaneLayout>
                                    <dxc:XYDiagram2D.AxisX>
                                        <dxc:AxisX2D VisibilityInPaneItemsSource="{Binding chart8.ChPanes}" Visible="True">
                                            <dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                                <DataTemplate>
                                                    <ContentControl>
                                                        <dxc:VisibilityInPane Pane="{Binding}" Visible="{Binding ShowXAxis}" />
                                                    </ContentControl>
                                                </DataTemplate>
                                            </dxc:AxisX2D.VisibilityInPaneItemTemplate>
                                        </dxc:AxisX2D>
                                    </dxc:XYDiagram2D.AxisX>
                                </dxc:XYDiagram2D>
                            </dxc:ChartControl>
                        </UniformGrid>
                    </dxlc:LayoutGroup>
                </ScrollViewer>
            </dxlc:LayoutGroup>


        </dxlc:LayoutControl>


    </DockPanel>

</dxwui:NavigationPage>