﻿using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public partial class LVDSCard(string mcardName, uint cardAddress) : PCICard(mcardName, cardAddress)
    {
        /// <summary>
        /// 最大读数容量
        /// </summary>
        [ObservableProperty]
        public int maxReadDataMB = -1;

        /// <summary>
        /// 当前读数容量
        /// </summary>
        [ObservableProperty]
        public int currentReadDataMB = -1;

        /// <summary>
        /// 一次读取数据量，单位：字节 匹配半满容量
        /// </summary>
        [ObservableProperty]
        public static int onceGetBytes = 8192;

        /// <summary>
        /// 最大读取次数
        /// </summary>
        private int maxReadTimes = -1;


        /// <summary>
        /// true:按照设定的读取容量，跟新界面；    false：按照设定的百分比跟新界面；
        /// </summary>
        [ObservableProperty]
        public bool reportProgressMode_MB = true;

        /// <summary>
        ///  默认按照1MB 更新界面；
        /// </summary>
        [ObservableProperty]
        public int reportProgressMB = 1;

        /// <summary>
        ///  默认按照总读数容量的1%进行更新界面
        /// </summary>
        [ObservableProperty]
        public double reportProgressPercent = 1;

        /// <summary>
        /// 保存的数据文件的完整路径
        /// </summary>
        [ObservableProperty]
        public string saveDataFileName = "";

        /// <summary>
        /// 后台线程更新前台信息频率，值越大更新次数越少
        /// </summary>
        private int rptTimes = 10000000;



        public event EventHandler DataDownloadCompleted;

        [ObservableProperty]
        private double downloadPercent;

        [ObservableProperty]
        private long downloadedBytes = 0;

        [ObservableProperty]
        private string downloadedBytesStr;


        private Thread? monitorThread;
        private volatile bool isMonitoring = false;
        private volatile bool shouldStopMonitoring = false; // 监测线程停止信号

        private Thread? downloadThread;
        private volatile bool isDownloading = false;
        private volatile bool shouldStopDownloading = false; // 下载线程停止信号

        private readonly ManualResetEventSlim stopEvent = new(false); // 停止事件

        public event EventHandler<byte[]>? OnMonitorDataReceived;

        /// <summary>
        /// 获取当前是否正在监测
        /// </summary>
        public bool IsMonitoring => isMonitoring;

        /// <summary>
        /// 获取当前是否正在下载
        /// </summary>
        public bool IsDownloading => isDownloading;

        /// <summary>
        /// 获取当前是否有任何操作在运行
        /// </summary>
        public bool IsBusy => isMonitoring || isDownloading;

        /// <summary>
        /// 获取当前运行状态描述
        /// </summary>
        public string CurrentStatus
        {
            get
            {
                if (isMonitoring) return "监测中";
                if (isDownloading) return "下载中";
                return "空闲";
            }
        }
        public bool InitReadData()
        {
            if ((MaxReadDataMB == -1) && (CurrentReadDataMB == -1))
                return false;

            if (string.IsNullOrEmpty(SaveDataFileName))
                return false;

            if (ReportProgressMode_MB)   // true:按照设定的读取容量，更新界面；
                rptTimes = ReportProgressMB * 1024 * 1024 / OnceGetBytes;                                   //128
            else
                rptTimes = (int)(CurrentReadDataMB * ReportProgressPercent * 1024 / 100 / OnceGetBytes);    //8

            maxReadTimes = (int)(CurrentReadDataMB * (long)1024 * 1024 / rptTimes / OnceGetBytes);          //100

            return true;
        }



        /// <summary>
        /// 启动数据下载
        /// </summary>
        public bool StartDownloadData()
        {
#if !DEBUG
            if (!Online)
            {
                OnErrorOccurred($"{CardName}不在线");
                return false;
            }
#endif

            if (isDownloading)
            {
                OnErrorOccurred("数据下载已在运行中");
                return false;
            }

            if (isMonitoring)
            {
                OnErrorOccurred("监测正在运行中，请先停止监测再开始下载");
                return false;
            }

            if (!InitReadData())
            {
                OnErrorOccurred("下载配置不正确");
                return false;
            }


            // 重置下载停止信号
            shouldStopDownloading = false;
            stopEvent.Reset();

            // 创建专用下载线程，设置高优先级
            downloadThread = new Thread(DownloadLoop)
            {
                Priority = ThreadPriority.AboveNormal, // 高优先级确保下载不被打断
                IsBackground = true,
                Name = $"{CardName}_数据下载线程"
            };

            OnInfoOccurred("开始下载数据");

            downloadThread.Start();
            return true;
        }


        /// <summary>
        /// 下载循环
        /// </summary>
        private void DownloadLoop()
        {
            FileStream? fileOut = null;
            byte[]? writeBuffer = null;
            int bufferCount = 0;

            try
            {
                // 在Loop开始时设置状态
                isDownloading = true;

                // 创建输出文件
                fileOut = new FileStream(SaveDataFileName, FileMode.Create, FileAccess.Write, FileShare.Read, 81920, false); // 同步IO

                // 初始化缓冲区
                writeBuffer = new byte[OnceGetBytes * 1024]; // 1024个数据包的缓冲区
                bufferCount = 0;

                // 初始化下载参数
                int cyc = rptTimes;
                int rptVal = 0;
                long totalBytesDownloaded = 0;
                DateTime startTime = DateTime.Now;
                DateTime lastFlushTime = DateTime.Now;

                DownloadPercent = 0;
                DownloadedBytes = 0;
                DownloadedBytesStr = "0.00 B";

                while (rptVal < maxReadTimes && !shouldStopDownloading)
                {
                    try
                    {
#if 本地调试
                        byte[] dat=new byte[OnceGetBytes];
                        Thread.Sleep(1); // 模拟延时
#else
                        // 读取数据
                        byte[] dat = ReadData("LVDS下载数据", false);
#endif
                        if (dat.Length == OnceGetBytes)
                        {
                            // 复制数据到缓冲区
                            Array.Copy(dat, 0, writeBuffer, bufferCount * OnceGetBytes, OnceGetBytes);
                            bufferCount++;
                            totalBytesDownloaded += OnceGetBytes;

                            // 当缓冲区满时写入文件
                            if (bufferCount == 1024)
                            {
                                fileOut.Write(writeBuffer, 0, writeBuffer.Length);
                                fileOut.Flush();
                                bufferCount = 0;
                                lastFlushTime = DateTime.Now;
                            }
                        }
                        else
                        {
                            // 没有数据时短暂休眠
                            try
                            {
                                Trace.WriteLine($"没有数据可读{dat.Length}");
                                Thread.Sleep(0);
                            }
                            catch (ThreadInterruptedException)
                            {
                                // 线程被中断，正常退出
                                break;
                            }
                        }

                        cyc--;

                        // 更新进度
                        if (cyc == 0)
                        {
                            DownloadPercent = rptVal * 100.0 / maxReadTimes;
                            DownloadedBytes = totalBytesDownloaded;
                            DownloadedBytesStr = DownloadedBytes.ConvertToFileSize();
                            rptVal++;
                            cyc = rptTimes;
                        }
                        // 定期强制刷新缓冲区
                        //if (bufferCount > 0 && (DateTime.Now - lastFlushTime).TotalSeconds >= 2.0)
                        //{
                        //    fileOut.Write(writeBuffer, 0, bufferCount * OnceGetBytes);
                        //    fileOut.Flush();
                        //    bufferCount = 0;
                        //    lastFlushTime = DateTime.Now;
                        //}
                    }
                    catch (ThreadInterruptedException)
                    {
                        // 线程被中断，正常退出
                        App.Log.Debug("下载线程被中断，正常退出");
                        break;
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续运行，避免因单次错误中断整个下载
                        App.Log.Debug($"下载循环中发生错误: {ex.Message}");
                        try
                        {
                            Thread.Sleep(10); // 短暂延迟后继续
                        }
                        catch (ThreadInterruptedException)
                        {
                            // 在错误恢复期间被中断，退出
                            break;
                        }
                    }
                }

                // 写入剩余数据
                if (bufferCount > 0)
                {
                    fileOut.Write(writeBuffer, 0, bufferCount * OnceGetBytes);
                    fileOut.Flush();
                    totalBytesDownloaded += bufferCount * OnceGetBytes;
                }

                // 更新最终进度
                DownloadPercent = 100.0;
                DownloadedBytes = totalBytesDownloaded;

                // 输出统计信息
                TimeSpan duration = DateTime.Now - startTime;
                double avgSpeed = totalBytesDownloaded / duration.TotalSeconds / 1024.0; // KB/s
                OnInfoOccurred($"下载完成 - 总下载: {totalBytesDownloaded} 字节, 平均速度: {avgSpeed:F2} KB/s");
            }
            catch (ThreadInterruptedException)
            {
                // 线程被中断，正常退出
                App.Log.Debug("下载线程被中断，正常退出");
                OnInfoOccurred("LVDS卡下载数据被取消");
            }
            catch (Exception ex)
            {
                ErrorString = $"LVDS卡下载数据发生异常:{ex.Message}";
                App.Log.Error($"LVDS卡下载数据发生异常: {ex}");
            }
            finally
            {
                // 确保文件被正确关闭
                try
                {
                    if (bufferCount > 0 && fileOut != null && writeBuffer != null)
                    {
                        fileOut.Write(writeBuffer, 0, bufferCount * OnceGetBytes);
                        fileOut.Flush();
                    }
                }
                catch { }

                fileOut?.Dispose();
                isDownloading = false;

                // 通知下载完成
                try
                {
                    DataDownloadCompleted?.Invoke(this, EventArgs.Empty);
                }
                catch (Exception ex)
                {
                    App.Log.Error($"触发LVDS卡下载完成事件时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 启动监测
        /// </summary>
        public bool StartMonitor()
        {
            if (isMonitoring)
            {
                OnErrorOccurred("监测已在运行中");
                return false;
            }

            if (isDownloading)
            {
                OnErrorOccurred("数据下载正在运行中，请先停止下载再开始监测");
                return false;
            }

#if !DEBUG
            if (!Online)
            {
                OnErrorOccurred($"{CardName}不在线");
                return false;
            }
#endif

            // 重置停止信号
            shouldStopMonitoring = false;
            stopEvent.Reset();

            // 创建专用监测线程，设置高优先级
            monitorThread = new Thread(MonitorLoop)
            {
                Priority = ThreadPriority.AboveNormal, // 高优先级确保数据采集不被打断
                IsBackground = true,
                Name = $"{CardName}_数据监测线程"
            };
            OnInfoOccurred("开始监测");
            monitorThread.Start();
            return true;
        }

        /// <summary>
        /// 监测循环
        /// </summary>
        private void MonitorLoop()
        {
            FileStream? fileOut = null;
            byte[]? writeBuffer = null;
            int writeBufferLen = 0;

            try
            {
                // 在Loop开始时设置状态
                isMonitoring = true;

                // 创建数据目录
                string dataDir = $"{AppDomain.CurrentDomain.BaseDirectory}数据\\监测数据";
                Directory.CreateDirectory(dataDir);

                // 创建输出文件
                string fileName = $"{dataDir}\\{DateTime.Now:yyyy-MM-dd-HH-mm-ss}_LVDS.dat";
                fileOut = new FileStream(fileName, FileMode.Create, FileAccess.Write, FileShare.Read, 81920, false); // 同步IO以确保数据完整性

                // 创建写缓冲区（10MB）
                writeBuffer = new byte[10485760];
                writeBufferLen = 0;

                // 统计信息
                long totalBytesReceived = 0;
                long totalBytesWritten = 0;
                DateTime startTime = DateTime.Now;
                DateTime lastFlushTime = DateTime.Now;

                while (!shouldStopMonitoring)
                {
                    try
                    {
                        // 读取数据
                        byte[] temp = ReadData("LVDS监测数据", false);

                        if (temp.Length > 0)
                        {
                            OnMonitorDataReceived?.Invoke(this, temp);
                            totalBytesReceived += temp.Length;

                            // 检查缓冲区是否需要刷新
                            if (writeBufferLen + temp.Length > writeBuffer.Length)
                            {
                                // 写入文件并刷新
                                fileOut.Write(writeBuffer, 0, writeBufferLen);
                                fileOut.Flush();
                                totalBytesWritten += writeBufferLen;
                                writeBufferLen = 0;
                                lastFlushTime = DateTime.Now;
                            }

                            // 复制数据到缓冲区
                            Array.Copy(temp, 0, writeBuffer, writeBufferLen, temp.Length);
                            writeBufferLen += temp.Length;
                        }
                        else
                        {
                            // 没有数据时短暂休眠，避免CPU占用过高
                            try
                            {
                                Thread.Sleep(10);
                            }
                            catch (ThreadInterruptedException)
                            {
                                // 线程被中断，正常退出
                                break;
                            }
                        }

                        // 定期强制刷新缓冲区（每秒一次）
                        if ((DateTime.Now - lastFlushTime).TotalSeconds >= 1.0 && writeBufferLen > 0)
                        {
                            fileOut.Write(writeBuffer, 0, writeBufferLen);
                            fileOut.Flush();
                            totalBytesWritten += writeBufferLen;
                            writeBufferLen = 0;
                            lastFlushTime = DateTime.Now;
                        }
                    }
                    catch (ThreadInterruptedException)
                    {
                        // 线程被中断，正常退出
                        App.Log.Debug("监测线程被中断，正常退出");
                        break;
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续运行，避免因单次错误中断整个监测
                        App.Log.Debug($"监测循环中发生错误: {ex.Message}");
                        try
                        {
                            Thread.Sleep(1); // 短暂延迟后继续
                        }
                        catch (ThreadInterruptedException)
                        {
                            // 在错误恢复期间被中断，退出
                            break;
                        }
                    }
                }

                // 写入剩余数据
                if (writeBufferLen > 0)
                {
                    fileOut.Write(writeBuffer, 0, writeBufferLen);
                    fileOut.Flush();
                    totalBytesWritten += writeBufferLen;
                }

                // 输出统计信息
                TimeSpan duration = DateTime.Now - startTime;
                double avgSpeed = totalBytesReceived / duration.TotalSeconds / 1024.0; // KB/s

                OnInfoOccurred($"监测停止 - 总接收: {totalBytesReceived} 字节, 总写入: {totalBytesWritten} 字节, 平均速度: {avgSpeed:F2} KB/s");
            }
            catch (ThreadInterruptedException)
            {
                // 线程被中断，正常退出
                App.Log.Debug("监测线程被中断，正常退出");
            }
            catch (Exception ex)
            {
                ErrorString = $"LVDS卡实时监测发生异常:{ex.Message}";
                App.Log.Error($"LVDS卡实时监测发生异常: {ex}");
            }
            finally
            {
                // 确保文件被正确关闭
                try
                {
                    if (writeBufferLen > 0 && fileOut != null && writeBuffer != null)
                    {
                        fileOut?.Write(writeBuffer, 0, writeBufferLen);
                        fileOut?.Flush();
                    }
                }
                catch { }

                fileOut?.Close();
                isMonitoring = false;
            }
        }


        /// <summary>
        /// 停止下载或监测
        /// </summary>
        public void StopDownloadOrMonitor()
        {
            // 设置停止信号
            shouldStopMonitoring = true;
            shouldStopDownloading = true;
            stopEvent.Set(); // 通知等待的线程

            // 停止下载线程
            if (downloadThread != null && downloadThread.IsAlive)
            {
                try
                {
                    // 等待最多3秒让下载线程正常退出
                    if (!downloadThread.Join(3000))
                    {
                        App.Log.Debug("下载线程未能在3秒内正常退出，尝试中断");

                        try
                        {
                            downloadThread.Interrupt();

                            // 再等待2秒
                            if (!downloadThread.Join(2000))
                            {
                                App.Log.Debug("下载线程中断后仍未退出");
                            }
                        }
                        catch (Exception ex)
                        {
                            App.Log.Error($"中断下载线程时发生错误: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    App.Log.Error($"停止下载线程时发生错误: {ex.Message}");
                }
                finally
                {
                    downloadThread = null;
                }
            }

            // 停止监测线程
            if (monitorThread != null && monitorThread.IsAlive)
            {
                try
                {
                    // 等待最多3秒让线程正常退出
                    if (!monitorThread.Join(3000))
                    {
                        App.Log.Debug("监测线程未能在3秒内正常退出，尝试中断");

                        // 尝试中断线程（如果线程在Sleep或Wait状态）
                        try
                        {
                            monitorThread.Interrupt();

                            // 再等待2秒
                            if (!monitorThread.Join(2000))
                            {
                                App.Log.Debug("监测线程中断后仍未退出");
                                // 注意：不使用Thread.Abort()，因为它在.NET Core/.NET 5+中已被移除
                            }
                        }
                        catch (Exception ex)
                        {
                            App.Log.Error($"中断监测线程时发生错误: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    App.Log.Error($"停止监测线程时发生错误: {ex.Message}");
                }
                finally
                {
                    monitorThread = null;
                }
            }


        }

        /// <summary>
        /// 停止数据下载
        /// </summary>
        public void StopDownload()
        {
            shouldStopDownloading = true;

            if (downloadThread != null && downloadThread.IsAlive)
            {
                try
                {
                    if (!downloadThread.Join(3000))
                    {
                        downloadThread.Interrupt();
                        downloadThread.Join(2000);
                    }
                }
                catch (Exception ex)
                {
                    App.Log.Error($"停止下载线程时发生错误: {ex.Message}");
                }
                finally
                {
                    downloadThread = null;
                }
            }
        }

        /// <summary>
        /// 停止监测
        /// </summary>
        public void StopMonitor()
        {
            shouldStopMonitoring = true;

            if (monitorThread != null && monitorThread.IsAlive)
            {
                try
                {
                    if (!monitorThread.Join(3000))
                    {
                        monitorThread.Interrupt();
                        monitorThread.Join(2000);
                    }
                }
                catch (Exception ex)
                {
                    App.Log.Error($"停止监测线程时发生错误: {ex.Message}");
                }
                finally
                {
                    monitorThread = null;
                }
            }
        }

        /// <summary>
        /// 强制停止所有操作（紧急情况使用）
        /// </summary>
        public void ForceStopAll()
        {
            shouldStopMonitoring = true;
            shouldStopDownloading = true;
            stopEvent.Set();

            // 强制中断所有线程
            try
            {
                downloadThread?.Interrupt();
                monitorThread?.Interrupt();
            }
            catch (Exception ex)
            {
                App.Log.Error($"强制停止线程时发生错误: {ex.Message}");
            }

            // 等待线程退出
            try
            {
                downloadThread?.Join(1000);
                monitorThread?.Join(1000);
            }
            catch (Exception ex)
            {
                App.Log.Error($"等待线程退出时发生错误: {ex.Message}");
            }
            finally
            {
                downloadThread = null;
                monitorThread = null;
                isDownloading = false;
                isMonitoring = false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopDownloadOrMonitor();
            stopEvent?.Dispose();
        }



    }
}
