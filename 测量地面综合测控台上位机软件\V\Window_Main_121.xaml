﻿<Window
    x:Class="测量地面综合测控台上位机软件.Window_Main_121"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
    xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Width="1280"
    Height="708"
    mc:Ignorable="d"
    FontSize="14"
    Title="BYD68-121地面综合测控台上位机软件"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized" SizeToContent="Manual">

    <Window.Resources>
        <!--<ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/测量地面综合测控台上位机软件;component/Resource/LogListViewItem.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            -->        <!-- 其他资源 -->        <!--
        </ResourceDictionary>-->



    </Window.Resources>

    <!--<Window.DataContext >
        <local:MainWindowViewModel  />
    </Window.DataContext>-->

    <DockPanel LastChildFill="True">
        <!-- 顶部工具栏 -->
        <!--<ToolBarTray DockPanel.Dock="Top">-->
        <ToolBar
            Background="White"
            DockPanel.Dock="Top"
            Name="toolBar">
            <dx:SimpleButton Content="配置" Click="Button_Click"  Glyph="{dx:FontIconSource ColorName=Blue, Glyph=63664}" GlyphAlignment="Top" GlyphWidth="30" GlyphToContentOffset="0" />
            <dx:SimpleButton x:Name="btnStartMonitor" Content="开始监测" IsEnabled="{Binding LVDSCard.Online}" Visibility="Visible" Click="SimpleButton_Click" ToolTip="开始监测" Glyph="{dx:FontIconSource ColorName=Green, Glyph=60890}" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />
            <dx:SimpleButton x:Name="btnStopMonitor" Content="停止监测" Visibility="Collapsed" Click="SimpleButton_Click_1" ToolTip="停止监测" Glyph="{dx:FontIconSource ColorName=Red, Glyph=61077}" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />
            <dx:SimpleButton x:Name="btnDownload" Content="数据下载" IsEnabled="{Binding LVDSCard.Online}" Visibility="Visible" Click="btnDownload_Click" ToolTip="数据下载" Glyph="{dx:FontIconSource ColorName=Blue, Glyph=59542}" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />
            <!--<dx:SimpleButton x:Name="btnDownload2" Content="数据下载"  Visibility="Visible" Click="btnDownload_Click" ToolTip="数据下载" Glyph="{dx:FontIconSource ColorName=Blue, Glyph=59542}" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />-->

            <dx:SimpleButton x:Name="btnStartSimulator" Content="生成模拟数据" Visibility="Visible" Click="Button_Click_1"  Glyph="/Image/模拟.png" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />
            <dx:SimpleButton x:Name="btnStopSimulator"  Content="停止仿真" Visibility="Collapsed" Click="SimpleButton_Click_2" ToolTip="停止仿真" Glyph="{dx:FontIconSource ColorName=Red, Glyph=61077}" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />
            <dx:SimpleButton Content="数据处理" Click="SimpleButton_Click_4" ToolTip="数据处理" Glyph="{dx:FontIconSource ColorName=Green, Glyph=59897}" GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30" />

            <dx:SimpleButton Content="信源配置" Click="SimpleButton_Click_3" ToolTip="配置信源卡" Glyph="{dx:FontIconSource ColorName=Blue, Glyph=59865}"  GlyphAlignment="Top" GlyphToContentOffset="0" GlyphWidth="30"  />
           
        </ToolBar>
        <ToolBar Background="White" 
                DockPanel.Dock="Top" >
            <Button x:Name="btnStartTest" Content="开始测试" Margin="0,0,10,0" Click="Button_Click_3"></Button>
            <CheckBox x:Name="chkQS"  Content="发送七室水冷泵数据" Margin="0,0,10,0"></CheckBox>
            <CheckBox x:Name="chkSS"  Content="发送三室控制系统状态数据" Margin="0,0,10,0"></CheckBox>
            <!--<CheckBox x:Name="chkJLYZT"  Content="接收记录仪状态" Margin="0,0,10,0"></CheckBox>-->
            <!--<CheckBox x:Name="chkWX"  Content="接收无线数据" Margin="0,0,10,0"></CheckBox>-->
            <CheckBox x:Name="chkGZ"  Content="发送惯组数据" Margin="0,0,10,0"></CheckBox>
            <CheckBox x:Name="chkFSJ"  Content="发送发射机数据" Margin="0,0,10,0"></CheckBox>
            <!--<CheckBox x:Name="chkTB422"  Content="接收同步422" Margin="0,0,10,0"></CheckBox>-->
        </ToolBar>
        
        <!--</ToolBarTray>-->


        <!-- 底部状态栏 -->
        <StatusBar Height="30" DockPanel.Dock="Bottom">
            <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,4,0"
                    VerticalAlignment="Center"
                    Text="{Binding PowerCardVM.PowerCard.CardName, StringFormat=\{0\}:}" />
                <Image Margin="0,0,0,0" VerticalAlignment="Center">
                    <Image.Style>
                        <Style TargetType="Image">
                            <Style.Triggers>
                                <DataTrigger Value="True" Binding="{Binding PowerCardVM.PowerCard.Online}">
                                    <Setter Property="Source" Value="/Image/green.png" />
                                </DataTrigger>
                                <DataTrigger Value="False" Binding="{Binding PowerCardVM.PowerCard.Online}">
                                    <Setter Property="Source" Value="/Image/gray.png" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>

                <TextBlock
                    Margin="15,0,4,0"
                    VerticalAlignment="Center"
                    Text="{Binding DigitalCard.CardName, StringFormat=\{0\}:}" />
                <Image Margin="0,0,0,0" VerticalAlignment="Center">
                    <Image.Style>
                        <Style TargetType="Image">
                            <Style.Triggers>
                                <DataTrigger Value="True" Binding="{Binding DigitalCard.Online}">
                                    <Setter Property="Source" Value="/Image/green.png" />
                                </DataTrigger>
                                <DataTrigger Value="False" Binding="{Binding DigitalCard.Online}">
                                    <Setter Property="Source" Value="/Image/gray.png" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>

                <TextBlock
                    Margin="15,0,4,0"
                    VerticalAlignment="Center"
                    Text="{Binding LVDSCard.CardName, StringFormat=\{0\}:}" />
                <Image Margin="0,0,0,0" VerticalAlignment="Center">
                    <Image.Style>
                        <Style TargetType="Image">
                            <Style.Triggers>
                                <DataTrigger Value="True" Binding="{Binding LVDSCard.Online}">
                                    <Setter Property="Source" Value="/Image/green.png" />
                                </DataTrigger>
                                <DataTrigger Value="False" Binding="{Binding LVDSCard.Online}">
                                    <Setter Property="Source" Value="/Image/gray.png" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>

                <TextBlock
                    Margin="15,0,4,0"
                    VerticalAlignment="Center"
                    Text="{Binding SignalCard.CardName, StringFormat=\{0\}:}" />
                <Image Margin="0,0,0,0" VerticalAlignment="Center">
                    <Image.Style>
                        <Style TargetType="Image">
                            <Style.Triggers>
                                <DataTrigger Value="True" Binding="{Binding SignalCard.Online}">
                                    <Setter Property="Source" Value="/Image/green.png" />
                                </DataTrigger>
                                <DataTrigger Value="False" Binding="{Binding SignalCard.Online}">
                                    <Setter Property="Source" Value="/Image/gray.png" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>
            </StackPanel>
        </StatusBar>



        <!-- 中间内容区域 -->
        <Grid UseLayoutRounding="True">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition
                    Height="280"
                    MinHeight="105"
                    MaxHeight="280" />
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="0" Grid.Column="0">
                <TextBlock
                    Height="Auto"
                    Margin="10,10,10,10"
                    HorizontalAlignment="Center"
                    FontFamily="SimHei"
                    FontSize="30"
                    FontWeight="Bold"
                    Text="BYD68-121" />





                <dxlc:GroupBox
                    Margin="5"
                    Background="Transparent"
                    Header="28V电源"
                    TitleBackground="Transparent">
                    <StackPanel>
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Voltage28, StringFormat=供电电压：\{0:F1\} V}" />
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Current28, StringFormat=供电电流：\{0:F1\} mA}" />

                        <local:MySwitchButton
                            x:Name="btn28V"
                            Width="129"
                            Height="30"
                            Margin="10,10,10,10"
                            HorizontalAlignment="Stretch"
                            Background="#FAFAFA"
                            Command="{Binding PowerCardVM.PowerCardCommand}"
                            CommandParameter="1"
                            ContextWhenChecked="28V断电"
                            ContextWhenUnChecked="28V供电"
                            hc:BorderElement.CornerRadius="12"
                            IsChecked="{Binding PowerCardVM.Is28VPowerOn}"
                            Style="{StaticResource SwitchButtonStyle}" />
                    </StackPanel>
                </dxlc:GroupBox>

                <dxlc:GroupBox
                    Margin="5"
                    Background="Transparent"
                    Header="5V电源"
                    TitleBackground="Transparent"
                    Visibility="Collapsed">
                    <StackPanel Width="149">
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Voltage5, StringFormat=供电电压：\{0:F1\} V}" />
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Current5, StringFormat=供电电流：\{0:F1\} mA}" />

                        <local:MySwitchButton
                            x:Name="btn5V"
                            Width="129"
                            Height="30"
                            Margin="10,10,10,10"
                            HorizontalAlignment="Stretch"
                            Background="#FAFAFA"
                            Command="{Binding PowerCardVM.PowerCardCommand}"
                            CommandParameter="4"
                            ContextWhenChecked="5V断电"
                            ContextWhenUnChecked="5V供电"
                            hc:BorderElement.CornerRadius="12"
                            IsChecked="{Binding PowerCardVM.Is5VPowerOn}"
                            Style="{StaticResource SwitchButtonStyle}" />
                    </StackPanel>
                </dxlc:GroupBox>

                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="2"
                    Content="激活信号1"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="3"
                    Content="激活信号2"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="3"
                    Content="起飞信号"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="6"
                    Content="有源信号1"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="7"
                    Content="有源信号2"
                    hc:BorderElement.CornerRadius="12" />

                <!--<dxe:CheckEdit x:Name="power28V" Style="{StaticResource power}" Width="50" Height="50"/>

                <dxe:CheckEdit  Style="{StaticResource power}" Width="100" Height="100"/>-->

            </StackPanel>



            <dxwui:NavigationFrame
                Grid.Row="0"
                Grid.Column="1"
                Grid.ColumnSpan="2"
                AnimationSpeedRatio="10"
                AnimationType="None"
                Source="MonitorPage1" />

            <GridSplitter
                Grid.Row="1"
                Grid.ColumnSpan="2"
                Height="2"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center" />
            <ListView
                Grid.Row="2"
                Grid.ColumnSpan="2"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                HorizontalContentAlignment="Stretch"
                VerticalContentAlignment="Center"
                Background="White"
                DataContext="{Binding Logs}"
                ItemContainerStyle="{StaticResource LogListViewItemStyle}"
                ItemsSource="{Binding}"
                Name="LogList"
                ScrollViewer.HorizontalScrollBarVisibility="Auto"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                SizeChanged="LogList_SizeChanged">

                <ListView.View>
                    <GridView>
                        <GridViewColumn
                            Width="120"
                            DisplayMemberBinding="{Binding Time}"
                            Header="时间" />
                        <GridViewColumn
                            Width="80"
                            DisplayMemberBinding="{Binding Type}"
                            Header="类型" />
                        <GridViewColumn Header="消息">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Width="Auto"
                                        Text="{Binding Msg}"
                                        TextWrapping="Wrap"
                                        ToolTip="{Binding Msg}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>
        </Grid>
    </DockPanel>
</Window>