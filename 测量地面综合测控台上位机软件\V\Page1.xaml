﻿<dxwui:NavigationPage
    x:Class="测量地面综合测控台上位机软件.Page1"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
    xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:vm="clr-namespace:测量地面综合测控台上位机软件.VM"
    mc:Ignorable="d"
    d:DesignHeight="450"
    d:DesignWidth="800">

    <!--<dxwui:NavigationPage.DataContext>
        <vm:Page1ViewModel />
    </dxwui:NavigationPage.DataContext>-->

    <!--<dxwui:NavigationPage.Resources>
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter" />
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter" />
    </dxwui:NavigationPage.Resources>-->

    <Grid>
        <!-- 数据表格 - 显示7500字节数据帧 -->
        <dxg:GridControl
            x:Name="DataGrid"
            Margin="5,5,5,5"
            ShowBorder="True" >

            <dxg:GridControl.View>
                <dxg:TableView
                    x:Name="TableView"
                    AllowColumnFiltering="False"
                    AllowColumnMoving="False"
                    AllowEditing="False"
                    AllowSorting="False"
                    AutoWidth="False"
                    FixedLineWidth="1"
                    HorizontalScrollbarVisibility="Auto"
                    ShowGroupPanel="False"
                    ShowHorizontalLines="True"
                    ShowIndicator="True"
                    ShowVerticalLines="True"
                    UseLightweightTemplates="All"
                    VerticalScrollbarVisibility="Auto" />
            </dxg:GridControl.View>
            <dxg:GridColumn FieldName="ID" Width="50"/>
            
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>
            <dxg:GridColumn FieldName="KZ" Width="50"/>

            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>
            <dxg:GridColumn FieldName="ZT" Width="50"/>

            <dxg:GridColumn FieldName="SF" Width="50"/>

            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>
            <dxg:GridColumn FieldName="KZ_M" Width="50"/>

            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>
            <dxg:GridColumn FieldName="ZT_M" Width="50"/>

            <dxg:GridColumn FieldName="同步字" Width="50"/>
            <dxg:GridColumn FieldName="同步字" Width="50"/>
          

        </dxg:GridControl>


    </Grid>
</dxwui:NavigationPage>