﻿using CommunityToolkit.Mvvm.ComponentModel;
using DevExpress.Xpo.DB;
using DevExpress.XtraRichEdit.Model;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public partial class DigitalCard(string cardName, uint cardAddress) : PCICard(cardName, cardAddress)
    {
        /// <summary>
        /// 可以读取的数据量 
        /// <para>0为不可读</para>
        /// <para>-1为未连接</para>
        /// <para>-2为读取异常</para>
        /// </summary>
        /// <returns></returns>
        protected virtual int CanRead
        {
            get
            {
                if (!Online)
                {
                    ErrorString = $"{CardName}未连接";
                    return -1;
                }
                byte[] res = new byte[2];
                int readLen = 0;
                lock (_ioLock)
                {
                    readLen = HNFS_ReadWordByAddr(CardIndex, 0x0030, res);

                    //Trace.WriteLine(res.ToHexString());
                }
                if (readLen == 2)
                {
                    return (res[0] << 8) | res[1];
                }
                else
                {
                    ErrorString = $"从{CardName}：0x0030读取失败,HNFS_ReadWordByAddr的返回值为{readLen}";
                    return -2;
                }
            }
        }

        /// <summary>
        /// 是否可写
        /// </summary>
        /// <param name="ch">通道号 0~15</param>
        /// <returns></returns>
        protected new virtual bool CanWrite(int ch)
        {
            if (!Online)
            {
                ErrorString = $"{CardName}未连接";
                return false;
            }

            //byte expedtedValue = (byte)(0xA0 + ch);


            byte[] res = new byte[2];
            int readLen = 0;
            lock (_ioLock)
            {
                readLen = HNFS_ReadWordByAddr(CardIndex, 0x0040, res);
            }
            if (readLen == 2)
                return (((res[0] * 256 + res[1]) >> ch) & 1) == 1;
            else
            {
                ErrorString = $"从{CardName}：0x0040读取失败";
                return false;
            }

        }

        /// <summary>
        /// 发送指令/数据  发送配置时不需要判断是否可写
        /// </summary>
        /// <param name="data">要发送的指令/数据</param>
        /// <param name="cmdName">指令/数据名称</param>
        /// <returns>-1：不可写</returns>
        /// <exception cref="ArgumentNullException"></exception>
        public virtual int SendData(byte[] data, int ch, bool checkCanWrite = true, string? cmdName = null, bool showLog = true)
        {
            if (data == null || data.Length == 0)
                throw new ArgumentNullException(nameof(data), "要发送的数据不能为空");

            if (data.Length > 8192)
                throw new ArgumentException($"要发送的数据过长({data.Length})");

            if (checkCanWrite)
            {
                if (!CanWrite(ch))
                {
                    OnErrorOccurred($"{CardName}：【{cmdName}】发送失败,板卡不可写");
                    return -1;
                }
            }


            int result = 0;


            lock (_ioLock)
            {
                result = HNFS_WriteDataAny(CardIndex, 0x3000, (uint)data.Length, data);
                HNFS_WriteWordByAddr(CardIndex, 0x0060, [0xAA, 0xAA]); //写入写数完成标志
                App.Log.Debug($"向{CardName}发送数据：{data.ToHexString()}，长度：{data.Length}字节，返回值：{result}");
            }
            if (result == data.Length)
            {
                if (showLog)
                    OnInfoOccurred($"{CardName}：【{cmdName}】发送成功");
            }

            else
                ErrorString = $"{CardName}发送数据异常，预期发送{data.Length}字节，但实际发送{result}字节";
            return result;
        }


        /// <summary>
        /// 读取回令/数据
        /// </summary>
        /// <returns></returns>
        public virtual byte[] ReadData(string? readContext = null, bool showLog = false)
        {
            byte[] readData = [];
            int result;
            int canRead = 0;
            lock (Global.PciBusLock)
            {
                 canRead = CanRead;
                if (canRead <= 0)
                {
                    if (showLog)
                        OnErrorOccurred($"{CardName}：读取【{readContext}】失败");

                    return readData;
                }

                //while (canRead % 4 != 0)
                //    canRead++;
                lock (_ioLock)
                {
                    readData = new byte[canRead];
                    result = HNFS_ReadDataAny(CardIndex, 0x2000, (uint)canRead, readData);
                    HNFS_WriteWordByAddr(CardIndex, 0x0050, [0xAA, 0xAA]); //写入读数完成标志
                    App.Log.Debug($"从{CardName}读取数据：{readData.ToHexString()}，长度：{readData.Length}字节，返回值：{result}");
                }
            }
            if (result != canRead)
                ErrorString = ($"{CardName}读取数据异常，预期读取{canRead}字节，但实际读取{result}字节");
            return readData;
        }


        private Thread? monitorThread;
        private volatile bool isMonitoring = false;
        private volatile bool shouldStopMonitoring = false; // 监测线程停止信号

        private Thread? downloadThread;
        private volatile bool isDownloading = false;
        private volatile bool shouldStopDownloading = false;  // 下载线程停止信号

        public event EventHandler DataDownloadCompleted;
       
        /// <summary>
        /// 获取当前是否正在监测
        /// </summary>
        public bool IsMonitoring => isMonitoring;

        /// <summary>
        /// 获取当前是否正在下载
        /// </summary>
        public bool IsDownloading => isDownloading;


        /// <summary>
        /// 获取当前是否有任何操作在运行
        /// </summary>
        public bool IsBusy => isMonitoring || isDownloading;
        
        
        /// <summary>
        /// 获取当前运行状态描述
        /// </summary>
        public string CurrentStatus
        {
            get
            {
                if (isMonitoring) return "监测中";
                if (isDownloading) return "下载中";
                return "空闲";
            }
        }

        public bool StartDownloadData()
        {
            if (!Online)
            {
                OnErrorOccurred($"{CardName}不在线");
                return false;
            }

            if (isDownloading)
            {
                OnErrorOccurred($"数字量卡已在下载数据");
                return false;
            }
            if (isMonitoring)
            {
                OnErrorOccurred("监测正在运行中，请先停止监测再开始下载");
                return false;
            }

            shouldStopDownloading = false;
            Thread thread = new(DownloadLoop)
            {
                Priority = ThreadPriority.AboveNormal, // 高优先级确保下载不被打断
                IsBackground = true, 
                Name = $"{CardName}数据下载线程"
            };


            OnInfoOccurred($"开始读取{CardName}数据");
            
            thread.Start();
            return true;
        }


        private void DownloadLoop()
        {
            FileStream? fileOut = null;
            
            Directory.CreateDirectory($"{AppDomain.CurrentDomain.BaseDirectory}数据\\数字量卡");
            string outFileName = $"{AppDomain.CurrentDomain.BaseDirectory}数据\\数字量卡\\{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.dat";


            try
            {
                isDownloading = true;
                // 创建输出文件
                fileOut = new FileStream(outFileName, FileMode.Create, FileAccess.Write, FileShare.Read, 81920, false); // 同步IO
                BufferedStream bufferedStream = new(fileOut, 8192);
                DateTime startTime = DateTime.Now;
                long totalBytesDownloaded = 0;
                while (!shouldStopDownloading)
                {
                    try
                    {
                        byte[] dat = ReadData($"数字量卡数据", false);
                        if(dat.Length>0)
                        {
                            bufferedStream.Write(dat, 0, dat.Length);
                            totalBytesDownloaded += dat.Length;
                        }
                        else
                        {
                            // 没有数据时短暂休眠
                            try
                            {
                                Thread.Sleep(10);
                            }
                            catch (ThreadInterruptedException)
                            {
                                // 线程被中断，正常退出
                                break;
                            }
                        }

                    }
                    catch (ThreadInterruptedException)
                    {
                        // 线程被中断，正常退出
                        App.Log.Debug($"数字量卡读数线程被中断，正常退出");
                        break;
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续运行，避免因单次错误中断整个下载
                        App.Log.Debug($"数字量卡读数循环中发生错误: {ex.Message}");
                        try
                        {
                            Thread.Sleep(10); // 短暂延迟后继续
                        }
                        catch (ThreadInterruptedException)
                        {
                            // 在错误恢复期间被中断，退出
                            break;
                        }
                    }
                }
                bufferedStream.Flush();
                fileOut.Flush();
                fileOut.Close();
                fileOut = null;
                // 输出统计信息
                TimeSpan duration = DateTime.Now - startTime;
                double avgSpeed = totalBytesDownloaded / duration.TotalSeconds ; // KB/s
                OnInfoOccurred($"数字量卡读数完成 - 总读取: {totalBytesDownloaded} 字节, 平均速度: {avgSpeed:F2} B/s");
            }
            catch (ThreadInterruptedException)
            {
                // 线程被中断，正常退出
                App.Log.Debug($"数字量卡读数线程被中断，正常退出");
                OnInfoOccurred($"数字量卡读数线程被取消");
            }
            catch (Exception ex)
            {
                ErrorString = $"数字量卡读数线程发生异常:{ex.Message}";
                App.Log.Error($"数字量卡读数线程发生异常: {ex}");
            }
            finally
            {
                fileOut?.Flush();
                fileOut?.Close();
                isDownloading = false;

                try
                {
                    DataDownloadCompleted?.Invoke(this, EventArgs.Empty);
                }
                catch (Exception ex)
                {
                    App.Log.Error($"触发数字量卡下载完成事件时发生错误: {ex.Message}");
                }
            }
        }


        public void StopDownload()
        {
            shouldStopDownloading = true;

            if (downloadThread != null && downloadThread.IsAlive)
            {
                try
                {
                    // 等待最多3秒让下载线程正常退出
                    if (!downloadThread.Join(3000))
                    {
                        App.Log.Debug($"数字量卡读数线程未能在3秒内正常退出，尝试中断");

                        try
                        {
                            downloadThread.Interrupt();

                            // 再等待2秒
                            if (!downloadThread.Join(2000))
                            {
                                App.Log.Debug($"数字量卡读数线程中断后仍未退出");
                            }
                        }
                        catch (Exception ex)
                        {
                            App.Log.Error($"中断数字量卡读数线程时发生错误: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    App.Log.Error($"停止数字量卡读数线程时发生错误: {ex.Message}");
                }
                finally
                {
                    downloadThread = null;
                    
                }
            }
        }

  
        public bool StartMonitor()
        {
            if (!Online)
            {
                OnErrorOccurred($"{CardName}不在线");
                return false;
            }
            if (isMonitoring)
            {
                OnErrorOccurred("监测已在运行中");
                return false;
            }

            if (isDownloading)
            {
                OnErrorOccurred("数据下载正在运行中，请先停止下载再开始监测");
                return false;
            }

            // 重置停止信号
            shouldStopMonitoring = false;

            // 创建专用监测线程，设置高优先级
            monitorThread = new Thread(MonitorLoop)
            {
                Priority = ThreadPriority.AboveNormal, // 高优先级确保数据采集不被打断
                IsBackground = true, 
                Name = $"{CardName}_数据监测线程"
            };
            OnInfoOccurred("开始监测");
            monitorThread.Start();
            return true;

        }


        public event EventHandler<byte[]>? OnMonitorDataReceived;


        /// <summary>
        /// 监测循环
        /// </summary>
        private void MonitorLoop()
        {
            FileStream? fileOut = null;
            byte[]? writeBuffer = null;
            int writeBufferLen = 0;

            try
            {
                // 在Loop开始时设置状态
                isMonitoring = true;

                // 创建数据目录
                string dataDir = $"{AppDomain.CurrentDomain.BaseDirectory}数据\\监测数据";
                Directory.CreateDirectory(dataDir);

                // 创建输出文件
                string fileName = $"{dataDir}\\{DateTime.Now:yyyy-MM-dd-HH-mm-ss}_422.dat";
                fileOut = new FileStream(fileName, FileMode.Create, FileAccess.Write, FileShare.Read, 81920, false); // 同步IO以确保数据完整性

                // 创建写缓冲区（10MB）
                writeBuffer = new byte[10485760];
                writeBufferLen = 0;

                // 统计信息
                long totalBytesReceived = 0;
                long totalBytesWritten = 0;
                DateTime startTime = DateTime.Now;
                DateTime lastFlushTime = DateTime.Now;

                while (!shouldStopMonitoring)
                {
                    try
                    {
                        // 读取数据
                        byte[] temp = ReadData("422监测数据", false);

                        if (temp.Length > 0)
                        {
                            OnMonitorDataReceived?.Invoke(this, temp);

                            totalBytesReceived += temp.Length;

                            // 检查缓冲区是否需要刷新
                            if (writeBufferLen + temp.Length > writeBuffer.Length)
                            {
                                // 写入文件并刷新
                                fileOut.Write(writeBuffer, 0, writeBufferLen);
                                fileOut.Flush();
                                totalBytesWritten += writeBufferLen;
                                writeBufferLen = 0;
                                lastFlushTime = DateTime.Now;
                            }

                            // 复制数据到缓冲区
                            Array.Copy(temp, 0, writeBuffer, writeBufferLen, temp.Length);
                            writeBufferLen += temp.Length;
                        }
                        else
                        {
                            // 没有数据时短暂休眠，避免CPU占用过高
                            try
                            {
                                Thread.Sleep(10);
                            }
                            catch (ThreadInterruptedException)
                            {
                                // 线程被中断，正常退出
                                break;
                            }
                        }

                        // 定期强制刷新缓冲区（每秒一次）
                        if ((DateTime.Now - lastFlushTime).TotalSeconds >= 1.0 && writeBufferLen > 0)
                        {
                            fileOut.Write(writeBuffer, 0, writeBufferLen);
                            fileOut.Flush();
                            totalBytesWritten += writeBufferLen;
                            writeBufferLen = 0;
                            lastFlushTime = DateTime.Now;
                        }
                    }
                    catch (ThreadInterruptedException)
                    {
                        // 线程被中断，正常退出
                        App.Log.Debug("监测线程被中断，正常退出");
                        break;
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续运行，避免因单次错误中断整个监测
                        App.Log.Debug($"监测循环中发生错误: {ex.Message}");
                        try
                        {
                            Thread.Sleep(1); // 短暂延迟后继续
                        }
                        catch (ThreadInterruptedException)
                        {
                            // 在错误恢复期间被中断，退出
                            break;
                        }
                    }
                }

                // 写入剩余数据
                if (writeBufferLen > 0)
                {
                    fileOut.Write(writeBuffer, 0, writeBufferLen);
                    fileOut.Flush();
                    totalBytesWritten += writeBufferLen;
                }

                // 输出统计信息
                TimeSpan duration = DateTime.Now - startTime;
                double avgSpeed = totalBytesReceived / duration.TotalSeconds / 1024.0; // KB/s

                OnInfoOccurred($"监测停止 - 总接收: {totalBytesReceived} 字节, 总写入: {totalBytesWritten} 字节, 平均速度: {avgSpeed:F2} KB/s");
            }
            catch (ThreadInterruptedException)
            {
                // 线程被中断，正常退出
                App.Log.Debug("监测线程被中断，正常退出");
            }
            catch (Exception ex)
            {
                ErrorString = $"LVDS卡实时监测发生异常:{ex.Message}";
                App.Log.Error($"LVDS卡实时监测发生异常: {ex}");
            }
            finally
            {
                // 确保文件被正确关闭
                try
                {
                    if (writeBufferLen > 0 && fileOut != null && writeBuffer != null)
                    {
                        fileOut?.Write(writeBuffer, 0, writeBufferLen);
                        fileOut?.Flush();
                    }
                }
                catch { }

                fileOut?.Close();
                isMonitoring = false;
            }
        }


        /// <summary>
        /// 设置模拟通道参数
        /// </summary>
        /// <param name="ch">0:配置板卡模拟发送七室数据   2:测试台发指令（下载和复位）+装订；接收记录仪状态  3:不发送只接受无线数据  4:惯组（发送）    6:发射机  13:测量收发一体机RS-485 14:姿态发射机 15:无线帧
        /// <para>1:配置板卡模拟发送三室数据</para>
        /// <para>2:发送下载/复位/装订指令前配置通道</para>
        /// <para>3:板卡开始接受无线数据</para>
        /// <para>4:配置板卡模拟发送惯组数据</para>
        /// <para>6:配置板卡模拟发送发射机数据</para>
        /// <para>13:给测量收发一体机发送指令前配置</para>
        /// <para>14:给姿态测量发射机发送指令前配置通道</para>
        /// <para>15:配置板卡模拟发送无线数据</para>
        /// </param>
        /// <param name="cycle">发送周期</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="parity">校验</param>
        /// <param name="dataLen">单次发送数据长</param>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        public void SetSimulateChannelPara(byte ch, byte cycle, uint baudRate, byte parity, ushort dataLen)
        {
            if (ch < 0 || ch > 15)
                throw new ArgumentOutOfRangeException(nameof(ch), "通道号必须在0~15之间");
            if (!Online)
            {
                ErrorString = $"{CardName}未连接";
                return;
            }
            SendFrame_DigitalCard sendFrame_DigitalCard = new SendFrame_DigitalCard(ch, Global.FrameCounter_DigitalCard++, parity, cycle, baudRate, dataLen);
            SendData(sendFrame_DigitalCard.ToBytes(), ch, false);
        }

        private readonly ConcurrentDictionary<byte, CancellationTokenSource> _simulateTasks = new();

        public void StopSimulate(byte ch)
        {
            if (_simulateTasks.TryRemove(ch, out var cts))
            {
                cts.Cancel();
                cts.Dispose();
            }
        }
        public void StopAllSimulate()
        {
            foreach (var ch in _simulateTasks.Keys.ToList())
            {
                StopSimulate(ch);
            }
        }

        public void StartSimulate(byte ch, byte cycle, uint baudRate, byte parity, ushort dataLen)
        {
            // 若该通道已在模拟，先停止
            StopSimulate(ch);

            //配置通道
            SetSimulateChannelPara(ch, cycle, baudRate, parity, dataLen);

            //开始读数发送
            string? file = ch switch
            {
                0 => AppDomain.CurrentDomain.BaseDirectory + "\\配置文件\\模拟数据\\七室水冷泵.dat",
                1 => AppDomain.CurrentDomain.BaseDirectory + "\\配置文件\\模拟数据\\三室控制系统状态.dat",
                4 => AppDomain.CurrentDomain.BaseDirectory + "\\配置文件\\模拟数据\\惯组.dat",
                6 => AppDomain.CurrentDomain.BaseDirectory + "\\配置文件\\模拟数据\\发射机.dat",
                15 => AppDomain.CurrentDomain.BaseDirectory + "\\配置文件\\模拟数据\\122无线.dat",
                _ => null
            };




            if (file == null)
            {
                OnInfoOccurred($"通道{ch + 1}配置完成");

                if (ch == 2|| ch == 3|| ch == 7)
                {
                    StartDownloadData();
                }
                

                return;
            }
            else
            {
                if (!File.Exists(file))
                {
                    OnErrorOccurred($"{CardName}：通道{ch + 1}的模拟数据文件不存在");
                    return;
                }

                var cts = new CancellationTokenSource();
                _simulateTasks[ch] = cts;
                var token = cts.Token;

                Task.Run(() =>
                {
                    try
                    {
                        using var fs = new FileStream(file, FileMode.Open, FileAccess.Read, FileShare.Read);
                        const int bufferSize = 4 * 1024 * 1024; // 4MB
                        byte[] fileBuffer = new byte[bufferSize];
                        int bufferOffset = 0, bufferValid = 0;

                        while (!token.IsCancellationRequested)
                        {
                            // 缓冲区耗尽则重新填充
                            if (bufferOffset >= bufferValid)
                            {
                                bufferValid = fs.Read(fileBuffer, 0, bufferSize);
                                bufferOffset = 0;
                                if (bufferValid == 0)
                                {
                                    fs.Seek(0, SeekOrigin.Begin);
                                    continue;
                                }
                            }

                            int sendLen = Math.Min(8000, bufferValid - bufferOffset);
                            byte[] sendData = new byte[sendLen];
                            Array.Copy(fileBuffer, bufferOffset, sendData, 0, sendLen);

                            SendFrame_DigitalCard dataFrame = new(ch, Global.FrameCounter_DigitalCard++, sendData);


                            // 等待可写
                            while (!CanWrite(ch))
                            {
                                if (token.IsCancellationRequested) return;
                                Thread.Sleep(10);
                            }

                            SendData(dataFrame.ToBytes(), ch, false, $"通道{ch + 1}模拟数据", false);
                            bufferOffset += sendLen;
                            Thread.Sleep(10);
                            //break;// TODO:
                        }
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred($"{CardName}：通道{ch + 1}模拟发送异常 - {ex.Message}");
                    }
                }, token);
            }
        }

    }
}
