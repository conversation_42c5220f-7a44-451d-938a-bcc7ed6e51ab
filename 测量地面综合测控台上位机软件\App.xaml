﻿<Application x:Class="测量地面综合测控台上位机软件.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
             xmlns:converters="clr-namespace:测量地面综合测控台上位机软件.Converters"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             ShutdownMode="OnLastWindowClose"
             StartupUri="V/SelectProject.xaml"
             Startup="Application_Startup"
             Exit="Application_Exit"
             DispatcherUnhandledException="Application_DispatcherUnhandledException"
             >
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml"/>
                <ResourceDictionary Source="/Resource/样式/SwitchButton.xaml"/>
                <ResourceDictionary Source="/Resource/样式/LogListViewItem.xaml"/>
                <ResourceDictionary Source="/Resource/样式/ChartStyle.xaml"/>
                <ResourceDictionary Source="/Resource/样式/ProgressButtonStyle.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 添加转换器 -->
            <converters:IsFrameNodeConverter x:Key="IsFrameNodeConverter"/>
            <converters:IsDeviceNodeConverter x:Key="IsDeviceNodeConverter"/>
            <converters:IsChannelNodeConverter x:Key="IsChannelNodeConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:DataTypeConverter x:Key="DataTypeConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
