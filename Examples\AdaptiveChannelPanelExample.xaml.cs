using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace 测量地面综合测控台上位机软件.Examples
{
    /// <summary>
    /// AdaptiveChannelPanelExample.xaml 的交互逻辑
    /// </summary>
    public partial class AdaptiveChannelPanelExample : UserControl
    {
        public AdaptiveChannelPanelExample()
        {
            InitializeComponent();
            LoadSampleData();
        }

        /// <summary>
        /// 加载示例数据
        /// </summary>
        private void LoadSampleData()
        {
            // 创建示例通道数据
            var sampleChannels = new List<Channel>();
            
            for (int i = 1; i <= 20; i++)
            {
                var channel = new Channel
                {
                    ChannelName = $"CH{i:D2}",
                    ChannelDescription = $"通道{i}描述",
                    Unit1 = "V",
                    Unit2 = "°C",
                    LastDecValue = new Random().NextDouble() * 100,
                    LastHexValue = $"0x{new Random().Next(0, 255):X2}",
                    LastVolValue = new Random().NextDouble() * 5,
                    LastPhyValue = new Random().NextDouble() * 50
                };
                
                sampleChannels.Add(channel);
            }

            // 设置到自适应面板
            adaptivePanel.SetChannels(sampleChannels);
        }
    }
}
