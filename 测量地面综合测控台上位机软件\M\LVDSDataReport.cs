﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class LVDSDataReport
    {
        public string FileName { get; set; }

        public string FileSize { get; set; }

        public DateTime ReportTime { get; set; }

        public CounterChecker LVDSRawDataReport { get; set; }

        public StorageDataReport StorageDataReport { get; set; }


        public LVDSDataReport()
        { }

        public LVDSDataReport(string fileName, string fileSize, DateTime reportTime)
        {
            FileName = fileName;
            FileSize = fileSize;
            ReportTime = reportTime;
        }

    }

    public class RS422DataReport
    {
        public string FileName { get; set; }
        public string FileSize { get; set; }
        public DateTime ReportTime { get; set; }
        public CounterChecker RS422RawDataReport { get; set; }


        public FrameDataReport CCQStateReport { get; set; }
        public FrameDataReport TYReport { get; set; }
    }



    public class StorageDataReport
    {
        public string FileName { get; set; }
        public string FileSize { get; set; }
        public DateTime ReportTime { get; set; }
        public CounterChecker StorageRawDataReport { get; set; }

        public FrameDataReport CCQMNLReport { get; set; }
        public FrameDataReport WX_121Report { get; set; }
        public FrameDataReport GZReport { get; set; }
        public FrameDataReport FSJReport { get; set; }
        public FrameDataReport CCQStateReport { get; set; }
        public FrameDataReport QSSLBReport { get; set; }
        public FrameDataReport XTZTReport { get; set; }
        public FrameDataReport SSKZXTZTReport { get; set; }


        public StorageDataReport() { }

        public StorageDataReport(string fileName, string fileSize, DateTime reportTime)
        {
            FileName = fileName;
            FileSize = fileSize;
            ReportTime = reportTime;
        }
    }

    public class FrameDataReport
    {
        public string FileName { get; set; }
        public string FileSize { get; set; }
        public DateTime ReportTime { get; set; }

        public CounterChecker FrameRawDataReport { get; set; }


        public List<ChannelDataReport> ChannelDataReports { get; set; }

        public FrameDataReport()
        {

        }

        public FrameDataReport(string fileName, string fileSize, DateTime reportTime)
        {
            FileName = fileName;
            FileSize = fileSize;
            ReportTime = reportTime;
        }

    }

    public class ChannelDataReport
    {
        public string ChannelName { get; set; }

        public string NumberFormatted { get; set; } = "0.###";

        

        /// <summary>
        /// 电压量下限
        /// </summary>
        public decimal Minimum { get; set; }

        

        /// <summary>
        /// 电压量上限
        /// </summary>
        public decimal Maximum { get; set; }

        private long totalSamplesCount = 0;

        /// <summary>
        /// 总点数
        /// </summary>
        public string TotalSamplesCount { get { return $"{totalSamplesCount}"; } }



        private long samplesCountBiggerThanMaximum = 0;

        /// <summary>
        /// 低于下限的点数
        /// </summary>
        public string SamplesCountBiggerThanMaximum { get { return $"{samplesCountBiggerThanMaximum}"; } }



        private long samplesCountSmallerThanMinimum = 0;

        /// <summary>
        /// 高于上限的点数
        /// </summary>
        public string SamplesCountSmallerThanMinimum { get { return $"{samplesCountSmallerThanMinimum}"; } }

        private long samplesCountInRange = 0;

        /// <summary>
        /// 在范围内的点数
        /// </summary>
        public string SamplesCountInRange
        {
            get
            {
                samplesCountInRange = totalSamplesCount - samplesCountBiggerThanMaximum - samplesCountSmallerThanMinimum;
                return $"{samplesCountInRange}";
            }
        }

        /// <summary>
        /// 合格率
        /// </summary>
        public string PercentageInRange
        {
            get
            {
                if (totalSamplesCount == 0)
                {
                    return "/";
                }
                else
                {
                    return $"{(samplesCountInRange * 100.0 / totalSamplesCount):0.##}%";
                }
            }
        }

        private decimal actualMinValue_Vol = 0;

        /// <summary>
        /// 电压量实际最小值
        /// </summary>
        public string ActualMinValue_Vol { get { return actualMinValue_Vol.ToString(NumberFormatted); } }

        private decimal actualMaxValue_Vol = 0;

        /// <summary>
        /// 电压量实际最大值
        /// </summary>
        public string ActualMaxValue_Vol { get { return actualMaxValue_Vol.ToString(NumberFormatted); } }

        private decimal average_Vol = 0;

        /// <summary>
        /// 电压量平均值
        /// </summary>
        public string Average_Vol { get { return average_Vol.ToString(NumberFormatted); } }

        private decimal actualMinValue_Physical = 0;

        /// <summary>
        /// 物理量实际最小值
        /// </summary>
        public string ActualMinValue_Physical { get { return actualMinValue_Physical.ToString(NumberFormatted); } }

        private decimal actualMaxValue_Physical = 0;

        /// <summary>
        /// 物理量实际最大值
        /// </summary>
        public string ActualMaxValue_Physical { get { return actualMaxValue_Physical.ToString(NumberFormatted); } }

        private decimal average_Physical = 0;

        /// <summary>
        /// 物理量平均值
        /// </summary>
        public string Average_Physical { get { return average_Physical.ToString(NumberFormatted); } }

        public ChannelDataReport(string channelName, decimal minimum, decimal maximum)
        {
            ChannelName = channelName;
            Minimum = minimum;
            Maximum = maximum;
        }

        public void AddSample(decimal value_Vol, decimal value_Physical)
        {
            totalSamplesCount++;
            if (value_Vol < Minimum)
            {
                samplesCountBiggerThanMaximum++;
            }
            else if (value_Vol > Maximum)
            {
                samplesCountSmallerThanMinimum++;
            }

            if (totalSamplesCount == 1)
            {
                actualMinValue_Vol = value_Vol;
                actualMaxValue_Vol = value_Vol;
                actualMinValue_Physical = value_Physical;
                actualMaxValue_Physical = value_Physical;
            }
            else
            {
                if (value_Vol < actualMinValue_Vol)
                {
                    actualMinValue_Vol = value_Vol;
                }
                else if (value_Vol > actualMaxValue_Vol)
                {
                    actualMaxValue_Vol = value_Vol;
                }
                average_Vol = (average_Vol * (totalSamplesCount - 1) + value_Vol) / totalSamplesCount;

                if (value_Physical < actualMinValue_Physical )
                {
                    actualMinValue_Physical = value_Physical;
                }
                else if (value_Physical > actualMaxValue_Physical)
                {
                    actualMaxValue_Physical = value_Physical;
                }

                average_Physical = (average_Physical * (totalSamplesCount - 1) + value_Physical) / totalSamplesCount;
            }
        }

    }



}
