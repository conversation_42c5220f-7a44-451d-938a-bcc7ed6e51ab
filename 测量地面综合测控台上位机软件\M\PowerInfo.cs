﻿using CommunityToolkit.Mvvm.ComponentModel;
using DevExpress.Xpf.Core.ConditionalFormatting.Native;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.ApplicationModel.Appointments;

namespace 测量地面综合测控台上位机软件
{

    /// <summary>
    /// 电源信息
    /// </summary>
    public partial class PowerInfo : ObservableObject
    {
        /// <summary>
        /// 电压K
        /// </summary>
        [ObservableProperty]
        private decimal voltage28K = 1;

        /// <summary>
        /// 电压B
        /// </summary>
        [ObservableProperty]
        private decimal voltage28B = 0;

        /// <summary>
        /// 电流K
        /// </summary>
        [ObservableProperty]
        private decimal current28K = 1;

        /// <summary>
        /// 电流B
        /// </summary>
        [ObservableProperty]
        private decimal current28B = 0;



        private decimal voltage28 = decimal.Zero;

        /// <summary>
        /// 电压值
        /// </summary>
        public decimal Voltage28
        {
            get => voltage28;
            private set
            {
                if (value != voltage28)
                {
                    voltage28 = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal current28 = decimal.Zero;
        /// <summary>
        /// 电流值
        /// </summary>
        public decimal Current28
        {
            get => current28;
            private set
            {
                if (value != current28)
                {
                    current28 = value;
                    OnPropertyChanged();
                }
            }
        }


        private int voltage28Raw = 0;

        /// <summary>
        /// 电压原始值
        /// </summary>
        public int Voltage28Raw
        {
            set
            {
                voltage28Raw = value;
                Voltage28 = value * Voltage28K + Voltage28B;
            }
        }

        private int current28Raw = 0;

        /// <summary>
        /// 电流原始值
        /// </summary>
        public int Current28Raw
        {
            set 
            {
                current28Raw = value;
                Current28 = value * Current28K + Current28B;
            }
        }

        /// <summary>
        /// 电压K
        /// </summary>
        [ObservableProperty]
        private decimal voltage5K = 1;

        /// <summary>
        /// 电压B
        /// </summary>
        [ObservableProperty]
        private decimal voltage5B = 0;

        /// <summary>
        /// 电流K
        /// </summary>
        [ObservableProperty]
        private decimal current5K = 1;

        /// <summary>
        /// 电流B
        /// </summary>
        [ObservableProperty]
        private decimal current5B = 0;



        private decimal voltage5 = decimal.Zero;

        /// <summary>
        /// 电压值
        /// </summary>
        public decimal Voltage5
        {
            get => voltage5;
            private set
            {
                if (value != voltage5)
                {
                    voltage5 = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal current5 = decimal.Zero;
        /// <summary>
        /// 电流值
        /// </summary>
        public decimal Current5
        {
            get => current5;
            private set
            {
                if (value != current5)
                {
                    current5 = value;
                    OnPropertyChanged();
                }
            }
        }


        private int voltage5Raw = 0;

        /// <summary>
        /// 电压原始值
        /// </summary>
        public int Voltage5Raw
        {
            set
            {
                voltage5Raw = value;
                Voltage5 = value * Voltage5K + Voltage5B;
            }
        }

        private int current5Raw = 0;

        /// <summary>
        /// 电流原始值
        /// </summary>
        public int Current5Raw
        {
            set
            {
                current5Raw = value;
                Current5 = value * Current5K + Current5B;
            }
        }


        // 只要K或B变化就重新计算
        partial void OnVoltage28KChanged(decimal value) => Voltage28 = voltage28Raw * Voltage28K + Voltage28B;
        partial void OnVoltage28BChanged(decimal value) => Voltage28 = voltage28Raw * Voltage28K + Voltage28B;
        partial void OnCurrent28KChanged(decimal value) => Current28 = current28Raw * Current28K + Current28B;
        partial void OnCurrent28BChanged(decimal value) => Current28 = current28Raw * Current28K + Current28B;

        partial void OnVoltage5KChanged(decimal value) => Voltage5 = voltage5Raw * Voltage5K + Voltage5B;
        partial void OnVoltage5BChanged(decimal value) => Voltage5 = voltage5Raw * Voltage5K + Voltage5B;
        partial void OnCurrent5KChanged(decimal value) => Current5 = current5Raw * Current5K + Current5B;
        partial void OnCurrent5BChanged(decimal value) => Current5 = current5Raw * Current5K + Current5B;

    }
}
