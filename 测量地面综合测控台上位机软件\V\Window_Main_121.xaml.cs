﻿
using Microsoft.Extensions.DependencyInjection;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
namespace 测量地面综合测控台上位机软件
{

    public partial class Window_Main_121 : Window
    {
        public Window_Main_121()
        {
            InitializeComponent();


            var vm = App.ServiceProvider.GetRequiredService<MainWindowViewModel>();
            this.DataContext = vm;

            vm.LogsUpdated += () =>
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    if (LogList.Items.Count > 0)
                    {
                        if (LogList.SelectedIndex == -1)
                        {
                            LogList.ScrollIntoView(LogList.Items[LogList.Items.Count - 1]);
                        }
                        else if (LogList.SelectedIndex == LogList.Items.Count - 2)
                        {
                            LogList.ScrollIntoView(LogList.Items[LogList.Items.Count - 1]);
                            LogList.SelectedIndex = LogList.Items.Count - 1;
                        }
                    }
                });
            };


            vm.AddLog(LogHelper.LogLevel.INFO, "选择BYD68-121");
            Global.Init();

            vm.DigitalCard.StartMonitor();

            //vm.DigitalCard.DataDownloadCompleted += DigitalCard_DataDownloadCompleted;
        }

        

        private void LogList_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            GridView gv = (LogList.View as GridView);
            gv.Columns[2].Width = LogList.ActualWidth - gv.Columns[0].ActualWidth - gv.Columns[1].ActualWidth - 36;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Window_FrameConfig window_FrameConfig = new()
            {
                Owner = this
            };
            window_FrameConfig.Show();
        }

        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            //App.ServiceProvider.GetRequiredService<MainWindowViewModel>().Analyse(true);
            //App.ServiceProvider.GetRequiredService<MainWindowViewModel>().Simulator(true);
            //btnStartSimulator.Visibility = Visibility.Collapsed;
            //btnStopSimulator.Visibility = Visibility.Visible;
        }


        private void SimpleButton_Click(object sender, RoutedEventArgs e)
        {

            if (Global.Card_LVDS?.StartMonitor() == true)
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().Analyse(true);
                this.btnStartMonitor.Visibility = Visibility.Collapsed;
                btnStopMonitor.Visibility = Visibility.Visible;
            }
        }

        private void SimpleButton_Click_1(object sender, RoutedEventArgs e)
        {
            Global.Card_LVDS?.StopDownloadOrMonitor();
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().Analyse(false);
            btnStopMonitor.Visibility = Visibility.Collapsed;
            btnStartMonitor.Visibility = Visibility.Visible;
        }

        private void SimpleButton_Click_2(object sender, RoutedEventArgs e)
        {
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().Simulator(false);
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().Analyse(false);
            btnStopSimulator.Visibility = Visibility.Collapsed;
            btnStartSimulator.Visibility = Visibility.Visible;
        }



        private void SimpleButton_Click_4(object sender, RoutedEventArgs e)
        {
            WindowDataProcess windowDataProcess = new() { Owner = this };
            windowDataProcess.ShowDialog();
        }

        private void btnDownload_Click(object sender, RoutedEventArgs e)
        {
            WindowDownload windowDownload = new()
            {
                Owner = this
            };
            windowDownload.ShowDialog();
        }

        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            Window_test window_Test = new() { Owner = this };
            window_Test.Show();
        }

        
        private Window1 _window1Instance;

        private void SimpleButton_Click_3(object sender, RoutedEventArgs e)
        {
            if (_window1Instance == null || !_window1Instance.IsLoaded)
            {
                _window1Instance = new Window1 { Owner = this };
                _window1Instance.Closed += (s, args) => _window1Instance = null;
                _window1Instance.Show();
            }
            else
            {
                _window1Instance.Activate();
            }
        }
        private void DigitalCard_DataDownloadCompleted(object? sender, System.EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                btnStartTest.Content = "开始测试";
                chkQS.IsEnabled = true;
                chkSS.IsEnabled = true;
                //chkJLYZT.IsEnabled = true;
                //chkWX.IsEnabled = true;
                chkGZ.IsEnabled = true;
                chkFSJ.IsEnabled = true;
                //chkTB422.IsEnabled = true;
            });
        }
        private void Button_Click_3(object sender, RoutedEventArgs e)
        {
            if (btnStartTest.Content.ToString() == "开始测试")
            {
                bool[] state = [chkQS.IsChecked == true, chkSS.IsChecked == true, false, false, chkGZ.IsChecked == true, false, chkFSJ.IsChecked == true, false];
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().StartTest(state);
                btnStartTest.Content = "停止测试";

                chkQS.IsEnabled = false;
                chkSS.IsEnabled = false;
                //chkJLYZT.IsEnabled = false;
                //chkWX.IsEnabled = false;
                chkGZ.IsEnabled = false;
                chkFSJ.IsEnabled = false;
                //chkTB422.IsEnabled = false;
            }
            else
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().StopAllTest();
                
            }
        }

        private void SimpleButton_Click_5(object sender, RoutedEventArgs e)
        {

        }
    }
}