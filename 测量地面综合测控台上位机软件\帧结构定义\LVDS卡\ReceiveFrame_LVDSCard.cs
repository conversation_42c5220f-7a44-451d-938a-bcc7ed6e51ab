﻿using DevExpress.XtraRichEdit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace 测量地面综合测控台上位机软件
{
    class ReceiveFrame_LVDSCard
    {
        /// <summary>
        /// 帧头
        /// </summary>
        public static readonly byte[] FrameHead = [0xEB, 0x90];

        /// <summary>
        /// 帧尾
        /// </summary>
        public static readonly byte[] FrameTail = [0x14, 0x6F];

        /// <summary>
        /// 帧长
        /// </summary>
        public ushort FrameLength { get; }

        /// <summary>
        /// 帧计数
        /// </summary>
        public ushort FrameCounter { get; }

        /// <summary>
        /// 时间
        /// </summary>
        public ushort TimeStamp { get; }

        /// <summary>
        /// 数据区
        /// </summary>
        public byte[]? Data { get; } = [];

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool Valid { get; }

        /// <summary>
        /// 从数据中解析出有效帧数据
        /// </summary>
        /// <param name="data">数据</param>
        public ReceiveFrame_LVDSCard(byte[] data)
        {
            Valid = false;

            byte[]? frame = null;

            if (data == null || data.Length < 11)
            {
                //throw new ArgumentException("数据长度不足，无法解析帧");
                System.Diagnostics.Trace.Assert(data != null && data.Length >= 11, "数据长度不足，无法解析帧");
                return;
            }

            for (int i = 0; i < data.Length - 3; i++)
            {
                // 找到帧头
                if (data[i] == FrameHead[0] && data[i + 1] == FrameHead[1])
                {
                    ushort fLen = (ushort)((data[i + 2] << 8) | data[i + 3]);

                    if (i + fLen <= data.Length)
                    {
                        // 找到帧尾
                        if (data[i + fLen - 2] == FrameTail[0] && data[i + fLen - 1] == FrameTail[1])
                        {
                            frame = data.BlockCopy(i, fLen);
                            FrameLength = fLen;
                            break;
                        }
                    }
                }
            }

            if (frame == null || frame.Length < 11)
            {
                System.Diagnostics.Trace.TraceWarning("LVDS卡数据未找到有效帧");
                return;
            }

            FrameCounter= (ushort)((frame[5] << 8) | frame[6]);
            TimeStamp = (ushort)((frame[7] << 8) | frame[8]);
            Data = frame.BlockCopy(9, FrameLength - 11);

            Valid = true;
        }

        public override string ToString()
        {
            return $"是否有效:{Valid}  帧长:{FrameLength}  帧计数:{FrameCounter}  计时时间:{TimeStamp} ms  数据:{Data?.ToHexString()}";
        }
    }
}
