﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using DevExpress.Xpf.LayoutControl;
using CommunityToolkit.Mvvm.ComponentModel;
using Orientation = System.Windows.Controls.Orientation;
using HorizontalAlignment = System.Windows.HorizontalAlignment;
using Binding = System.Windows.Data.Binding;
using DevExpress.Xpf.Docking;
using LayoutGroup = DevExpress.Xpf.LayoutControl.LayoutGroup;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 通道布局视图模型，用于自动生成LayoutGroup中的TextBlock
    /// </summary>
    public partial class ChannelLayoutViewModel : ObservableObject
    {
        /// <summary>
        /// 通道列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<Channel> channels = new();

        /// <summary>
        /// 绑定路径配置
        /// </summary>
        [ObservableProperty]
        private string valueBindingPath = "LastDecValue";


        /// <summary>
        /// 通道列数
        /// </summary>
        [ObservableProperty]
        private int channelColumns = 1;

        /// <summary>
        /// 是否显示通道名称
        /// </summary>
        [ObservableProperty]
        private bool showChannelName = false;

        /// <summary>
        /// 是否显示通道描述
        /// </summary>
        [ObservableProperty]
        private bool showChannelDescription = true;

        /// <summary>
        /// 数值格式化字符串
        /// </summary>
        [ObservableProperty]
        private string valueFormat = "";


        /// <summary>
        /// 行高
        /// </summary>
        [ObservableProperty]
        private double rowHeight = 25;

        /// <summary>
        /// 边距
        /// </summary>
        [ObservableProperty]
        private Thickness itemMargin = new Thickness(5);



        /// <summary>
        /// 构造函数
        /// </summary>
        public ChannelLayoutViewModel()
        {
            // 监听属性变化，自动更新布局
            PropertyChanged += OnPropertyChanged;
        }


        /// <summary>
        /// 设置通道列表
        /// </summary>
        /// <param name="channelList">通道列表</param>
        public void SetChannels(IEnumerable<Channel>? channelList)
        {
            Channels.Clear();
            if (channelList != null)
            {
                foreach (var channel in channelList.Where(c => c.Monitor))
                {
                    Channels.Add(channel);
                }
            }
        }

        /// <summary>
        /// 为指定的LayoutGroup生成UI元素
        /// </summary>
        /// <param name="layoutGroup">目标LayoutGroup</param>
        public void GenerateLayout(LayoutGroup layoutGroup)
        {
            if (layoutGroup == null) return;

            // 清除现有内容
            layoutGroup.Children.Clear();

            if (!Channels.Any())
                return;

            ScrollViewer scrollViewer = new ScrollViewer();


            // 创建Grid容器
            Grid grid = new Grid
            {
                Name = $"grid_{Guid.NewGuid():N}",
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch,
            };

            scrollViewer.Content = grid;

            // 设置Grid的列定义
            SetupGridColumns(grid);

            // 设置Grid的行定义
            SetupGridRows(grid);

            // 添加通道控件
            AddChannelControls(grid);

            // 将Grid添加到stackPanel
            layoutGroup.Children.Add(scrollViewer);
        }

        /// <summary>
        /// 设置Grid的列定义
        /// </summary>
        private void SetupGridColumns(Grid grid)
        {
            grid.ColumnDefinitions.Clear();

            // 根据通道列数设置列定义，每个通道占用三列（标签+值+单位）
            int totalColumns = ChannelColumns * 3;

            for (int i = 0; i < totalColumns; i++)
            {
                // 值列使用Star宽度，标签列和单位列使用Auto宽度
                if (i % 3 == 1) // 值列（每组的第2列）
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100.0 / ChannelColumns, GridUnitType.Star) });
                else // 标签列和单位列
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            }
        }

        /// <summary>
        /// 设置Grid的行定义
        /// </summary>
        private void SetupGridRows(Grid grid)
        {
            grid.RowDefinitions.Clear();

            // 计算需要的行数
            int totalRows = (int)Math.Ceiling((double)Channels.Count / ChannelColumns);

            for (int i = 0; i < totalRows; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(RowHeight) });
            }
        }

        /// <summary>
        /// 添加通道控件到Grid
        /// </summary>
        private void AddChannelControls(Grid grid)
        {
            for (int i = 0; i < Channels.Count; i++)
            {
                var channel = Channels[i];

                // 计算位置
                int row = i / ChannelColumns;
                int colIndex = i % ChannelColumns;
                int labelCol = colIndex * 3;
                int valueCol = colIndex * 3 + 1;
                int unitCol = colIndex * 3 + 2;

                // 创建标签TextBlock
                var labelTextBlock = CreateLabelTextBlock(channel);
                Grid.SetRow(labelTextBlock, row);
                Grid.SetColumn(labelTextBlock, labelCol);
                grid.Children.Add(labelTextBlock);

                // 创建值TextBlock
                var valueTextBlock = CreateValueTextBlock(channel);
                Grid.SetRow(valueTextBlock, row);
                Grid.SetColumn(valueTextBlock, valueCol);
                grid.Children.Add(valueTextBlock);

                // 创建单位TextBlock
                var unitTextBlock = CreateUnitTextBlock(channel);
                Grid.SetRow(unitTextBlock, row);
                Grid.SetColumn(unitTextBlock, unitCol);
                grid.Children.Add(unitTextBlock);


            }
        }

        public void AddChannelItem(LayoutGroup layoutGroup)
        {
            if (layoutGroup == null) return;

            // 清除现有内容
            layoutGroup.Children.Clear();

            if (!Channels.Any())
                return;

            for (int i = 0; i < Channels.Count; i++)
            {
                var channel = Channels[i];

                LayoutControlItem item = new LayoutControlItem();

                var binding = new Binding("ChannelDescription")
                { Source = channel };


                item.SetBinding(LayoutControlItem.CaptionProperty, binding);



                var bingding2 = new Binding(ValueBindingPath)
                {
                    Source = channel,
                    Mode = BindingMode.OneWay,
                    StringFormat = ValueFormat
                };


                item.Content = CreateValueTextBlock(channel);




                layoutGroup.Children.Add(item);

            }


        }

        /// <summary>
        /// 创建标签TextBlock
        /// </summary>
        private TextBlock CreateLabelTextBlock(Channel channel)
        {
            var textBlock = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Stretch,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                Margin = ItemMargin,
                TextTrimming = TextTrimming.CharacterEllipsis,

                TextWrapping = TextWrapping.Wrap
            };

            // 设置显示文本
            string displayText = ShowChannelDescription && !string.IsNullOrWhiteSpace(channel.ChannelDescription)
                ? channel.ChannelDescription
                : channel.ChannelName;

            textBlock.Text = displayText + ":";

            return textBlock;
        }

        /// <summary>
        /// 创建值TextBlock
        /// </summary>
        private TextBlock CreateValueTextBlock(Channel channel)
        {
            var textBlock = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Stretch,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                Margin = ItemMargin,
                FontWeight = FontWeights.Bold,
                TextWrapping = TextWrapping.Wrap,
                TextAlignment = TextAlignment.Right
            };

            // 创建绑定
            var binding = new Binding(ValueBindingPath)
            {
                Source = channel,
                Mode = BindingMode.OneWay,
                StringFormat = ValueFormat
            };

            textBlock.SetBinding(TextBlock.TextProperty, binding);

            return textBlock;
        }

        /// <summary>
        /// 创建单位TextBlock
        /// </summary>
        private TextBlock CreateUnitTextBlock(Channel channel)
        {
            var textBlock = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = ItemMargin,
                TextTrimming = TextTrimming.CharacterEllipsis
            };

            // 根据valueBindingPath决定显示的单位
            textBlock.Text = ValueBindingPath switch
            {
                "LastVolValue" => channel.Unit1 ?? "",
                "LastPhyValue" => channel.Unit2 ?? "",
                _ => ""
            };

            return textBlock;
        }



        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // 当关键属性变化时，可以触发布局更新事件
            if (e.PropertyName == nameof(ValueBindingPath) ||
                e.PropertyName == nameof(ShowChannelName) ||
                e.PropertyName == nameof(ShowChannelDescription) ||
                e.PropertyName == nameof(ValueFormat) ||
                e.PropertyName == nameof(ChannelColumns) ||
                e.PropertyName == nameof(RowHeight)
                )
            {
                LayoutChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// 布局变化事件
        /// </summary>
        public event EventHandler LayoutChanged;
    }
}
