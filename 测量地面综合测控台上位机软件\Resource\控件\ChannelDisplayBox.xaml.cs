﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using UserControl = System.Windows.Controls.UserControl;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// ChannelDisplayBox.xaml 的交互逻辑
    /// </summary>
    public partial class ChannelDisplayBox : UserControl
    {
        public static readonly DependencyProperty ChannelNameProperty =
        DependencyProperty.Register("ChannelName", typeof(string), typeof(ChannelDisplayBox), new FrameworkPropertyMetadata("通道名称", FrameworkPropertyMetadataOptions.AffectsRender));
        
        public static readonly DependencyProperty ChannelValueProperty =
        DependencyProperty.Register("ChannelValue", typeof(string), typeof(ChannelDisplayBox), new FrameworkPropertyMetadata("通道的值", FrameworkPropertyMetadataOptions.AffectsRender));

        public string ChannelName
        {
            get { return (string)GetValue(ChannelNameProperty); }
            set { SetValue(ChannelNameProperty, value); }
        }

        public string ChannelValue
        {
            get { return (string)GetValue(ChannelValueProperty); }
            set { SetValue(ChannelValueProperty, value); }
        }

        public ChannelDisplayBox()
        {
            InitializeComponent();
        }
    }
}
