using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using 测量地面综合测控台上位机软件.VM;

namespace 测量地面综合测控台上位机软件.Converters
{
    /// <summary>
    /// 判断是否为Frame节点的转换器
    /// </summary>
    public class IsFrameNodeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is FrameNodeViewModel;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 判断是否为Device节点的转换器
    /// </summary>
    public class IsDeviceNodeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is DeviceNodeViewModel;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 判断是否为Channel节点的转换器
    /// </summary>
    public class IsChannelNodeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is ChannelNodeViewModel;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值取反转可见性转换器
    /// </summary>
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility != Visibility.Visible;
            }
            return false;
        }
    }

    /// <summary>
    /// 数据类型枚举转换器
    /// </summary>
    public class DataTypeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Channel.EnumDataType dataType)
            {
                return dataType.ToString();
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ComboBoxItem item && item.Tag is string tagValue)
            {
                if (Enum.TryParse<Channel.EnumDataType>(tagValue, out var result))
                {
                    return result;
                }
            }
            else if (value is string stringValue)
            {
                if (Enum.TryParse<Channel.EnumDataType>(stringValue, out var result))
                {
                    return result;
                }
            }
            return Channel.EnumDataType.有符号整数;
        }
    }

    /// <summary>
    /// 多值布尔逆转换器 - 当所有值都为false时返回Visible
    /// </summary>
    public class MultiBooleanToVisibilityConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length == 0)
                return Visibility.Visible;

            // 如果任何一个值为true，则返回Collapsed
            foreach (var value in values)
            {
                if (value is bool boolValue && boolValue)
                    return Visibility.Collapsed;
            }

            // 所有值都为false，返回Visible
            return Visibility.Visible;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 小数输入转换器 - 支持灵活的小数输入格式
    /// </summary>
    public class DecimalInputConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                // 显示时保留有效小数位，去除多余的零
                return decimalValue.ToString("G29", CultureInfo.InvariantCulture);
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                // 尝试解析输入的字符串为decimal
                if (string.IsNullOrWhiteSpace(stringValue))
                {
                    return 0m; // 空值默认为0
                }

                // 支持多种小数格式
                if (decimal.TryParse(stringValue, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal result))
                {
                    return result;
                }

                // 如果解析失败，尝试使用当前文化设置
                if (decimal.TryParse(stringValue, NumberStyles.Float, CultureInfo.CurrentCulture, out result))
                {
                    return result;
                }
            }

            return DependencyProperty.UnsetValue; // 解析失败，保持原值
        }
    }
}
