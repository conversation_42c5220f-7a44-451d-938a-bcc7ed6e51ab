﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace 测量地面综合测控台上位机软件.VM
{
    public partial class FrameManagementViewModel : ObservableObject
    {
        /// <summary>
        /// 所有Frame的集合
        /// </summary>
        public ObservableCollection<FrameNodeViewModel> Frames { get; } = new();

        /// <summary>
        /// 当前选中的节点
        /// </summary>
        [ObservableProperty]
        private TreeNodeViewModel? selectedNode;

        [ObservableProperty]
        private TreeNodeViewModel? selectedDeviceNode;

        [ObservableProperty]
        private TreeNodeViewModel? selectedFrameNode;


        /// <summary>
        /// 当前选中的Channel（用于属性编辑）
        /// </summary>
        [ObservableProperty]
        private Channel? selectedChannel;

        /// <summary>
        /// 当前选中的Device（用于属性编辑）
        /// </summary>
        [ObservableProperty]
        private Device? selectedDevice;

        /// <summary>
        /// 是否显示Channel属性编辑面板
        /// </summary>
        [ObservableProperty]
        private bool isChannelPropertyPanelVisible;

        /// <summary>
        /// 是否显示Device属性编辑面板
        /// </summary>
        [ObservableProperty]
        private bool isDevicePropertyPanelVisible;

        /// <summary>
        /// 搜索关键字
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        public FrameManagementViewModel()
        {
            LoadFrames();
            
            // 监听搜索文本变化
            PropertyChanged += OnSearchTextChanged;
        }

     



        /// <summary>
        /// 加载所有Frame数据
        /// </summary>
        private void LoadFrames()
        {
            Frames.Clear();

            // 从Global获取所有Frame
            var allFrames = new[]
            {
                Global.Frame_CCQMNL,    // 存储器模拟量
                Global.Frame_CCQState,  // 存储器状态
                Global.Frame_FSJ,       // 发射机
                Global.Frame_GZ,        // 惯组
                Global.Frame_QSSLB,     // 七室水冷泵
                Global.Frame_SSKZXTZT,  // 三室控制系统状态
                Global.Frame_TY,        // 体遥
                Global.Frame_XTZT       // 系统状态
            };

            foreach (var frame in allFrames.Where(f => f != null))
            {
                // 根据Frame.CurrentDeviceName设置设备的当前状态
                SyncDeviceCurrentStatus(frame!);

                var frameNode = new FrameNodeViewModel(frame!);
                Frames.Add(frameNode);
            }
        }

        /// <summary>
        /// 同步设备的当前状态与Frame.CurrentDeviceName
        /// </summary>
        private void SyncDeviceCurrentStatus(Frame frame)
        {
            // 如果帧中只有一个设备，该设备必须为当前设备
            if (frame.Devices.Count == 1)
            {
                var singleDevice = frame.Devices[0];
                singleDevice.IsCurrent = true;
                frame.CurrentDeviceName = singleDevice.DeviceName;
                return;
            }

            // 多设备情况下，根据Frame.CurrentDeviceName设置当前设备
            foreach (var device in frame.Devices)
            {
                // 设备名称与Frame.CurrentDeviceName相同的为当前设备
                device.IsCurrent = device.DeviceName == frame.CurrentDeviceName;
            }
        }

        /// <summary>
        /// 搜索文本变化处理
        /// </summary>
        private void OnSearchTextChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SearchText))
            {
                ApplyFilter();
            }
        }

        /// <summary>
        /// 应用搜索过滤
        /// </summary>
        private void ApplyFilter()
        {
            foreach (var frame in Frames)
            {
                frame.ApplyFilter(SearchText);
            }
        }

        /// <summary>
        /// 添加Device命令
        /// </summary>
        [RelayCommand]
        private void AddDevice()
        {
            if (SelectedFrameNode is FrameNodeViewModel frameNode)
            {
                var deviceName = $"新设备_{DateTime.Now:HHmmss}";
                var newDevice = new Device(frameNode.Frame, deviceName, frameNode.Frame.Devices[0].Channels);
                frameNode.Frame.Devices.Add(newDevice);
                
                var deviceNode = new DeviceNodeViewModel(newDevice, frameNode);
                frameNode.Devices.Add(deviceNode);

                // 刷新所有设备的可用性状态
                foreach (var device in frameNode.Frame.Devices)
                {
                    device.RefreshAvailability();
                }

                // 选中新添加的设备
                //SelectedNode = deviceNode;
            }
        }

        /// <summary>
        /// 删除Device命令
        /// </summary>
        [RelayCommand]
        private void DeleteDevice()
        {
            if (SelectedDeviceNode is DeviceNodeViewModel deviceNode)
            {
                // 检查是否为帧中唯一的设备
                if (deviceNode.Parent.Frame.Devices.Count <= 1)
                {
                    MessageBox.Show(
                        "无法删除设备！\n每个帧至少需要保留一个设备。",
                        "删除失败",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"确定要删除设备 '{deviceNode.Device.DeviceName}' 吗？\n这将同时删除该设备下的所有通道。",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var frame = deviceNode.Parent.Frame;

                    // 如果删除的是当前设备，需要设置新的当前设备
                    if (deviceNode.Device.IsCurrent && frame.Devices.Count > 1)
                    {
                        // 找到第一个不是当前要删除的设备，设为当前设备
                        var newCurrentDevice = frame.Devices.FirstOrDefault(d => d != deviceNode.Device);
                        if (newCurrentDevice != null)
                        {
                            frame.CurrentDeviceName = newCurrentDevice.DeviceName;
                            newCurrentDevice.IsCurrent = true;
                        }
                    }

                    // 从Frame中移除
                    frame.Devices.Remove(deviceNode.Device);
                    // 从UI中移除
                    deviceNode.Parent.Devices.Remove(deviceNode);

                    // 刷新显示和可用性状态
                    foreach (var devNode in deviceNode.Parent.Devices)
                    {
                        devNode.RefreshDisplayName();
                    }

                    // 刷新剩余设备的可用性状态
                    foreach (var device in frame.Devices)
                    {
                        device.RefreshAvailability();
                    }

                    SelectedNode = deviceNode.Parent;
                }
            }
        }

        /// <summary>
        /// 添加Channel命令
        /// </summary>
        [RelayCommand]
        private void AddChannel()
        {
            if (SelectedDeviceNode is DeviceNodeViewModel deviceNode)
            {
                var channelName = $"新通道_{DateTime.Now:HHmmss}";
                var newChannel = new Channel(deviceNode.Device)
                {
                    ChannelName = channelName,
                    ChannelDescription = "新添加的通道"
                };
                
                deviceNode.Device.Channels.Add(newChannel);
                
                var channelNode = new ChannelNodeViewModel(newChannel, deviceNode);
                deviceNode.Channels.Add(channelNode);
                
                // 选中新添加的通道
                SelectedNode = channelNode;
                SelectedChannel = newChannel;
                IsChannelPropertyPanelVisible = true;
            }
        }

        /// <summary>
        /// 删除Channel命令
        /// </summary>
        [RelayCommand]
        private void DeleteChannel()
        {
            if (SelectedNode is ChannelNodeViewModel channelNode)
            {
                var result = MessageBox.Show(
                    $"确定要删除通道 '{channelNode.Channel.ChannelName}' 吗？",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // 从Device中移除
                    channelNode.Parent.Device.Channels.Remove(channelNode.Channel);
                    // 从UI中移除
                    channelNode.Parent.Channels.Remove(channelNode);
                    
                    SelectedNode = channelNode.Parent;
                    SelectedChannel = null;
                    IsChannelPropertyPanelVisible = false;
                }
            }
        }

        /// <summary>
        /// 节点选择变化处理
        /// </summary>
        partial void OnSelectedNodeChanged(TreeNodeViewModel? value)
        {
            // 重置所有面板状态
            SelectedChannel = null;
            SelectedDevice = null;
            IsChannelPropertyPanelVisible = false;
            IsDevicePropertyPanelVisible = false;
            
            if (value is ChannelNodeViewModel channelNode)
            {
                SelectedChannel = channelNode.Channel;
                IsChannelPropertyPanelVisible = true;
                SelectedDeviceNode = channelNode.Parent;
                SelectedFrameNode = channelNode.Parent.Parent;
            }
            else if (value is DeviceNodeViewModel deviceNode)
            {
                SelectedDeviceNode = deviceNode;
                SelectedFrameNode = deviceNode.Parent;
                SelectedDevice = deviceNode.Device;
                IsDevicePropertyPanelVisible = true;
            }
            else if (value is FrameNodeViewModel frameNode)
            {
                SelectedDeviceNode = null;
                SelectedFrameNode = frameNode;
            }
        }

        /// <summary>
        /// 保存所有更改
        /// </summary>
        [RelayCommand]
        private void SaveChanges()
        {
            try
            {
                foreach (var frameNode in Frames)
                {
                    var xmlPath = $"配置文件/{frameNode.Frame.FrameName}.xml";
                    frameNode.Frame.SaveFrameStructureToXml(xmlPath);
                }
                
                MessageBox.Show("保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        [RelayCommand]
        private void RefreshData()
        {
            LoadFrames();
            SelectedNode = null;
            SelectedChannel = null;
            SelectedDevice = null;
            IsChannelPropertyPanelVisible = false;
            IsDevicePropertyPanelVisible = false;
        }

        /// <summary>
        /// 展开所有节点
        /// </summary>
        [RelayCommand]
        private void ExpandAll()
        {
            foreach (var frame in Frames)
            {
                frame.IsExpanded = true;
                foreach (var device in frame.Devices)
                {
                    device.IsExpanded = true;
                }
            }
        }

        /// <summary>
        /// 折叠所有节点
        /// </summary>
        [RelayCommand]
        private void CollapseAll()
        {
            foreach (var frame in Frames)
            {
                frame.IsExpanded = false;
                foreach (var device in frame.Devices)
                {
                    device.IsExpanded = false;
                }
            }
        }

        /// <summary>
        /// 设置当前设备命令
        /// </summary>
        [RelayCommand]
        private void SetCurrentDevice()
        {
            if (SelectedDevice != null)
            {
                // 找到选中设备所属的Frame
                var selectedFrame = FindFrameByDevice(SelectedDevice);
                if (selectedFrame != null)
                {
                    // 更新Frame.CurrentDeviceName
                    selectedFrame.CurrentDeviceName = SelectedDevice.DeviceName;

                    // 同步该Frame下所有设备的当前状态
                    SyncDeviceCurrentStatus(selectedFrame);

                    // 刷新显示
                    var frameNode = Frames.FirstOrDefault(f => f.Frame == selectedFrame);
                    if (frameNode != null)
                    {
                        foreach (var deviceNode in frameNode.Devices)
                        {
                            deviceNode.RefreshDisplayName();
                        }
                    }

                    MessageBox.Show($"已将设备 '{SelectedDevice.DeviceName}' 设为当前设备", "提示",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// 取消当前设备命令
        /// </summary>
        [RelayCommand]
        private void ClearCurrentDevice()
        {
            if (SelectedDevice != null && SelectedDevice.IsCurrent)
            {
                // 检查是否可以取消当前状态
                if (!SelectedDevice.CanClearCurrent)
                {
                    MessageBox.Show(
                        "无法取消当前设备状态！\n当帧中只有一个设备时，该设备必须为当前设备。",
                        "操作失败",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return;
                }

                // 找到选中设备所属的Frame
                var selectedFrame = FindFrameByDevice(SelectedDevice);
                if (selectedFrame != null)
                {
                    // 清空Frame.CurrentDeviceName
                    selectedFrame.CurrentDeviceName = string.Empty;

                    // 同步该Frame下所有设备的当前状态
                    SyncDeviceCurrentStatus(selectedFrame);

                    // 刷新显示
                    var frameNode = Frames.FirstOrDefault(f => f.Frame == selectedFrame);
                    if (frameNode != null)
                    {
                        foreach (var deviceNode in frameNode.Devices)
                        {
                            deviceNode.RefreshDisplayName();
                        }
                    }

                    MessageBox.Show($"已取消设备 '{SelectedDevice.DeviceName}' 的当前状态", "提示",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// 根据设备查找所属的Frame
        /// </summary>
        private Frame? FindFrameByDevice(Device device)
        {
            foreach (var frameNode in Frames)
            {
                if (frameNode.Frame.Devices.Contains(device))
                {
                    return frameNode.Frame;
                }
            }
            return null;
        }
    }
}
