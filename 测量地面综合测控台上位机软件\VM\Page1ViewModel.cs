using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace 测量地面综合测控台上位机软件.VM
{
    /// <summary>
    /// 字节数据项，表示数据帧中的一个字节
    /// </summary>
    public partial class ByteDataItem : ObservableObject
    {
        [ObservableProperty]
        private int position; // 字节位置 (0-7499)

        [ObservableProperty]
        private byte value; // 字节值 (0-255)

        [ObservableProperty]
        private string hexValue; // 十六进制显示

        [ObservableProperty]
        private DateTime lastUpdateTime; // 最后更新时间

        public ByteDataItem(int position, byte value = 0)
        {
            Position = position;
            Value = value;
            HexValue = value.ToString("X2");
            LastUpdateTime = DateTime.Now;
        }

        partial void OnValueChanged(byte value)
        {
            HexValue = value.ToString("X2");
            LastUpdateTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 数据帧更新器基类
    /// </summary>
    public abstract class FrameDataUpdaterBase
    {
        protected readonly Random _random = new();
        protected readonly ObservableCollection<ByteDataItem> _dataFrame;
        protected int _changeCount = 0;
        protected bool _needStop = false;

        public FrameDataUpdaterBase(ObservableCollection<ByteDataItem> dataFrame)
        {
            _dataFrame = dataFrame;
        }

        public int GetAndResetChangesCount()
        {
            var result = _changeCount;
            _changeCount = 0;
            return result;
        }

        public void Stop()
        {
            _needStop = true;
        }

        public abstract void SetUpdateRate(int updatesPerSecond);

        protected void RandomlyUpdateBytes(int count)
        {
            for (int i = 0; i < count; i++)
            {
                int position = _random.Next(0, _dataFrame.Count);
                byte newValue = (byte)_random.Next(0, 256);

                Application.Current?.Dispatcher.Invoke(() =>
                {
                    _dataFrame[position].Value = newValue;
                });

                _changeCount++;
            }
        }
    }

    /// <summary>
    /// 实时数据帧更新器
    /// </summary>
    public class RealTimeFrameDataUpdater : FrameDataUpdaterBase
    {
        private int _interEventDelayMs = 100;
        private Task? _updateTask;
        private readonly CancellationTokenSource _cancellationTokenSource = new();

        public RealTimeFrameDataUpdater(ObservableCollection<ByteDataItem> dataFrame) : base(dataFrame)
        {
            _updateTask = Task.Factory.StartNew(UpdateDataLoop, _cancellationTokenSource.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
        }

        private void UpdateDataLoop()
        {
            var stopwatch = Stopwatch.StartNew();

            while (!_needStop && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                stopwatch.Restart();

                // 每次更新1-10个字节
                int updateCount = _random.Next(1, 11);
                RandomlyUpdateBytes(updateCount);

                // 控制更新频率
                int sleepTime = _interEventDelayMs - (int)stopwatch.ElapsedMilliseconds;
                if (sleepTime > 0)
                {
                    Thread.Sleep(sleepTime);
                }
            }
        }

        public override void SetUpdateRate(int updatesPerSecond)
        {
            _interEventDelayMs = Math.Max(1, 1000 / updatesPerSecond);
        }

        public void Dispose()
        {
            Stop();
            _cancellationTokenSource.Cancel();
            _updateTask?.Wait(1000);
            _cancellationTokenSource.Dispose();
        }
    }

    /// <summary>
    /// Page1的ViewModel，用于管理7500字节数据帧的实时表格
    /// </summary>
    public partial class Page1ViewModel : ObservableObject
    {
        private readonly DispatcherTimer _performanceTimer;
        private readonly Stopwatch _performanceStopwatch = new();
        private RealTimeFrameDataUpdater? _dataUpdater;

        [ObservableProperty]
        private ObservableCollection<ByteDataItem> dataFrame;

        [ObservableProperty]
        private bool isRealTimeUpdateEnabled = false;

        [ObservableProperty]
        private int updatesPerSecond = 10; // 每秒更新次数

        [ObservableProperty]
        private string statusText = "就绪";

        [ObservableProperty]
        private ObservableCollection<UpdateCountInfo> updateInfo;

        public class UpdateCountInfo
        {
            public double TimeStamp { get; set; }
            public int ChangesPerSecond { get; set; }

            public UpdateCountInfo(double timeStamp, int changesPerSecond)
            {
                TimeStamp = timeStamp;
                ChangesPerSecond = changesPerSecond;
            }
        }

        public Page1ViewModel()
        {
            // 初始化7500字节数据帧
            InitializeDataFrame();

            // 初始化性能监控
            UpdateInfo = new ObservableCollection<UpdateCountInfo>();
            _performanceTimer = new DispatcherTimer(TimeSpan.FromSeconds(0.5), DispatcherPriority.Send, PerformanceTimerCallback, Dispatcher.CurrentDispatcher);
            _performanceStopwatch.Start();
        }

        /// <summary>
        /// 初始化7500字节数据帧
        /// </summary>
        private void InitializeDataFrame()
        {
            DataFrame = new ObservableCollection<ByteDataItem>();

            // 创建7500个字节数据项
            for (int i = 0; i < 7500; i++)
            {
                DataFrame.Add(new ByteDataItem(i, (byte)(i % 256)));
            }

            StatusText = $"数据帧已初始化：7500 字节";
        }

        /// <summary>
        /// 性能监控定时器回调
        /// </summary>
        private void PerformanceTimerCallback(object? sender, EventArgs e)
        {
            if (_dataUpdater != null)
            {
                var changesPerSecond = _dataUpdater.GetAndResetChangesCount() * 2; // 因为定时器是0.5秒间隔
                UpdateInfo.Add(new UpdateCountInfo(DateTime.Now.TimeOfDay.TotalSeconds, changesPerSecond));

                // 保持最近10个数据点
                if (UpdateInfo.Count > 10)
                    UpdateInfo.RemoveAt(0);

                StatusText = $"实时更新中... 当前更新频率: {changesPerSecond} 字节/秒";
            }
        }

        /// <summary>
        /// 开始实时更新
        /// </summary>
        public void StartRealTimeUpdate()
        {
            if (!IsRealTimeUpdateEnabled)
            {
                IsRealTimeUpdateEnabled = true;
                _dataUpdater = new RealTimeFrameDataUpdater(DataFrame);
                _dataUpdater.SetUpdateRate(UpdatesPerSecond);
                _performanceTimer.Start();
                StatusText = "实时更新已启动";
            }
        }

        /// <summary>
        /// 停止实时更新
        /// </summary>
        public void StopRealTimeUpdate()
        {
            if (IsRealTimeUpdateEnabled)
            {
                IsRealTimeUpdateEnabled = false;
                _dataUpdater?.Stop();
                _dataUpdater?.Dispose();
                _dataUpdater = null;
                _performanceTimer.Stop();
                StatusText = "实时更新已停止";
            }
        }

        /// <summary>
        /// 更新频率变化时的处理
        /// </summary>
        partial void OnUpdatesPerSecondChanged(int value)
        {
            _dataUpdater?.SetUpdateRate(value);
        }

        /// <summary>
        /// 重置数据帧
        /// </summary>
        public void ResetDataFrame()
        {
            StopRealTimeUpdate();
            InitializeDataFrame();
        }

        /// <summary>
        /// 模拟接收新的数据帧
        /// </summary>
        public void UpdateDataFrame(byte[] newFrameData)
        {
            if (newFrameData == null || newFrameData.Length != 7500)
            {
                StatusText = "数据帧长度错误，应为7500字节";
                return;
            }

            Application.Current?.Dispatcher.Invoke(() =>
            {
                for (int i = 0; i < 7500; i++)
                {
                    DataFrame[i].Value = newFrameData[i];
                }
            });

            StatusText = "数据帧已更新";
        }

        /// <summary>
        /// 获取当前数据帧的字节数组
        /// </summary>
        public byte[] GetCurrentFrameData()
        {
            byte[] frameData = new byte[7500];
            for (int i = 0; i < 7500; i++)
            {
                frameData[i] = DataFrame[i].Value;
            }
            return frameData;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            StopRealTimeUpdate();
            _performanceTimer?.Stop();
        }


    }
}
