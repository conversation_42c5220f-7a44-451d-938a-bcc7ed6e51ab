﻿using DevExpress.XtraPrinting.Native;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Windows.Storage.Streams;

namespace 测量地面综合测控台上位机软件
{
    public class FrameAnalyzer(Frame frame) : IDisposable
    {
        /// <summary>
        /// 待解析的帧结构
        /// </summary>
        public readonly Frame FrameStructure  = frame;

        /// <summary>
        /// 是否匹配连续两帧的帧标识（防止把非帧标识的数据误判为帧标识）。
        /// </summary>
        /// <remarks>当帧与帧之间有其它数据，必须为false。帧与帧之间无填充，则可根据帧标识长度选择是否需要设为true。</remarks>
        public bool FindTwoFlag { get; set; } = false;

        /// <summary>
        /// 存储原始数据的缓冲区
        /// </summary>
        private readonly ConcurrentQueue<byte[]> rawDataBuffer = new();

        /// <summary>
        /// 数据缓冲区最大容量
        /// </summary>
        public int MaxBufferSize { get; set; } = 10485760;

        /// <summary>
        /// 原始数据缓冲区当前字节数
        /// </summary>
        private int bufferCurrentSize = 0;

        /// <summary>
        /// 存储解出的数据帧的缓冲区
        /// </summary>
        private readonly ConcurrentQueue<byte[]> frameBuffer = new();

        /// <summary>
        /// 数据到达通知信号（唤醒解析线程）
        /// </summary>
        private readonly ManualResetEventSlim dataAvailableEvent = new(false);

        /// <summary>
        /// 解析线程控制标志
        /// </summary>
        private volatile bool isProcessing = true;

        /// <summary>
        /// 解析线程
        /// </summary>
        private Thread? processingThread;


        public bool AnalyseChannel = false;

        public void Reset()
        {
            rawDataBuffer.Clear();
            frameBuffer.Clear();
            bufferCurrentSize = 0;
        }

        /// <summary>
        /// 获取缓冲区中原始数据大小/字节
        /// </summary>
        /// <returns></returns>
        public int GetRawDataSize()
        {
            return bufferCurrentSize;
        }

        /// <summary>
        /// 获取缓冲区中帧数/帧
        /// </summary>
        /// <returns></returns>
        public int GetFrameCount()
        {
            return frameBuffer.Count;
        }


        public void ClearRawDataBuffer()
        {
            rawDataBuffer.Clear();
            bufferCurrentSize = 0;
        }

        public void AddRawData(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return;
            }

            if (bufferCurrentSize + data.Length < MaxBufferSize)
            {
                rawDataBuffer.Enqueue(data);
                bufferCurrentSize += data.Length;
            }
            else
            {
                while (!rawDataBuffer.IsEmpty && bufferCurrentSize + data.Length > MaxBufferSize)
                {
                    if (rawDataBuffer.TryDequeue(out byte[] dataDisposed))
                    {
                        bufferCurrentSize -= dataDisposed.Length;
                        Trace.TraceWarning($"{FrameStructure.FrameName}:原始数据缓冲区堆积过多数据");
                    }
                }

                rawDataBuffer.Enqueue(data);
                bufferCurrentSize += data.Length;
            }
            dataAvailableEvent.Set();
        }





        private void AddFrame(byte[] frame)
        {
            if(frameBuffer.Count>10000)
            {
                frameBuffer.TryDequeue(out _);
                Trace.TraceWarning($"{FrameStructure.FrameName}:帧数据缓冲区堆积过多数据");
            }


            frameBuffer.Enqueue(frame);
        }

        public byte[]? GetFrame()
        {
            return frameBuffer.TryDequeue(out var frame) ? frame : null;
        }

        public byte[]? GetLastFrameAndClearBuffer()
        {
            if(frameBuffer.IsEmpty)
                return null;

            byte[] ret = [.. frameBuffer.Last()];
            frameBuffer.Clear();
           return ret;
        }

        private void ProcessingLoop()
        {
            List<byte> buffer = [];
            int i;
            bool find;
            int flagCount = FrameStructure.FrameFlagPosition.Count;
            int bufLen = FrameStructure.FrameLength;
            isProcessing = true;

            List<Channel> channels = FrameStructure.CurrentDeviceChannels;


            if (FindTwoFlag)
            {
                bufLen= FrameStructure.FrameLength*2;
                while (isProcessing)
                {
                    dataAvailableEvent.Wait(TimeSpan.FromMilliseconds(200));
                    dataAvailableEvent.Reset();

                    while (rawDataBuffer.TryDequeue(out byte[] rawData))
                    {
                        buffer.AddRange(rawData);


                        while (buffer.Count >= bufLen)
                        {
                            find = true;
                            for (i = 0; i < flagCount; i++)
                            {
                                if (buffer[FrameStructure.FrameFlagPosition[i]] != FrameStructure.FrameFlag[i])
                                {
                                    find = false;
                                    break;
                                }
                            }

                            if (find)
                            {
                                for (i = 0; i < flagCount; i++)
                                {
                                    if (buffer[FrameStructure.FrameLength+ FrameStructure.FrameFlagPosition[i]] != FrameStructure.FrameFlag[i])
                                    {
                                        find = false;
                                        break;
                                    }
                                }
                            }

                            if (find)
                            {
                                byte[] frame = [.. buffer.Take(FrameStructure.FrameLength)];
                                AddFrame(frame);
                                buffer.RemoveRange(0, FrameStructure.FrameLength);

                                if (AnalyseChannel)
                                {
                                    //foreach (Channel ch in channels)
                                    //{
                                    //    ch.GetNewSamplesFromFrame(frame);
                                    //}

                                    FrameStructure.GetNewSamplesFromFrame(frame);


                                }
                            }
                            else
                            {
                                buffer.RemoveAt(0);
                            }
                        }
                    }
                }
            }
            else
            {
                while (isProcessing)
                {
                    dataAvailableEvent.Wait(TimeSpan.FromMilliseconds(20));
                    dataAvailableEvent.Reset();

                    while (rawDataBuffer.TryDequeue(out byte[] rawData))
                    {
                        buffer.AddRange(rawData);


                        while (buffer.Count >= bufLen)
                        {
                            find = true;
                            for (i = 0; i < flagCount; i++)
                            {
                                if (buffer[FrameStructure.FrameFlagPosition[i]] != FrameStructure.FrameFlag[i])
                                {
                                    find = false;
                                    break;
                                }
                            }
                            if(find)
                            {
                                byte[] frame=[..buffer.Take(FrameStructure.FrameLength)];
                                AddFrame(frame);
                                //Trace.WriteLine($"解出一帧{FrameStructure.FrameName},当前缓冲区帧数{frameBuffer.Count}");
                                buffer.RemoveRange(0, FrameStructure.FrameLength);

                                if(AnalyseChannel)
                                {
                                    //foreach(Channel ch in channels)
                                    //{
                                    //    ch.GetNewSamplesFromFrame(frame);
                                    //  // Trace.WriteLine( $"解析线程中{ch.ChannelName}的地址为{ Function.getMemory(ch)}");
                                    //}

                                    FrameStructure.GetNewSamplesFromFrame(frame);
                                }
                            }
                            else
                            {
                                buffer.RemoveAt(0);
                            }
                        }
                    }
                }
            }
        }

        public bool StartAnalyse(ThreadPriority priority= ThreadPriority.Normal)
        {
            if (processingThread != null)
                return false;

            Reset();

            processingThread = new Thread(ProcessingLoop)
            {
                IsBackground = true,
                Name = $"解析{FrameStructure.FrameName}",
                Priority = priority
            };
            processingThread.Start();
            return true;
        }

        public void StopAnalyse()
        {
            isProcessing = false;
            processingThread?.Join();
            processingThread = null;
        }


        public void UpdateChannelLastValue()
        {
            byte[] lastFrame = GetLastFrameAndClearBuffer();

            if (lastFrame != null)
            {
                foreach (Channel ch in FrameStructure.CurrentDeviceChannels)
                {
                    ch.UpdateLastValue(lastFrame);
                }
            }
        }


        protected virtual void Dispose(bool disposing)
        {
            isProcessing = false;
            processingThread?.Join();  // 等待线程退出
            dataAvailableEvent.Dispose();
            GC.SuppressFinalize(this);
        }

        ~FrameAnalyzer()
        {
            Dispose(disposing: false);
        }

        public void Dispose()
        {
            Dispose(disposing: true);
        }
    }
}
