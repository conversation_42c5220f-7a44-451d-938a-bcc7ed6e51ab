﻿<Window
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
        xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui" 
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors" 
        xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        x:Class="测量地面综合测控台上位机软件.Window_FrameConfig"
        mc:Ignorable="d"
        Title="配置" Height="1000" Width="1000">
    <dxwui:HamburgerMenu AvailableViewStates="Inline" >
        <dxwui:HamburgerMenu.BottomBarItems>
            <dxwui:HamburgerMenuBottomBarNavigationButton Content="Settings" GlyphHeight="16" GlyphWidth="16"/>
            <dxwui:HamburgerMenuBottomBarNavigationButton Content="About" GlyphHeight="16" GlyphWidth="16"/>
        </dxwui:HamburgerMenu.BottomBarItems>
        <dxwui:HamburgerMenu.Content>
            <dxwui:NavigationFrame Source="Page0" AnimationSpeedRatio="10" AnimationType="None"/>
        </dxwui:HamburgerMenu.Content>
        <dxwui:HamburgerMenuNavigationButton Content="电源卡配置" GlyphHeight="16" GlyphWidth="16" IsSelected="True" NavigationTargetTypeName="Page0"/>
        <dxwui:HamburgerMenuNavigationButton Content="帧配置" GlyphHeight="16" GlyphWidth="16" NavigationTargetTypeName="Page2"/>
        <dxwui:HamburgerMenuHyperlinkButton Content="Support" NavigateUri=""/>
    </dxwui:HamburgerMenu>









</Window>
