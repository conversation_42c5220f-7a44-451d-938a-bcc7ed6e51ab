﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class SendFrame_DigitalCard
    {
        /// <summary>
        /// 帧头
        /// </summary>
        public static readonly byte[] FrameHead = [0xEB, 0x90];
        /// <summary>
        /// 帧尾
        /// </summary>
        public static readonly byte[] FrameTail = [0x14, 0x6F];

        /// <summary>
        /// 帧长
        /// </summary>
        public ushort FrameLength { get; }

        /// <summary>
        /// 帧类型
        /// <para>0x00:第1路数字量数据</para>
        /// <para>0x01:第2路数字量数据</para>
        /// <para>...</para>
        /// <para>0x0F:第16路数字量数据</para>
        /// <para>__________________________________</para>
        /// <para>0x10:第1路数字量指令</para>
        /// <para>0x11:第2路数字量指令</para>
        /// <para>...</para>
        /// <para>0x1F:第16路数字量指令</para>
        /// </summary>
        public byte FrameType { get; set; }

        /// <summary>
        /// 全帧计数
        /// </summary>
        public byte CounterF { get; set; }


        /// <summary>
        /// 01:配置  00:数据或指令
        /// </summary>
        public byte DataType = 0x01;

        #region 数据区配置内容



        /// <summary>
        /// 预留
        /// </summary>
        public byte Spare = 0x00;

        /// <summary>
        /// 周期
        /// </summary>
        public byte Cycle { get; set; } = 0;

        /// <summary>
        /// 波特率  
        /// </summary>
        public uint BaudRate { get; set; }

        /// <summary>
        /// <para>校验类型</para>
        /// <para>0x00:无校验</para>
        /// <para>0x01:奇校验</para>
        /// <para>0x02:偶校验</para>
        /// </summary>
        public byte Parity { get; set; } = 0;

        /// <summary>
        /// 单次发送数据/指令长度
        /// </summary>
        public ushort DataLength;

        #endregion

        
        

        /// <summary>
        /// 指令/数据
        /// </summary>
        public byte[] Data { get; set; } = [];

        /// <summary>
        /// 
        /// </summary>
        /// <param name="frameType">
        /// <para>0x00:第1路数字量数据</para>
        /// <para>0x01:第2路数字量数据</para>
        /// <para>...</para>
        /// <para>0x0F:第16路数字量数据</para>
        /// <para>__________________________________</para>
        /// <para>0x10:第1路数字量指令</para>
        /// <para>0x11:第2路数字量指令</para>
        /// <para>...</para>
        /// <para>0x1F:第16路数字量指令</para>
        /// </param>
        /// <param name="counterF"></param>
        /// <param name="parity">
        /// <para>0x00:数据或者指令</para>
        /// <para>0x01:无校验</para>
        /// <para>0x02:奇校验</para>
        /// <para>0x03:偶校验</para>
        /// </param>
        /// <param name="sendCount"></param>
        /// <param name="cycle"></param>
        /// <param name="data"></param>
        /// <exception cref="ArgumentException"></exception>
        public SendFrame_DigitalCard(byte frameType, byte counterF, byte parity, byte cycle,uint baudrate, ushort DataLen)
        {
            DataType = 0x01; // 配置数据
            FrameType = frameType;
            CounterF = counterF;
            Parity = parity;
            
            Cycle = cycle;
            BaudRate = baudrate;

            DataLength = DataLen;

            // 计算帧长度
            FrameLength = 17;
        }

        public SendFrame_DigitalCard(byte frameType,byte counterF, byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                throw new ArgumentException("数据区不能为空");
            }

            DataType = 0x00;// 数据或者指令
            FrameType = frameType;
            CounterF = counterF;
            Data = data;


            FrameLength = (ushort)(9 + data.Length);

        }

        /// <summary>
        /// 指令/数据帧转换为字节数组
        /// </summary>
        /// <returns></returns>
        public byte[] ToBytes()
        {
            byte[] bytes = new byte[FrameLength];

            // 填充帧头
            Buffer.BlockCopy(FrameHead, 0, bytes, 0, FrameHead.Length);
            // 填充帧类型
            bytes[2] = FrameType;
            // 填充帧长度
            bytes[3] = (byte)(FrameLength >> 8);
            bytes[4] = (byte)(FrameLength & 0xFF);
            // 填充全帧计数
            bytes[5] = CounterF;

            // 填充数据区

            bytes[6] = DataType;

            if (DataType == 0x01)
            {
                bytes[7] = Spare;
                bytes[8] = Cycle;

                uint temp = 40000000 / BaudRate;

                bytes[9] = (byte)(temp >> 16);
                bytes[10] = (byte)(temp >> 8);
                bytes[11] = (byte)(temp & 0xFF);
                bytes[12] = Parity;


                bytes[13] = (byte)(DataLength >>8);
                bytes[14] = (byte)(DataLength & 0xFF);
            }
            else
            {
                Buffer.BlockCopy(Data, 0, bytes, 9, Data.Length);
            }
 
            // 填充帧尾
            Buffer.BlockCopy(FrameTail, 0, bytes, FrameLength - 2, FrameTail.Length);
            return bytes;
        }


    }

}
