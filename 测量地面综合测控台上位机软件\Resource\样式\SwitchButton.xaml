﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件">
    <Style x:Key="SwitchButtonStyle" TargetType="{x:Type local:MySwitchButton }">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:MySwitchButton}">
                    <Border
                        x:Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="1"
                        CornerRadius="4">
                        <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <TextBlock
                                    x:Name="btnText"
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    Foreground="{TemplateBinding Foreground}">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding ContextWhenUnChecked, RelativeSource={RelativeSource TemplatedParent}}" />
                                            <Style.Triggers>
                                                <DataTrigger Value="True" Binding="{Binding IsChecked, RelativeSource={RelativeSource TemplatedParent}}">
                                                    <Setter Property="Text" Value="{Binding ContextWhenChecked, RelativeSource={RelativeSource TemplatedParent}}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <Image
                                    x:Name="statusImage"
                                    Width="16"
                                    Height="16"
                                    VerticalAlignment="Center" />
                            </StackPanel>
                        </Grid>

                    </Border>

                    <ControlTemplate.Triggers>
                        <!-- 鼠标悬停效果 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderThickness" Value="2" />
                            <Setter TargetName="border" Property="BorderBrush" Value="#FF64B5F6" />
                        </Trigger>

                        <!-- 按压效果 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="BorderThickness" Value="3" />
                            <Setter TargetName="border" Property="BorderBrush" Value="#FF1976D2" />
                        </Trigger>

                        <!-- 禁用效果 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>

                        <!-- 开关状态样式 -->
                        <!--<DataTrigger Binding="{Binding PowerCardViewModel.Is28VPowerOn}" Value="True">
                            -->                        <!--<Setter Property="Background" Value="#FF4CAF50"/>-->                        <!--
                            <Setter Property="BorderBrush" Value="#FF388E3C"/>
                            <Setter Property="Foreground" Value="#FF424242"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="3"/>
                            <Setter TargetName="statusImage" Property="Source" Value="/Image/green.png"/>
                        </DataTrigger>

                        <DataTrigger Binding="{Binding PowerCardViewModel.Is28VPowerOn}" Value="False">
                            -->                        <!--<Setter Property="Background" Value="#FFE0E0E0"/>-->                        <!--
                            <Setter Property="BorderBrush" Value="#FFBDBDBD"/>
                            <Setter Property="Foreground" Value="#FF424242"/>
                            <Setter Property="BorderThickness" Value="1"/>
                            <Setter TargetName="statusImage" Property="Source" Value="/Image/gray.png"/>
                        </DataTrigger>-->

                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="Background" Value="#FF4CAF50" />
                            <Setter Property="BorderBrush" Value="#FF388E3C" />
                            <Setter Property="Foreground" Value="#FF424242" />
                            <Setter TargetName="border" Property="BorderThickness" Value="3" />
                            <Setter TargetName="statusImage" Property="Source" Value="/Image/green.png" />
                            <!--<Setter TargetName="btnText" Property="Text" Value="{TemplateBinding ContextWhenChecked}"/>-->
                        </Trigger>

                        <Trigger Property="IsChecked" Value="False">
                            <Setter Property="Background" Value="#FFE0E0E0" />
                            <Setter Property="BorderBrush" Value="#FFBDBDBD" />
                            <Setter Property="Foreground" Value="#FF424242" />
                            <Setter Property="BorderThickness" Value="1" />
                            <Setter TargetName="statusImage" Property="Source" Value="/Image/gray.png" />
                            <!--<Setter TargetName="btnText" Property="Text" Value="{TemplateBinding ContextWhenUnChecked}"/>-->
                        </Trigger>


                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 基础样式 -->
        <Setter Property="Width" Value="120" />
        <Setter Property="Height" Value="30" />
        <Setter Property="FontSize" Value="14" />
        <!--<Setter Property="Cursor" Value="Hand"/>-->
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="BorderThickness" Value="1" />
    </Style>
</ResourceDictionary>