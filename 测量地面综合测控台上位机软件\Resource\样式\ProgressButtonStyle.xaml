﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件">

    <!-- ProgressButton 样式 -->
    <Style TargetType="{x:Type local:ProgressButton}">
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="MinHeight" Value="30"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:ProgressButton}">
                    <Grid>
                        <!-- 主边框 -->
                        <Border x:Name="MainBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">

                            <Grid>
                                <!-- 进度条背景 -->
                                <Border x:Name="ProgressBackground"
                                        Background="{TemplateBinding ProgressBackground}"
                                        CornerRadius="2"
                                        Margin="1">

                                    <!-- 进度条前景 -->
                                    <Border x:Name="ProgressForeground"
                                            Background="{TemplateBinding ProgressForeground}"
                                            CornerRadius="2"
                                            HorizontalAlignment="Left"
                                            Opacity="0.8">
                                        <Border.Width>
                                            <MultiBinding>
                                                <MultiBinding.Converter>
                                                    <local:ProgressToWidthConverter/>
                                                </MultiBinding.Converter>
                                                <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Progress"/>
                                                <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="ActualWidth"/>
                                            </MultiBinding>
                                        </Border.Width>
                                    </Border>
                                </Border>

                                <!-- 文本内容 -->
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Content="{TemplateBinding CurrentText}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Margin="{TemplateBinding Padding}"
                                                  RecognizesAccessKey="True"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </Grid>
                        </Border>
                        
                        <!-- 视觉状态管理 -->
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="MainBorder"
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="#E5E5E5"
                                                        Duration="0:0:0.1"/>
                                        <ColorAnimation Storyboard.TargetName="MainBorder"
                                                        Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                        To="#999999"
                                                        Duration="0:0:0.1"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="MainBorder"
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="#DADADA"
                                                        Duration="0:0:0.05"/>
                                        <DoubleAnimation Storyboard.TargetName="ContentPresenter"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.8"
                                                         Duration="0:0:0.05"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="MainBorder"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.5"
                                                         Duration="0:0:0.1"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            
                            <VisualStateGroup x:Name="ProgressStates">
                                <VisualState x:Name="NotInProgress">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="ProgressBackground"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0:0:0.2"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="InProgress">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="ProgressBackground"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1"
                                                         Duration="0:0:0.2"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    
                    <ControlTemplate.Triggers>
                        <!-- 进度状态触发器 -->
                        <Trigger Property="IsInProgress" Value="True">
                            <Setter TargetName="ProgressBackground" Property="Opacity" Value="1"/>
                        </Trigger>
                        <Trigger Property="IsInProgress" Value="False">
                            <Setter TargetName="ProgressBackground" Property="Opacity" Value="0"/>
                        </Trigger>
                        
                        <!-- 鼠标悬停触发器 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsInProgress" Value="True"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="ProgressForeground" Property="Background" Value="#FF6B6B"/>
                            <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="White"/>
                            <Setter TargetName="ContentPresenter" Property="TextElement.FontWeight" Value="Bold"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
