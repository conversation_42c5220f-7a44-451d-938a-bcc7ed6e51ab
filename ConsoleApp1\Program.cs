﻿// See https://aka.ms/new-console-template for more information
using System.Text.RegularExpressions;



 static string ConvertToVersion(byte[] buffer, string formattedStr)
{
    string[] parts = formattedStr.Split('.');
    List<int> values = new List<int>();
    int totalBits = buffer.Length * 8;

    foreach (string part in parts)
    {
        Match match = Regex.Match(part, @"\[(\d+):(\d+)\]");
        if (!match.Success)
            throw new ArgumentException($"无效的格式: {part}");

        int high = int.Parse(match.Groups[1].Value);
        int low = int.Parse(match.Groups[2].Value);

        if (high < low)
            throw new ArgumentException($"{part}：高位索引必须 >= 低位索引 ");

        if (high >= totalBits || low < 0)
            throw new ArgumentException($"{part}：bit索引超出范围");

        int numBits = high - low + 1;
        int value = 0;

        for (int i = 0; i < numBits; i++)
        {
            int pos = high - i;
            int overallIndex = totalBits - 1 - pos;
            int byteIndex = overallIndex / 8;
            int bitInByte = overallIndex % 8;
            int bit = (buffer[byteIndex] >> (7 - bitInByte)) & 1;
            value = (value << 1) | bit;
        }
        values.Add(value);
    }

    string[] strParts = new string[values.Count];
    for (int i = 0; i < values.Count; i++)
    {
        // 除了主版本号，其余的小于10时补零成两位数
        if (i == 0)
            strParts[i] = values[i].ToString();
        else
        {
            if (values[i] < 10)
                strParts[i] = values[i].ToString("D2");
            else
                strParts[i] = values[i].ToString();
        }
    }

    return string.Join(".", strParts);
}

string temp = ConvertToVersion([0x17], "[7:5].[4:0]");

temp = ConvertToVersion([0xFF], "[7:7].[4:0]");

temp = ConvertToVersion([0xAB,0xCD], "[14:7].[5:3].[2:0]");