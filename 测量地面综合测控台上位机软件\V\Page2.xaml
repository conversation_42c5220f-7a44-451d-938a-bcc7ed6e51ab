﻿<dxwui:NavigationPage x:Class="测量地面综合测控台上位机软件.V.Page2"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:vm="clr-namespace:测量地面综合测控台上位机软件.VM"
      xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
      xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
      xmlns:system="clr-namespace:System;assembly=mscorlib"
      xmlns:converters="clr-namespace:测量地面综合测控台上位机软件.Converters"
      mc:Ignorable="d"
      d:DesignHeight="800" d:DesignWidth="800"
                    
                      >

    <dxwui:NavigationPage.DataContext>
        <vm:FrameManagementViewModel />
    </dxwui:NavigationPage.DataContext>

    <dxwui:NavigationPage.Resources>
        <!-- 添加转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:IsFrameNodeConverter x:Key="IsFrameNodeConverter"/>
        <converters:IsDeviceNodeConverter x:Key="IsDeviceNodeConverter"/>
        <converters:IsChannelNodeConverter x:Key="IsChannelNodeConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:DataTypeConverter x:Key="DataTypeConverter"/>
        <converters:MultiBooleanToVisibilityConverter x:Key="MultiBooleanToVisibilityConverter"/>
        <converters:DecimalInputConverter x:Key="DecimalInputConverter"/>

        <!-- 树节点样式 -->
        <HierarchicalDataTemplate x:Key="ChannelTemplate" DataType="{x:Type vm:ChannelNodeViewModel}">
            <StackPanel Orientation="Horizontal" Margin="2">
                <TextBlock Text="{Binding DisplayName}"
                          Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </HierarchicalDataTemplate>

        <HierarchicalDataTemplate x:Key="DeviceTemplate"
                                 DataType="{x:Type vm:DeviceNodeViewModel}"
                                 ItemsSource="{Binding Channels}"
                                 ItemTemplate="{StaticResource ChannelTemplate}">
            <StackPanel Orientation="Horizontal" Margin="2">
                <TextBlock Text="{Binding DisplayName}"
                          Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                          FontWeight="SemiBold"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </HierarchicalDataTemplate>

        <HierarchicalDataTemplate x:Key="FrameTemplate"
                                 DataType="{x:Type vm:FrameNodeViewModel}"
                                 ItemsSource="{Binding Devices}"
                                 ItemTemplate="{StaticResource DeviceTemplate}">
            <StackPanel Orientation="Horizontal" Margin="2">
                <TextBlock Text="{Binding DisplayName}"
                          Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                          FontWeight="Bold"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </HierarchicalDataTemplate>

        <!-- 节点可见性样式 -->
        <Style x:Key="TreeViewItemStyle" TargetType="TreeViewItem">
            <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}" />
            <Setter Property="Visibility" Value="{Binding IsVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
            <Setter Property="Padding" Value="2" />
            <Setter Property="Margin" Value="1" />
        </Style>
    </dxwui:NavigationPage.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" MinWidth="400"/>
            <ColumnDefinition Width="5"/>
            <ColumnDefinition Width="*" MinWidth="400"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧：树形结构 -->
        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题和工具栏 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10,10,10,5">
                <TextBlock Text="帧结构管理" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                <Button Content="🔄刷新" ToolTip="刷新" Command="{Binding RefreshDataCommand}"
                       Width="Auto" Height="30" Margin="10,0,5,0"/>
                <Button Content="📂展开所有" ToolTip="展开所有" Command="{Binding ExpandAllCommand}"
                       Width="Auto" Height="30" Margin="0,0,5,0"/>
                <Button Content="📁折叠所有" ToolTip="折叠所有" Command="{Binding CollapseAllCommand}"
                       Width="Auto" Height="30"/>
            </StackPanel>

            <!-- 搜索框 -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="10,5" Height="Auto">
                <TextBlock Text="搜索：" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        Width="200" Height="25" VerticalAlignment="Center"
                        ToolTip="输入关键字搜索Frame、Device或Channel"/>
                <Button Content="❌清除搜索" ToolTip="清除搜索" Width="Auto" Height="Auto" Margin="5,0,0,0"
                       Click="ClearSearch_Click"/>
            </StackPanel>

            <!-- 树形控件 -->
            <TreeView Grid.Row="2"
                     ItemsSource="{Binding Frames}"
                     ItemTemplate="{StaticResource FrameTemplate}"
                     ItemContainerStyle="{StaticResource TreeViewItemStyle}"
                      HorizontalAlignment="Stretch"
                     SelectedItemChanged="TreeView_SelectedItemChanged"
                     Margin="10,5,10,5"
                     BorderBrush="Gray" BorderThickness="1">
            </TreeView>

            <!-- 操作按钮 -->
            <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                <Button Content="➕ 添加设备" Command="{Binding AddDeviceCommand}"
                       Margin="0,0,5,0" Padding="10,5"/>
                <Button Content="➕ 添加通道" Command="{Binding AddChannelCommand}"
                       Margin="0,0,5,0" Padding="10,5"/>
                <Button Content="🗑️ 删除设备" Command="{Binding DeleteDeviceCommand}"
                       IsEnabled="{Binding SelectedDeviceNode.Device.CanDelete}"
                       Margin="0,0,5,0" Padding="10,5"/>
                <Button Content="🗑️ 删除通道" Command="{Binding DeleteChannelCommand}"
                       Padding="10,5"/>
            </StackPanel>
        </Grid>

        <!-- 分隔符 -->
        <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch"
                     Background="LightGray" ResizeBehavior="PreviousAndNext"/>

        <!-- 右侧：属性编辑面板 -->
        <Grid Grid.Column="2" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 属性面板标题 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                <!-- Channel 标题 -->
                <TextBlock Text="通道属性编辑" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"
                          Visibility="{Binding IsChannelPropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                <TextBlock Text="{Binding SelectedChannel.ChannelName, StringFormat=' - {0}'}"
                          FontSize="14" VerticalAlignment="Center" Margin="10,0,0,0"
                          Visibility="{Binding IsChannelPropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- Device 标题 -->
                <TextBlock Text="设备属性编辑" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"
                          Visibility="{Binding IsDevicePropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                <TextBlock Text="{Binding SelectedDevice.DeviceName, StringFormat=' - {0}'}"
                          FontSize="14" VerticalAlignment="Center" Margin="10,0,0,0"
                          Visibility="{Binding IsDevicePropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Channel 属性编辑区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                         Visibility="{Binding IsChannelPropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel Margin="0,0,20,0">
                    <!-- 基本信息 -->
                    <GroupBox Header="基本信息" Margin="0,0,0,10">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="通道名称：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedChannel.ChannelName, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="通道描述：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedChannel.ChannelDescription, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="数据类型：" VerticalAlignment="Center" Margin="0,5"/>
                            <ComboBox Grid.Row="2" Grid.Column="1"
                                     SelectedValue="{Binding SelectedChannel.DataType, Converter={StaticResource DataTypeConverter}}"
                                     SelectedValuePath="Tag"
                                     Height="25" Margin="5">
                                <ComboBox.Items>
                                    <ComboBoxItem Content="有符号整数" Tag="有符号整数"/>
                                    <ComboBoxItem Content="无符号整数" Tag="无符号整数"/>
                                    <ComboBoxItem Content="单精度浮点数" Tag="单精度浮点数"/>
                                    <ComboBoxItem Content="双精度浮点数" Tag="双精度浮点数"/>
                                    <ComboBoxItem Content="比特位" Tag="比特位"/>
                                    <ComboBoxItem Content="版本号" Tag="版本号"/>
                                    <ComboBoxItem Content="自定义" Tag="自定义"/>
                                </ComboBox.Items>
                            </ComboBox>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="样本点长度：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding SelectedChannel.PointBytes, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5"/>
                        </Grid>
                    </GroupBox>

                    <!-- 显示设置 -->
                    <GroupBox Header="显示设置" Margin="0,0,0,10">
                        <StackPanel Margin="10">
                            <CheckBox Content="在界面上监测" IsChecked="{Binding SelectedChannel.Monitor}" Margin="0,5"/>
                            <CheckBox Content="在界面上显示曲线" IsChecked="{Binding SelectedChannel.ShowCurve}" Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- 数值范围设置 -->
                    <GroupBox Header="数值范围" Margin="0,0,0,10">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="增益系数：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedChannel.GainCoef, UpdateSourceTrigger=LostFocus, ValidatesOnExceptions=True}"
                                    Height="25" Margin="5"
                                    ToolTip="支持小数输入，例如：1.234"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="偏移系数：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedChannel.OffsetCoef, UpdateSourceTrigger=LostFocus, ValidatesOnExceptions=True}"
                                    Height="25" Margin="5"
                                    ToolTip="支持小数输入，例如：0.123"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="最小值：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding SelectedChannel.Minimum, UpdateSourceTrigger=LostFocus, ValidatesOnExceptions=True}"
                                    Height="25" Margin="5"
                                    ToolTip="支持小数输入，例如：-123.456"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="最大值：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding SelectedChannel.Maximum, UpdateSourceTrigger=LostFocus, ValidatesOnExceptions=True}"
                                    Height="25" Margin="5"
                                    ToolTip="支持小数输入，例如：123.456"/>
                        </Grid>
                    </GroupBox>

                    <!-- 单位设置 -->
                    <GroupBox Header="单位设置" Margin="0,0,0,10">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="电压量单位：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedChannel.Unit1, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="物理量单位：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedChannel.Unit2, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5"/>
                        </Grid>
                    </GroupBox>

                    <!-- 高级设置 -->
                    <GroupBox Header="高级设置" Margin="0,0,0,10">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="比特位索引：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedChannel.BitIndex, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5" ToolTip="仅在数据类型为比特位时使用"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="版本号格式：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedChannel.VersionFormat, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5" ToolTip="仅在数据类型为版本号时使用"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="字节序：" VerticalAlignment="Center" Margin="0,5"/>
                            <ComboBox Grid.Row="2" Grid.Column="1"
                                     SelectedValue="{Binding SelectedChannel.IsBigEndian}"
                                     SelectedValuePath="Tag"
                                     Height="25" Margin="5">
                                <ComboBox.Items>
                                    <ComboBoxItem Content="小端序 (Little Endian)" Tag="False"/>
                                    <ComboBoxItem Content="大端序 (Big Endian)" Tag="True"/>
                                </ComboBox.Items>
                            </ComboBox>
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>

            <!-- Device 属性编辑区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                         Visibility="{Binding IsDevicePropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel Margin="0,0,20,0">
                    <!-- 设备基本信息 -->
                    <GroupBox Header="设备基本信息" Margin="0,0,0,10">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="设备名称：" VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedDevice.DeviceName, UpdateSourceTrigger=PropertyChanged}"
                                    Height="25" Margin="5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="通道数量：" VerticalAlignment="Center" Margin="0,5"/>
                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="5">
                                <TextBlock Text="{Binding SelectedDevice.Channels.Count}" FontWeight="Bold"/>
                                <TextBlock Text=" 个通道" FontWeight="Bold"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- 设备状态 -->
                    <GroupBox Header="设备状态" Margin="0,0,0,10">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="当前设备：" VerticalAlignment="Center" Margin="0,5"/>
                            <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="{Binding SelectedDevice.IsCurrent}"
                                     IsEnabled="{Binding SelectedDevice.CanClearCurrent}"
                                     Content="设为当前设备" VerticalAlignment="Center" Margin="5"/>

                            <!-- <TextBlock Grid.Row="1" Grid.Column="0" Text="操作：" VerticalAlignment="Center" Margin="0,5"/>
                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                <Button Content="🎯 设为当前" Command="{Binding SetCurrentDeviceCommand}"
                                       Padding="8,4" Margin="0,0,10,0"
                                       Background="Green" Foreground="White" FontSize="12"/>
                                <Button Content="❌取消当前" Command="{Binding ClearCurrentDeviceCommand}"
                                       Padding="8,4"
                                       Background="Orange" Foreground="White" FontSize="12"/>
                            </StackPanel> -->
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>

            <!-- 未选择节点时的提示 -->
            <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <StackPanel.Visibility>
                    <MultiBinding Converter="{StaticResource MultiBooleanToVisibilityConverter}">
                        <Binding Path="IsChannelPropertyPanelVisible"/>
                        <Binding Path="IsDevicePropertyPanelVisible"/>
                    </MultiBinding>
                </StackPanel.Visibility>
                <TextBlock Text="请选择一个节点以编辑其属性" FontSize="14" Foreground="Gray" HorizontalAlignment="Center"/>
                <TextBlock Text="💡 在左侧树形结构中点击通道或设备节点" FontSize="12" Foreground="Gray" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>

            <!-- 保存按钮 -->
            <Button Grid.Row="2" Content="💾 保存所有更改" Command="{Binding SaveChangesCommand}"
                   HorizontalAlignment="Center" Height="Auto" Padding="15,8" Margin="0,0,20,0"
                   Background="DodgerBlue" Foreground="White" FontWeight="Bold"/>
        </Grid>
    </Grid>
</dxwui:NavigationPage>
