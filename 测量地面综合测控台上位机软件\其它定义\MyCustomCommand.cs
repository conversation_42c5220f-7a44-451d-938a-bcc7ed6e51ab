﻿using System;
using System.Linq;
using System.Windows.Input;

namespace 测量地面综合测控台上位机软件
{
    public class MyCustomCommand(Action<object?> doExecute, Func<object?, bool>? checkCanExecute) : ICommand
    {
        public event EventHandler? CanExecuteChanged;

        private readonly Func<object?, bool>? canExecute = checkCanExecute ?? throw new ArgumentNullException(nameof(checkCanExecute));
        private readonly Action<object?> execute = doExecute ?? throw new ArgumentNullException(nameof(doExecute));

        public bool CanExecute(object? parameter)
        {
            //如果没有指定CanExecute方法，则默认可以执行
            if (canExecute == null)
                return true;

            return true;    //TODO:该句仅用于测试，后续需要删除
            //return canExecute(parameter);
        }

        public void Execute(object? parameter)
        {
            execute(parameter);
        }
    }
}
