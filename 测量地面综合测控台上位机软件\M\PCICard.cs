﻿using CommunityToolkit.Mvvm.ComponentModel;
using DevExpress.XtraRichEdit.Model;
using System;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;

namespace 测量地面综合测控台上位机软件
{
    public partial class PCICard(string mcardName, uint cardAddress) : ObservableObject
    {
        #region PXI接口静态库函数导入

        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_OpenDevice();

        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_CloseDevice();





        /// <summary>
        /// 主控卡发送指令
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="ucCmd"></param>
        /// <param name="cmdLength"></param>
        /// <returns></returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_SendCmdByte(int deviceIndex, [In] byte[] ucCmd, int cmdLength);

        /// <summary>
        /// 信源卡发送指令
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="usCmdW"></param>
        /// <returns></returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")] //HNFS_PCI_all_16.dll
        public static partial int HNFS_SendCmdWord(int deviceIndex, [In] byte[] usCmdW);    //针对信号源

        /// <summary>
        /// 主控卡读取数据 传入读数长度
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="uiLen"></param>
        /// <param name="TData"></param>
        /// <returns></returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_ReadData(int deviceIndex, int uiLen, byte[] TData);

        /// <summary>
        /// 信源卡使用写入数据 2K数组，数组长度
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="TData"></param>
        /// <param name="datLen"></param>
        /// <returns></returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        public static partial int HNFS_WriteData(int deviceIndex, byte[] TData, uint datLen);

        /// <summary>
        /// 获取设备类型
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="TypeCode"></param>
        /// <returns></returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_GetDeviceTypeCode(int deviceIndex, uint[] TypeCode);

        /// <summary>
        /// 从指定板卡的地址uiLAddr 读取指定长度数据
        /// </summary>
        /// <param name="deviceIndex">设备号</param>
        /// <param name="uiLAddr">地址</param>
        /// <param name="uiLen">要读取的数据长度</param>
        /// <param name="pucDat"></param>
        /// <returns></returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_ReadDataAny(int deviceIndex, uint uiLAddr, uint uiLen, byte[] pucDat);

        /// <summary>
        /// 向地址写入指定长度数据
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="uiLAddr"></param>
        /// <param name="uiLen"></param>
        /// <param name="pucDat"></param>
        /// <returns></returns>

        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_WriteDataAny(int deviceIndex, uint uiLAddr, uint uiLen, byte[] pucDat);

        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_SendWord(int deviceIndex, [In] byte[] usCmdW);

        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_ReadWord(int deviceIndex, byte[] usCmdW);

        /// <summary>
        /// 指定地址写入两字节指令
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="uiLAddr"></param>
        /// <param name="pucDat"></param>
        /// <returns>成功返回1，失败则返回0</returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_WriteWordByAddr(int deviceIndex, uint uiLAddr, [In] byte[] pucDat);

        /// <summary>
        /// 从指定地址读取两字节数据
        /// </summary>
        /// <param name="deviceIndex"></param>
        /// <param name="uiLAddr"></param>
        /// <param name="pucDat"></param>
        /// <returns>成功返回2 </returns>
        [LibraryImport("DLL/hnfs_pc32_x64v1.dll")]
        protected static partial int HNFS_ReadWordByAddr(int deviceIndex, uint uiLAddr, byte[] pucDat);

        #endregion PXI接口静态库函数导入

        /// <summary>
        /// 板卡名称
        /// </summary>
        [ObservableProperty]
        public string cardName = mcardName;

        [ObservableProperty]
        protected bool _online = false;

        /// <summary>
        /// 板卡索引
        /// </summary>
        public int CardIndex { get; protected set; } = -1;

        /// <summary>
        /// 板卡地址
        /// </summary>
        public uint CardAddress { get; } = cardAddress;

        protected string _errorString = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorString
        {
            get { return _errorString; }
            protected set
            {
                _errorString = value;
                if (!string.IsNullOrEmpty(value))
                {
                    ErrorOccurred?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// 发生错误时触发的事件
        /// </summary>
        public event EventHandler<string>? ErrorOccurred;

        public event EventHandler<string>? InfoOccurred;

        public event EventHandler<string>? WarningOccurred;

        protected readonly object _ioLock = new();





        /// <summary>
        /// 可以读取的数据量 
        /// <para>0为不可读</para>
        /// <para>-1为未连接</para>
        /// <para>-2为读取异常</para>
        /// </summary>
        /// <returns></returns>
        protected virtual int CanRead
        {
            get
            {
                if (!Online)
                {
                    ErrorString = $"{CardName}未连接";
                    return -1;
                }
                byte[] res = new byte[2];
                int readLen = 0;
                lock (_ioLock)
                {
                    readLen = HNFS_ReadWordByAddr(CardIndex, 0x0030, res);

                    //Trace.WriteLine(res.ToHexString());
                }
                if (readLen == 2)
                {
                    if (res[0]==0xFF && res[1] == 0xFF)
                    {
                        Trace.WriteLine("可读标志为FFFF");
                        return 0;
                    }
                    return (res[0] << 8) | res[1];
                }
                else
                {
                    ErrorString = $"从{CardName}：0x0030读取失败,HNFS_ReadWordByAddr的返回值为{readLen}";
                    return -2;
                }
            }
        }

        /// <summary>
        /// 是否可写
        /// </summary>
        /// <returns></returns>
        protected virtual bool CanWrite
        {
            get
            {
                if (!Online)
                {
                    ErrorString = $"{CardName}未连接";
                    return false;
                }
                byte[] res = new byte[2];
                int readLen = 0;
                lock (_ioLock)
                {
                    readLen = HNFS_ReadWordByAddr(CardIndex, 0x0040, res);
                }
                if (readLen == 2)
                    return res[0] == 0xAA && res[1] == 0xAA;
                else
                {
                    ErrorString = $"从{CardName}：0x0040读取失败";
                    return false;
                }
            }
        }


        /// <summary>
        /// 触发信息事件
        /// </summary>
        /// <param name="message">信息内容</param>
        protected virtual void OnInfoOccurred(string message)
        {
            InfoOccurred?.Invoke(this, message);
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        /// <param name="message">错误信息</param>
        protected virtual void OnErrorOccurred(string message)
        {
            ErrorOccurred?.Invoke(this, message);
        }

        /// <summary>
        /// 触发警告事件
        /// </summary>
        /// <param name="message"></param>
        protected virtual void OnWarningOccurred(string message)
        {
            WarningOccurred?.Invoke(this, message);
        }


        /// <summary>
        /// 初始化板卡
        /// </summary>
        /// <returns></returns>
        public bool Init()
        {
            int PCICardNumber = HNFS_OpenDevice();

#if NoCard
            return true;
#else

            if (PCICardNumber < 1)
            {
                ErrorString = $"{CardName}初始化：没有找到任何PCI板卡，请检查连接是否正常或驱动是否安装正确";
                Online = false;
                CardIndex = -1;
                return false;
            }


            uint[] pciCardType = new uint[1];
            for (int i = 0; i < PCICardNumber; i++)
            {
                pciCardType[0] = 0xFFFFFFFF;
                HNFS_GetDeviceTypeCode(i, pciCardType);

                if (pciCardType[0] == CardAddress)
                {
                    Online = true;
                    CardIndex = i;
                    // System.Diagnostics.Trace.TraceInformation($"找到{CardName}：{CardAddress:X8}");
                    InfoOccurred?.Invoke(this, $"找到{CardName}：{CardAddress:X8}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Trace.TraceInformation($"{pciCardType[0]:X8}");
                    App.Log.Debug($"找到板卡{pciCardType[0]:X8}");
                }
            }
            ErrorString = $"没有找到{CardName}，请检查连接是否正常或驱动是否安装正确";
            return false;
#endif
        }



        /// <summary>
        /// 发送指令/数据
        /// </summary>
        /// <param name="data">要发送的指令/数据</param>
        /// <param name="cmdName">指令/数据名称</param>
        /// <returns>-1：不可写</returns>
        /// <exception cref="ArgumentNullException"></exception>
        public virtual int SendData(byte[] data, string? cmdName = null)
        {
            if (data == null || data.Length == 0)
                throw new ArgumentNullException(nameof(data), "要发送的数据不能为空");

            if (data.Length > 2048)
                throw new ArgumentException($"要发送的数据过长({data.Length})");
            int result = 0;
            lock (Global.PciBusLock)
            {
                if (!CanWrite)
                {
                    OnErrorOccurred($"{CardName}：【{cmdName}】发送失败,板卡不可写");
                    return -1;
                }

                //byte[] writeData;

                //if (data.Length % 4 == 0)
                //    writeData = data;
                //else
                //{
                //    int len = data.Length + 1;
                //    while (len % 4 != 0)
                //        len++;

                //    writeData = new byte[len];
                //    Array.Copy(data, writeData, data.Length);
                //}

                lock (_ioLock)
                {
                    result = HNFS_WriteDataAny(CardIndex, 0x1000, (uint)data.Length, data);
                    HNFS_WriteWordByAddr(CardIndex, 0x0060, [0xAA, 0xAA]); //写入写数完成标志
                    if(data.Length<100)
                        App.Log.Debug($"向{CardName}发送数据：{data.ToHexString()}，长度：{data.Length}字节，返回值：{result}");
                    else
                        App.Log.Debug($"向{CardName}发送数据：长度：{data.Length}字节，返回值：{result}");
                }
            }
            if (result == data.Length)
                OnInfoOccurred($"{CardName}：【{cmdName}】发送成功");
            else
                ErrorString = $"{CardName}发送数据异常，预期发送{data.Length}字节，但实际发送{result}字节";
            return result;
        }


        /// <summary>
        /// 读取回令/数据
        /// </summary>
        /// <returns></returns>
        public virtual byte[] ReadData(string? readContext = null, bool showLog = false)
        {
            byte[] readData = [];
            int result;
            int canRead = 0;
            lock (Global.PciBusLock)
            {
                canRead = CanRead;
                if (canRead <= 0)
                {
                    if (showLog)
                        OnErrorOccurred($"{CardName}：读取【{readContext}】失败");

                    return readData;
                }

                //while (canRead % 4 != 0)
                //    canRead++;
                lock (_ioLock)
                {
                    readData = new byte[canRead];
                    result = HNFS_ReadDataAny(CardIndex, 0x2000, (uint)canRead, readData);
                    HNFS_WriteWordByAddr(CardIndex, 0x0050, [0xAA, 0xAA]); //写入读数完成标志
                    if(readData.Length<100)
                        App.Log.Debug($"从{CardName}读取数据：{readData.ToHexString()}，长度：{readData.Length}字节，返回值：{result}");
                    else
                        App.Log.Debug($"从{CardName}读取数据：长度：{readData.Length}字节，返回值：{result}");
                }
            }

            if (result != canRead)
            {
                ErrorString = ($"{CardName}读取数据异常，预期读取{canRead}字节，但实际读取{result}字节");
                return readData.BlockCopy(0, result);
            }
            else
                return readData;
        }





    }
}
