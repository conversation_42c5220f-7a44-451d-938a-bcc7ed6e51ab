﻿using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;

namespace 测量地面综合测控台上位机软件
{
    public class LogHelper : IDisposable
    {
        /// <summary>
        /// 日志级别
        /// </summary>
        public enum LogLevel
        {
            /// <summary>
            /// 所有
            /// </summary>
            ALL = -2147483648,

            /// <summary>
            /// 调试
            /// </summary
            DEBUG = 30000,

            /// <summary>
            /// 信息
            /// </summary>
            INFO = 40000,

            /// <summary>
            /// 警告
            /// </summary>
            WARN = 60000,

            /// <summary>
            /// 错误
            /// </summary>
            ERROR = 70000,

            /// <summary>
            /// 致命错误
            /// </summary>
            //FATAL = 110000,

            /// <summary>
            /// 关闭
            /// </summary>
            OFF = 2147483647
        }

        private class LogInfo
        {
            /// <summary>
            /// 级别
            /// </summary>
            public LogLevel Level { get; set; }
            /// <summary>
            /// 时间
            /// </summary>
            public DateTime Time { get; set; }
            /// <summary>
            /// 内容
            /// </summary>
            public string? Message { get; set; }
            /// <summary>
            /// 调试信息
            /// </summary>
            public string? DebugInfo { get; set; }
        }

        private readonly StringBuilder sb = new();

        private BackgroundWorker? saver;

        private readonly ConcurrentQueue<LogInfo> Queue = new();

        private LogInfo? NoWriteLog;

        private bool needLog = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; } = LogLevel.INFO;

        /// <summary>
        /// 记录调试信息
        /// </summary>
        public bool LogDebugInfo { get; set; } = false;

        /// <summary>
        /// 使用多线程保存（每100ms写一次日志） 设为true后需要调用StartThread
        /// </summary>
        public bool UseThread { get; set; } = false;

        /// <summary>
        /// 日志时间格式
        /// </summary>
        public string DateTimeFormat { get; set; } = "yyyy-MM-dd HH:mm:ss.fff";

        /// <summary>
        /// 获取或设置日志文件的名称(不含扩展名)
        /// </summary>
        public string FileNamePrefix { get; set; } = "Log";

        /// <summary>
        /// 获取或设置日志文件的路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 获取或设置定义日志文件大小
        /// <para>如果该值为非正数，则所有日志都写到一个文件里，不限制日志文件的大小，文件名为"年月日+名称".txt</para>
        /// <para>如果该值为整数，则会在"年月"文件夹下额外创建"日"文件夹，这一天的日志都存在此处，文件名为"年月日+索引+名称".txt，每有一个文件大小达到上限，则索引+1</para>
        /// </summary>
        public int MaxFileSize { get; set; } = 0;

        /// <summary>
        /// 默认构造函数 文件名为年月日Log.txt
        /// </summary>
        public LogHelper() : this("Log", string.Empty)
        {
        }

        /// <summary>
        /// 一个参数构造方法。不创建相对路径（即Logs\\yyyy-MM\\……）
        /// </summary>
        /// <param name="name">日志文件（不含后缀）名称</param>
        public LogHelper(string name) : this(name, string.Empty)
        {
        }

        /// <summary>
        /// 两个参数构造方法。 direct不为空则创建相对路径（即Logs\\direct\\yyyy-MM\\……）
        /// </summary>
        /// <param name="name">日志文件名称（不含扩展名）</param>
        /// <param name="direct">日志文件相对路径</param>
        public LogHelper(string name, string direct)
        {
            Level = LogLevel.ALL;
            DateTimeFormat = "yyyy-MM-dd HH:mm:ss.fff";

            FileNamePrefix = name;
            FilePath = string.Concat(CurrentDir(), "Logs\\");
            if (!direct.IsNullOrEmpty())
                FilePath = string.Concat(FilePath, direct, "\\");
        }

        /// <summary>
        /// 开启线程
        /// </summary>
        public void StartThread()
        {
            if (saver == null)
            {
                saver = new BackgroundWorker();
                saver.DoWork += Saver_DoWork;
                saver.RunWorkerCompleted += Saver_RunWorkerCompleted;
            }

            saver.WorkerSupportsCancellation = true;
            UseThread = true;

            if (!saver.IsBusy)
                saver.RunWorkerAsync();
        }

        private void Saver_RunWorkerCompleted(object? sender, RunWorkerCompletedEventArgs e)
        {
            UseThread = false;
            saver = null;
        }

        /// <summary>
        /// 停止线程
        /// </summary>
        public void StopThread()
        {
            needLog = false;
        }

        #region private

        private void Dispose(bool disposing)
        {
            ReleaseUnmanagedResources();
            if (disposing)
                StopThread();
        }

        /// <summary>
        /// 处理文件夹名称末尾加反斜杠\
        /// </summary>
        /// <param name="path">文件夹名称</param>
        /// <returns>结果</returns>
        private static string DealPath(string path)
        {
            return path.Right(1) == "\\" ? path : path + "\\";
        }

        /// <summary>
        /// 当前程序所在路径
        /// </summary>
        /// <returns></returns>
        private static string CurrentDir()
        {
            return DealPath(AppDomain.CurrentDomain.BaseDirectory);
        }

        private static string StackFrameLocationInfo()
        {
            StackTrace st = new StackTrace(1, true);
            //st.GetFrame(2), 参数：0 当前行数；1 上级函数；2：上上级函数，依次类推
            StackFrame sf = st.GetFrame(2);
            if (sf == null)
                return "UnknownClass";

            string className = Path.GetFileNameWithoutExtension(sf.GetFileName());
            return $"{className},{sf.GetMethod()?.Name},L{sf.GetFileLineNumber()}";
        }

        /// <summary>
        /// 返回path路径下指定日期对应的log文件夹
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="path"></param>
        /// <param name="createDayDir"></param>
        /// <param name="createIfNotExist"></param>
        /// <returns></returns>
        private static string YearMonthFolder(DateTime dt, string path, bool createDayDir = false, bool createIfNotExist = true)
        {
            if (path.IsNullOrEmpty())
                return path;

            string result;
            if (createDayDir)
                result = DealPath(path) + dt.Year.ToString("D4") + "-" + dt.Month.ToString("D2") + "\\" + dt.Day.ToString("D2") + "\\";
            else
                result = DealPath(path) + dt.Year.ToString("D4") + "-" + dt.Month.ToString("D2") + "\\";
            if (createIfNotExist)
            {
                //如果目录不存在则创建该目录
                if (!Directory.Exists(result))
                {
                    try
                    {
                        Directory.CreateDirectory(result);
                    }
                    catch (Exception e) { Console.WriteLine(e); }
                }
            }
            return result;
        }

        /// <summary>
        /// 根据log(的日期)获取对应的日志文件
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        private string GetLogFileName(LogInfo log)
        {
            string fileName;
            if (MaxFileSize > 0)
            {
                string filepath = YearMonthFolder(log.Time, FilePath, true, true);
                int i = 1;
                fileName = string.Concat(filepath, log.Time.ToString("yyyy-MM-dd"), "-", i, "-", FileNamePrefix, ".txt");
                FileInfo infile = new FileInfo(fileName);

                while (true)
                {
                    if (!infile.Exists || infile.Length < MaxFileSize)
                        break;
                    i++;
                    fileName = string.Concat(filepath, log.Time.ToString("yyyy-MM-dd"), "-", i, "-", FileNamePrefix, ".txt");
                    infile = new FileInfo(fileName);
                }
            }
            else
            {
                string filepath = YearMonthFolder(log.Time, FilePath, false, true);
                fileName = string.Concat(filepath, log.Time.ToString("yyyy-MM-dd"), "-", FileNamePrefix, ".txt");
            }

            return fileName;
        }

        private void ReleaseUnmanagedResources()
        {
            saver?.CancelAsync();
        }

        private void Saver_DoWork(object? sender, DoWorkEventArgs e)
        {
            while (needLog)
            {
                if (saver == null)
                    break;
                else if (saver.CancellationPending)
                    break;

                WriteLog();
                Thread.Sleep(100);
            }
            WriteLog();

            e.Cancel = true;
        }

        private void AddLog(LogLevel level, params string[] message)
        {
#if DEBUG
            System.Diagnostics.Trace.WriteLine($"[{DateTime.Now.ToString(DateTimeFormat)}] {level} {string.Join(",", message)}");
#endif

            if (level < Level)
                return;

            LogInfo info = new LogInfo();
            if (LogDebugInfo || level >= LogLevel.WARN)
                info.DebugInfo = StackFrameLocationInfo();

            info.Level = level;
            info.Time = DateTime.Now;
            info.Message = string.Join(",", message);

            if (UseThread)
                Queue.Enqueue(info);
            else
                WriteAtOnce(info);
        }

        private void WriteAtOnce(LogInfo log)
        {
            sb.Clear();
            AddLog(log);

            bool isAdd = false;
            while (!isAdd)
            {
                try
                {
                    string filename = GetLogFileName(log);
                    StreamWriter sw = File.AppendText(filename);
                    sw.WriteLine(sb.ToString());
                    sw.Flush();
                    sw.Close();
                    sw.Dispose();
                    isAdd = true;
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }

        private void AddLog(LogInfo log)
        {
            sb.Append(log.Time.ToString(DateTimeFormat));
            sb.Append('\t');

            sb.Append(log.Level.ToString());
            sb.Append('\t');

            sb.Append(log.Message);

            if (!log.DebugInfo.IsNullOrEmpty())
            {
                sb.Append('\t');
                sb.Append(log.DebugInfo);
            }
        }

        private void WriteLog()
        {
            if (NoWriteLog != null || !Queue.IsEmpty)
            {
                sb.Clear();

                LogInfo log = null;
                if (NoWriteLog == null)
                {
                    while (log == null)
                        Queue.TryDequeue(out log);
                }
                else
                {
                    log = NoWriteLog;
                    NoWriteLog = null;
                }

                try
                {
                    DateTime dt = log.Time;
                    string filename = GetLogFileName(log);
                    AddLog(log);

                    while (!Queue.IsEmpty)
                    {
                        if (Queue.TryDequeue(out log))
                        {
                            if (log.Time.Date == dt.Date)
                            {
                                sb.Append('\r');
                                sb.Append('\n');
                                AddLog(log);
                            }
                            else
                            {
                                NoWriteLog = log;
                                break;
                            }
                        }
                    }

                    StreamWriter sw = File.AppendText(filename);
                    sw.WriteLine(sb.ToString());
                    sw.Flush();
                    sw.Close();
                    sw.Dispose();
                }
                catch (Exception exception)
                {
                    Console.WriteLine(exception);
                }
            }
        }

        #endregion private

        /// <summary>
        /// 释放
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">信息</param>
        public void Debug(params string[] message)
        {
            AddLog(LogLevel.DEBUG, message);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">信息</param>
        public void Info(params string[] message)
        {
            AddLog(LogLevel.INFO, message);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">信息</param>
        public void Warn(params string[] message)
        {
            AddLog(LogLevel.WARN, message);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">信息</param>
        public void Error(params string[] message)
        {
            AddLog(LogLevel.ERROR, message);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">信息</param>
        //public void Fatal(params string[] message)
        //{
        //    AddLog(LogLevel.FATAL, message);
        //}

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="e">错误信息</param>
        public void WarnException(Exception e)
        {
            AddLog(LogLevel.WARN, e.Message.Trim(), e.StackTrace?.Trim());
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="e">错误信息</param>
        public void ErrorException(Exception e)
        {
            AddLog(LogLevel.ERROR, e.Message.Trim(), e.StackTrace?.Trim());
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="e">错误信息</param>
        //public void FatalException(Exception e)
        //{
        //    AddLog(LogLevel.FATAL, e.Message.Trim(), e.StackTrace?.Trim());
        //}
    }
}
