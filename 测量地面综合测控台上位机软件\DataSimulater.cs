﻿global using System.Threading;
using DevExpress.Xpf.Core.Native;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class DataSimulator : IDisposable
    {
        /// <summary>
        /// 帧结构
        /// </summary>
        public Frame FrameStructure { get; set; }

        /// <summary>
        /// 帧计数
        /// </summary>
        private ulong counter = 0;

        /// <summary>
        /// 帧计数最大值
        /// </summary>
        private ulong counterMax = 255;


        public enum SignalType { 随机数, 通道内递增, 通道内递减, 帧内递增, 帧内递减, 正弦波, 三角波, 锯齿波, 方波 }

        /// <summary>
        /// 通道模拟参数
        /// </summary>
        public struct SimulatePara
        {
            /// <summary>
            /// 要模拟的波形
            /// </summary>
            public SignalType SignalType;

            /// <summary>
            /// 要模拟的波形的最大值
            /// </summary>
            public double Maximum;

            /// <summary>
            ///  要模拟的波形的最小值
            /// </summary>
            public double Minimum;
        }

        /// <summary>
        /// 各通道模拟参数
        /// </summary>
        public Dictionary<string, SimulatePara> ChannelPara = [];

        /// <summary>
        /// 根据各通道模拟参数生成的波形
        /// </summary>
        private Dictionary<string, byte[]> SignalData = [];

        /// <summary>
        /// 填充各通道波形数据时的索引
        /// </summary>
        private Dictionary<string, int> SignalDataIndex = [];


        private volatile bool isProcessing = true;

        private Thread? processingThread;

        public delegate void FrameGeneratedHandler(byte[] frame);

        public event FrameGeneratedHandler? OnFrameGenerated;


        public DataSimulator(Frame frame)
        {
            FrameStructure = frame;

            List<Channel> channels = frame.CurrentDeviceChannels?.ToList() ?? [];

            foreach (Channel ch in channels)
            {
                if (ch == null) 
                    continue;

                SimulatePara para = new() { SignalType = SignalType.通道内递增 };
                switch (ch.DataType)
                {
                    case Channel.EnumDataType.有符号整数:
                        para.Maximum = Math.Pow(2, (ch.PointBytes * 8 - 1)) - 1;
                        para.Minimum = -para.Maximum - 1;
                        break;
                    case Channel.EnumDataType.单精度浮点数:
                        para.Maximum = float.MaxValue;
                        para.Minimum = float.MinValue;
                        break;
                    case Channel.EnumDataType.双精度浮点数:
                        para.Maximum = double.MaxValue;
                        para.Minimum = double.MinValue;
                        break;
                    case Channel.EnumDataType.比特位:
                    case Channel.EnumDataType.版本号:
                    case Channel.EnumDataType.自定义:
                    case Channel.EnumDataType.无符号整数:
                        para.Maximum = Math.Pow(2, (ch.PointBytes * 8));
                        para.Minimum = 0;
                        break;
                }

                ChannelPara.Add(ch.ChannelName, para);
            }
        }

        public DataSimulator(Frame frame, Dictionary<string, SimulatePara> chParas)
        {
            FrameStructure = frame;
            ChannelPara.AddRange(chParas);
        }





        public void Init(int defaultSampleCount = 100)
        {
            counter = 0;
            if (FrameStructure.FrameCounterPosition.Count > 0)
            {
                switch (FrameStructure.FrameCounterPosition.Count)
                {
                    case 1:
                        counterMax = 255;
                        break;
                    case 2:
                        counterMax = 65535;
                        break;
                    case 3:
                        counterMax = 16777215;
                        break;
                    default:
                        counterMax = 4294967295;
                        break;
                }
            }

            foreach (var ch in ChannelPara)
            {
                var para = ch.Value;
                double min = para.Minimum;
                double max = para.Maximum;
                int sampleCount;
                double[] values;

                // 获取通道信息
                var channel = FrameStructure.CurrentDeviceChannels?.FirstOrDefault(c => c.ChannelName == ch.Key);

                int pointBytes = channel?.PointBytes ?? 2;
                var dataType = channel?.DataType ?? Channel.EnumDataType.无符号整数;
                bool isBigEndian = channel.IsBigEndian;

                switch (para.SignalType)
                {
                    case SignalType.通道内递增:
                    case SignalType.通道内递减:
                        sampleCount = (int)Math.Min(Math.Abs(max - min), 65536);

                        if (sampleCount < 1) sampleCount = 1;
                        values = new double[sampleCount];
                        if (para.SignalType == SignalType.通道内递增)
                        {
                            for (int i = 0; i < sampleCount; i++)
                                values[i] = min + i;
                        }
                        else
                        {
                            for (int i = 0; i < sampleCount; i++)
                                values[i] = max - i;
                        }
                        break;
                    case SignalType.随机数:
                        sampleCount = defaultSampleCount;
                        values = new double[sampleCount];
                        Random random = new();
                        for (int i = 0; i < sampleCount; i++)
                            values[i] = min + random.NextDouble() * (max - min);
                        break;
                    case SignalType.正弦波:
                        sampleCount = defaultSampleCount;
                        values = SignalGenerator.GenerateSineWave(1, (max - min) / 2, 0, (max + min) / 2, sampleCount, sampleCount);
                        break;
                    case SignalType.三角波:
                        sampleCount = defaultSampleCount;
                        values = new double[sampleCount];
                        SignalGenerator.GenerateTriangleWave(ref values, (max - min) / 2, 0, (max + min) / 2, 1);
                        break;
                    case SignalType.锯齿波:
                        sampleCount = defaultSampleCount;
                        values = new double[sampleCount];
                        SignalGenerator.GenerateSawtoothWave(ref values, (max - min) / 2, 0, (max + min) / 2, 1);
                        break;
                    case SignalType.方波:
                        sampleCount = defaultSampleCount;
                        values = SignalGenerator.GenerateSquareWave(1, (max - min) / 2, 0, (max + min) / 2, sampleCount, sampleCount, 50);
                        break;
                    default:
                        sampleCount = defaultSampleCount;
                        values = new double[sampleCount];
                        break;
                }

                // 转换为字节数组
                byte[] allBytes = new byte[sampleCount * pointBytes];
                for (int i = 0; i < sampleCount; i++)
                {
                    byte[] bytes = ConvertValueToBytes(values[i], dataType, pointBytes, isBigEndian);
                    Buffer.BlockCopy(bytes, 0, allBytes, i * pointBytes, pointBytes);
                }
                SignalData[ch.Key] = allBytes;
                SignalDataIndex[ch.Key] = 0;
            }
        }
        private static byte[] ConvertValueToBytes(double value, Channel.EnumDataType dataType, int pointBytes, bool isBigEndian)
        {
            byte[] bytes;
            switch (dataType)
            {
                case Channel.EnumDataType.有符号整数:
                    long lval = (long)Math.Round(value);
                    bytes = BitConverter.GetBytes(lval);
                    break;
                case Channel.EnumDataType.无符号整数:
                    ulong ulval = (ulong)Math.Round(value);
                    bytes = BitConverter.GetBytes(ulval);
                    break;
                case Channel.EnumDataType.单精度浮点数:
                    bytes = BitConverter.GetBytes((float)value);
                    break;
                case Channel.EnumDataType.双精度浮点数:
                    bytes = BitConverter.GetBytes(value);
                    break;
                default:
                    ulval = (ulong)Math.Round(value);
                    bytes = BitConverter.GetBytes(ulval);
                    break;
            }

            bool reversed = false;

            if ((BitConverter.IsLittleEndian && isBigEndian) || (!BitConverter.IsLittleEndian && !isBigEndian))
            {
                Array.Reverse(bytes);
                reversed = true;
            }
            if (bytes.Length != pointBytes)
            {
                byte[] result = new byte[pointBytes];
                if (bytes.Length > pointBytes)
                {
                    if (reversed)
                        Array.Copy(bytes, bytes.Length - pointBytes, result, 0, pointBytes);
                    else
                        Array.Copy(bytes, 0, result, 0, pointBytes);
                }
                else
                    Array.Copy(bytes, 0, result, pointBytes - bytes.Length, bytes.Length);
                return result;
            }
            return bytes;
        }


        private byte[] GetNextFrame()
        {
            byte[] data = new byte[FrameStructure.FrameLength];

            //填充帧标识
            for (int i = 0; i < FrameStructure.FrameFlagPosition.Count; i++)
                data[FrameStructure.FrameFlagPosition[i]] = FrameStructure.FrameFlag[i];



            //填充通道数据
            foreach (Channel ch in FrameStructure.CurrentDeviceChannels)
            {
                for (int i = 0; i < ch.Position.Count; i++)
                {
                    data[ch.Position[i]] = SignalData[ch.ChannelName][SignalDataIndex[ch.ChannelName]];
                    SignalDataIndex[ch.ChannelName]++;
                    if (SignalDataIndex[ch.ChannelName] == SignalData[ch.ChannelName].Length)
                        SignalDataIndex[ch.ChannelName] = 0;
                }
            }

            //填充帧计数
            if (FrameStructure.FrameCounterPosition.Count > 0)
            {
                byte[] counterData = GetCounter();

                for (int i = 0; i < counterData.Length; i++)
                    data[FrameStructure.FrameCounterPosition[i]] = counterData[i];
            }

            return data;
        }

        private byte[] GetCounter()
        {
            byte[] counterData = new byte[FrameStructure.FrameCounterPosition.Count];

            for (int i = 0; i < counterData.Length; i++)
                counterData[i] = (byte)((counter >> (8 * (counterData.Length - i - 1))) & 0xFF);

            counter++;
            if (counter > counterMax)
                counter = 0;

            return counterData;
        }


        private static ulong ConvertToUnsignedInteger(byte[] buffer, bool isBigEndian)
        {
            switch (buffer.Length)
            {
                case 1:
                    return buffer[0];
                case 2:
                    return isBigEndian ? BitConverter.ToUInt16([.. buffer.Reverse()], 0) : BitConverter.ToUInt16(buffer, 0);
                case 4:
                    return isBigEndian ? BitConverter.ToUInt32([.. buffer.Reverse()], 0) : BitConverter.ToUInt32(buffer, 0);
                case 8:
                    return isBigEndian ? BitConverter.ToUInt64([.. buffer.Reverse()], 0) : BitConverter.ToUInt64(buffer, 0);
                default:
                    if (buffer.Length > 8)
                    {
                        byte[] tempBuffer = new byte[8];
                        Buffer.BlockCopy(buffer, 0, tempBuffer, 0, 8);
                        return isBigEndian ? BitConverter.ToUInt64([.. tempBuffer.Reverse()], 0) : BitConverter.ToUInt64(tempBuffer, 0);
                    }
                    else
                    {
                        byte[] temp;
                        if (!isBigEndian)
                        {
                            temp = [.. buffer.Reverse()];
                        }
                        else
                        {
                            temp = new byte[buffer.Length];
                            Buffer.BlockCopy(buffer, 0, temp, 0, buffer.Length);
                        }

                        ulong result = 0;

                        for (int i = 0; i < temp.Length; i++)
                            result = (result << 8) | temp[i];

                        return result;
                    }
            }
        }


        private void ProcessingLoop()
        {
            isProcessing = true;
            while (isProcessing)
            {
                byte[] oneFrame = GetNextFrame();
                OnFrameGenerated?.Invoke(oneFrame);

                Thread.Sleep(50);
            }
        }

        private void ProcessingLoop2()
        {
            isProcessing = true;
            while (isProcessing)
            {

            }
        }


        public bool StartSimulator(ThreadPriority priority = ThreadPriority.Normal)
        {
            if (processingThread != null)
                return false;

            counter = 0;    //每次模拟器启动时重置帧计数

            processingThread = new Thread(ProcessingLoop)
            {
                IsBackground = true,
                Name = $"仿真{FrameStructure.FrameName}",
                Priority = priority
            };
            processingThread.Start();
            return true;
        }

        public void StopSimulator()
        {
            isProcessing = false;
            processingThread?.Join();
            processingThread = null;
        }
        protected virtual void Dispose(bool disposing)
        {
            isProcessing = false;
            processingThread?.Join();  // 等待线程退出
            GC.SuppressFinalize(this);
        }

        ~DataSimulator()
        {
            Dispose(disposing: false);
        }

        public void Dispose()
        {
            Dispose(disposing: true);
        }
    }
    internal static class SignalGenerator
    {
        /// <summary>
        /// PI*2
        /// </summary>
        public const double PI2 = double.Tau;

        /// <summary>
        /// PI/180.0
        /// </summary>
        public const double Degree =  0.017453292519943295769236907684886127134428718885417d;

        #region 生成正弦波

        /// <summary>
        /// 生成一个正弦波形，可设定正弦波的振幅、初始相位、频率和采样率。
        /// </summary>
        /// <param name="x">返回的正弦波形(长度必须不小于2)</param>
        /// <param name="amplitude">振幅</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="frequency">正弦波的频率(>=0)，以Hz为单位</param>
        /// <param name="samplingRate">采样率(>0)，以S/s为单位</param>
        /// <param name="numberOfCycles">样本点数</param>
        public static double[] GenerateSineWave(double frequency, double amplitude, double phase, double offset, double samplingRate, int numberOfSamples)
        {
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (frequency.CompareTo(double.Epsilon) < 0)
                throw new ArgumentException("正弦波的频率必须大于等于0");
            if (samplingRate.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("采样率必须大于0");
            if (samplingRate <= 2 * frequency)
                throw new ArgumentException("采样率必须大于信号频率的2倍以满足奈奎斯特采样定理");
            if (numberOfSamples <= 0)
                throw new ArgumentException("样本点数必须大于0");

            double[] ret = new double[numberOfSamples];

            // 计算相关参数
            double angleIncrement = PI2 * frequency / samplingRate;  // 每个采样点的相位增量
            double initialPhase = phase * Degree;                    // 初始相位(弧度)

            // 生成正弦波
            for (int i = 0; i < numberOfSamples; i++)
            {
                ret[i] = amplitude * Math.Sin(angleIncrement * i + initialPhase) + offset;
            }

            return ret;
        }

        /// <summary>
        /// 生成一个包含整数个周期的正弦波形，可设定正弦波的振幅、初始相位和周期数。
        /// </summary>
        /// <param name="x">返回的正弦波形(长度必须不小于2)</param>
        /// <param name="amplitude">振幅</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="numberOfCycles">正弦波周期数(>0)</param>
        public static void GenerateSineWave(ref double[] x, double amplitude = 1.0, double phase = 0.0, double offset = 0.0, int numberOfCycles = 1)
        {
            if (x.Length < 2)
                throw new ArgumentException("波形长度不满足");
            if (numberOfCycles <= 0)
                throw new ArgumentException("正弦波周期数≤0");

            double num = PI2 * numberOfCycles / x.Length;
            double num2 = phase * Degree;
            for (int i = 0; i < x.Length; i++)
                x[i] = amplitude * Math.Sin(num * i + num2) + offset;
        }

        #endregion 生成正弦波

        #region 生成方波

        /// <summary>
        /// 生成一个方波波形，可设定方波的振幅、占空比、频率和采样率。
        /// </summary>
        /// <param name="frequency">方波的频率，以Hz为单位</param>
        /// <param name="amplitude">振幅</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="samplingRate">采样率(>2*频率)，以S/s为单位</param>
        /// <param name="numberOfSamples">样本点数(>0)</param>
        /// <param name="dutyCycle">占空百分比(值域为[0,100])</param>
        public static double[] GenerateSquareWave(double frequency, double amplitude, double phase, double offset, double samplingRate, int numberOfSamples, double dutyCycle)
        {
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (frequency.CompareTo(double.Epsilon) < 0)
                throw new ArgumentException("方波频率必须大于等于0");
            if (samplingRate.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("采样率必须大于0");
            if (samplingRate <= 2 * frequency)
                throw new ArgumentException("采样率必须大于信号频率的2倍以满足奈奎斯特采样定理");
            if (!dutyCycle.InRange(double.Epsilon, 100.0))
                throw new ArgumentException("占空比必须在(0,100]范围内");



            if (numberOfSamples <= 0)
                throw new ArgumentException("样本点数必须大于0");

            double[] ret = new double[numberOfSamples];
            // 计算相关参数
            double normalizedFreq = frequency / samplingRate;  // 归一化频率
            double initialPhase = phase * Degree;              // 初始相位(弧度)
            double dutyCycleRatio = dutyCycle / 100.0;        // 占空比比率

            // 生成方波
            for (int i = 0; i < numberOfSamples; i++)
            {
                double currentPhase = normalizedFreq * i + initialPhase;
                double normalizedPhase = currentPhase - Math.Floor(currentPhase);
                ret[i] = (normalizedPhase < dutyCycleRatio) ? amplitude : (-amplitude);
                ret[i] += offset;
            }
            return ret;
        }

        /// <summary>
        /// 生成一个包含整数个周期的方波波形，可设定方波的振幅、初始相位、占空比和周期数。
        /// </summary>
        /// <param name="x">返回的方波波形(长度必须不小于2)</param>
        /// <param name="amplitude">振幅(必须>0)</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="dutyCycle">占空百分比(值域为(0,100])</param>
        /// <param name="offset">偏置</param>
        /// <param name="numberOfCycles">方波周期数(值域为(0,x.Length/2])</param>
        public static void GenerateSquareWave(
            ref double[] x,
            double amplitude = 1.0,
            double phase = 0.0,
            double dutyCycle = 50.0,
            double offset = 0.0,
            int numberOfCycles = 1)
        {
            // 参数验证
            if (x == null)
                throw new ArgumentNullException(nameof(x));
            if (x.Length < 2)
                throw new ArgumentException("波形长度不满足");
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (!dutyCycle.InRange(double.Epsilon, 100.0))
                throw new ArgumentException("占空比必须在(0,100]范围内");
            if (numberOfCycles <= 0 || numberOfCycles * 2 > x.Length)
                throw new ArgumentException("方波周期数必须大于0且不超过数组长度的一半");

            // 计算相关参数
            double cycleRatio = numberOfCycles / (double)x.Length;     // 周期比率
            double initialPhase = phase * Degree;                      // 初始相位(弧度)
            double dutyCycleRatio = dutyCycle / 100.0;                // 占空比比率

            // 生成方波
            for (int i = 0; i < x.Length; i++)
            {
                double currentPhase = cycleRatio * i + initialPhase;
                double normalizedPhase = currentPhase - Math.Floor(currentPhase);
                x[i] = (normalizedPhase < dutyCycleRatio) ? amplitude : (-amplitude);
                x[i] += offset;
            }
        }

        #endregion 生成方波

        #region 生成三角波

        /// <summary>
        /// 生成一个三角波波形，可设定三角波的振幅、初始相位、偏置、频率和采样率。
        /// </summary>
        /// <param name="amplitude">振幅(必须>0)</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="frequency">三角波的频率(>=0)，以Hz为单位</param>
        /// <param name="samplingRate">采样率(>0)，以S/s为单位</param>
        /// <param name="numberOfSamples">样本点数(>0)</param>
        /// <returns>生成的三角波数组</returns>
        public static double[] GenerateTriangleWave(
            double frequency,
            double amplitude,
            double phase,
            double offset,
            double samplingRate,
            int numberOfSamples)
        {
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (frequency.CompareTo(double.Epsilon) < 0)
                throw new ArgumentException("三角波的频率必须大于等于0");
            if (samplingRate.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("采样率必须大于0");
            if (samplingRate <= 2 * frequency)
                throw new ArgumentException("采样率必须大于信号频率的2倍以满足奈奎斯特采样定理");
            if (numberOfSamples <= 0)
                throw new ArgumentException("样本点数必须大于0");

            double[] result = new double[numberOfSamples];

            // 计算相关参数
            double angleIncrement = PI2 * frequency / samplingRate;  // 每个采样点的相位增量
            double initialPhase = (90 + phase) * Degree;            // 初始相位(弧度)，加90度使波形从0开始
            double amplitudeIncrement = 4 * amplitude * frequency / samplingRate; // 幅值增量

            // 生成三角波
            double currentPhase = initialPhase;
            double currentValue = 0;

            for (int i = 0; i < numberOfSamples; i++)
            {
                result[i] = currentValue + offset;

                // 根据当前相位决定是上升还是下降
                currentValue += (currentPhase < Math.PI) ? amplitudeIncrement : -amplitudeIncrement;

                // 更新相位
                currentPhase += angleIncrement;
                if (currentPhase >= PI2)
                    currentPhase -= PI2;
            }

            return result;
        }


        /// <summary>
        /// 生成一个包含整数个周期的三角波波形，可设定三角波的振幅、初始相位、偏置和周期数。
        /// </summary>
        /// <param name="x">返回的三角波波形(长度必须不小于2)</param>
        /// <param name="amplitude">振幅(必须>0)</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="numberOfCycles">三角波周期数(>0且不超过数组长度的一半)</param>
        public static void GenerateTriangleWave(
            ref double[] x,
            double amplitude = 1.0,
            double phase = 0.0,
            double offset = 0.0,
            int numberOfCycles = 1)
        {
            // 参数验证
            if (x == null)
                throw new ArgumentNullException(nameof(x));
            if (x.Length < 2)
                throw new ArgumentException("波形长度不满足");
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (numberOfCycles <= 0 || numberOfCycles * 2 > x.Length)
                throw new ArgumentException("三角波周期数必须大于0且不超过数组长度的一半");

            // 计算相关参数
            double amplitudeIncrement = 4 * amplitude * numberOfCycles / x.Length;  // 幅值增量
            double angleIncrement = PI2 * numberOfCycles / x.Length;                // 相位增量
            double currentPhase = (90 + phase) * Degree;                           // 初始相位(弧度)，加90度使波形从0开始

            // 生成三角波
            double currentValue = 0;
            x[0] = currentValue + offset;

            for (int i = 1; i < x.Length; i++)
            {
                // 根据当前相位决定是上升还是下降
                currentValue += (currentPhase < Math.PI) ? amplitudeIncrement : -amplitudeIncrement;

                // 限制幅值范围
                if (currentValue > amplitude)
                    currentValue = amplitude;
                else if (currentValue < -amplitude)
                    currentValue = -amplitude;

                x[i] = currentValue + offset;

                // 更新相位
                currentPhase += angleIncrement;
                if (currentPhase >= PI2)
                    currentPhase -= PI2;
            }
        }

        #endregion 生成三角波

        #region 生成锯齿波

        /// <summary>
        /// 生成一个锯齿波波形，可设定锯齿波的振幅、初始相位、偏置、频率和采样率。
        /// </summary>
        /// <param name="frequency">锯齿波的频率(>=0)，以Hz为单位</param>
        /// <param name="amplitude">振幅(必须>0)</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="samplingRate">采样率(>2*频率)，以S/s为单位</param>
        /// <param name="numberOfSamples">样本点数(>0)</param>
        /// <returns>生成的锯齿波数组</returns>
        public static double[] GenerateSawtoothWave(
            double frequency,
            double amplitude,
            double phase,
            double offset,
            double samplingRate,
            int numberOfSamples)
        {
            // 参数验证
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (frequency.CompareTo(double.Epsilon) < 0)
                throw new ArgumentException("锯齿波的频率必须大于等于0");
            if (samplingRate.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("采样率必须大于0");
            if (samplingRate <= 2 * frequency)
                throw new ArgumentException("采样率必须大于信号频率的2倍以满足奈奎斯特采样定理");
            if (numberOfSamples <= 0)
                throw new ArgumentException("样本点数必须大于0");

            double[] result = new double[numberOfSamples];
            double period = samplingRate / frequency;  // 每个周期的采样点数
            double initialPhase = phase * Degree;     // 初始相位(弧度)

            for (int i = 0; i < numberOfSamples; i++)
            {
                double t = (i + initialPhase * period / PI2) % period;
                // 修正后的波形生成逻辑
                if (t < period / 2)
                    result[i] = offset + amplitude * (2 * t / period);  // 上升阶段
                else
                    result[i] = offset - amplitude + amplitude * (2 * (t - period / 2) / period);  // 下降阶段
            }

            return result;
        }

        /// <summary>
        /// 生成一个包含整数个周期的锯齿波波形，可设定锯齿波的振幅、初始相位、偏置和周期数。
        /// </summary>
        /// <param name="x">返回的锯齿波波形(长度必须不小于2)</param>
        /// <param name="amplitude">振幅(必须>0)</param>
        /// <param name="phase">初始相位，以角度(Degree)为单位</param>
        /// <param name="offset">偏置</param>
        /// <param name="numberOfCycles">锯齿波周期数(>0且不超过数组长度的一半)</param>
        public static void GenerateSawtoothWave(
            ref double[] x,
            double amplitude = 1.0,
            double phase = 0.0,
            double offset = 0.0,
            int numberOfCycles = 1)
        {
            // 参数验证
            if (x == null)
                throw new ArgumentNullException(nameof(x));
            if (x.Length < 2)
                throw new ArgumentException("波形长度不满足");
            if (amplitude.CompareTo(double.Epsilon) <= 0)
                throw new ArgumentException("振幅必须大于0");
            if (numberOfCycles <= 0 || numberOfCycles * 2 > x.Length)
                throw new ArgumentException("锯齿波周期数必须大于0且不超过数组长度的一半");

            int length = x.Length;
            double period = length / (double)numberOfCycles;
            double initialPhase = phase * Degree;

            for (int i = 0; i < length; i++)
            {
                double t = (i + initialPhase * period / PI2) % period;
                if (t < period / 2)
                    x[i] = offset + amplitude * (2 * t / period);  // 上升阶段
                else
                    x[i] = offset - amplitude + amplitude * (2 * (t - period / 2) / period);  // 下降阶段
            }
        }

        #endregion 生成锯齿波

        #region 生成噪声

        /// <summary>
        /// 生成一个振幅在[–amplitude, amplitude]之间的随机白噪声波形
        /// </summary>
        /// <param name="x">返回的白噪声波形</param>
        /// <param name="amplitude">噪声振幅</param>
        public static void GenerateUniformWhiteNoise(ref double[] x, double amplitude = 1.0)
        {
            if (x == null)
                throw new ArgumentNullException(nameof(x));
            if (amplitude < 0)
                throw new ArgumentException("振幅不能为负数");
            Random random = new Random();
            for (int i = 0; i < x.Length; i++)
                x[i] = amplitude * (random.NextDouble() * 2.0 - 1.0);
        }

        #endregion 生成噪声

        /// <summary>
        /// 生成一个包含狄拉克脉冲的脉冲流信号
        /// </summary>
        /// <param name="x"></param>
        /// <param name="frequency">脉冲频率</param>
        /// <param name="amplitude"></param>
        /// <param name="phase"></param>
        public static void GenerateDiracPulse(ref double[] x, int frequency, double amplitude = 1.0, int phase = 0)
        {
            if (x == null)
                throw new ArgumentNullException(nameof(x));
            if (frequency <= 0)
                throw new ArgumentException("脉冲频率必须大于0");
            if (phase < 0 || phase >= x.Length)
                throw new ArgumentException("相位值超出范围");

            // 先将数组清零
            Array.Clear(x, 0, x.Length);

            // 生成脉冲
            if (amplitude != 0)
            {
                int currentPhase = phase;
                while (currentPhase < x.Length)
                {
                    x[currentPhase] = amplitude;
                    currentPhase += frequency;
                }
            }
        }




        /// <summary>
        /// 判断值是否在范围内
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="minValue">小值（小值必须≤大值）</param>
        /// <param name="maxValue">大值（小值必须≤大值）</param>
        /// <param name="containsMax">范围是否含最大值</param>
        /// <param name="containsMin">范围是否含最小值</param>
        /// <returns>结果</returns>
        public static bool InRange(this double value, double minValue, double maxValue, bool containsMin = true, bool containsMax = true)
        {
            if (containsMin)
            {
                if (containsMax)
                    return value >= minValue && value <= maxValue;
                else
                    return value >= minValue && value < maxValue;
            }
            else
            {
                if (containsMax)
                    return value > minValue && value <= maxValue;
                else
                    return value > minValue && value < maxValue;
            }
        }

    }
}
