﻿<dxwui:NavigationPage xmlns:hc="https://handyorg.github.io/handycontrol" 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
    xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
    xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"            
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    x:Class="测量地面综合测控台上位机软件.Page0"
    mc:Ignorable="d"
    d:DesignHeight="450"
    d:DesignWidth="800">

    <StackPanel Background="White" Name="panel">
        <dxlc:LayoutControl>
            <dxlc:LayoutGroup Header="电源卡配置" View="Group" Orientation="Vertical">
                <dxlc:LayoutGroup Orientation="Vertical">
                    <dxlc:LayoutItem Label="5V电压K">
                        <hc:NumericUpDown   Value="{Binding VoltageK_5V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0" />
                    </dxlc:LayoutItem>
                    <dxlc:LayoutItem Label="5V电压B">
                        <hc:NumericUpDown   Value="{Binding VoltageB_5V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0" />
                    </dxlc:LayoutItem>
                    <dxlc:LayoutItem Label="5V电流K">
                        <hc:NumericUpDown   Value="{Binding CurrentK_5V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0"  />
                    </dxlc:LayoutItem>
                    <dxlc:LayoutItem Label="5V电流B">
                        <hc:NumericUpDown   Value="{Binding CurrentB_5V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0" />
                    </dxlc:LayoutItem>
                </dxlc:LayoutGroup>

                <dxlc:LayoutGroup Orientation="Vertical" Margin="0,20,0,0">
                    <dxlc:LayoutItem Label="28V电压K">
                        <hc:NumericUpDown   Value="{Binding VoltageK_28V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0" />
                    </dxlc:LayoutItem>
                    <dxlc:LayoutItem Label="28V电压B">
                        <hc:NumericUpDown   Value="{Binding VoltageB_28V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0" />
                    </dxlc:LayoutItem>
                    <dxlc:LayoutItem Label="28V电流K">
                        <hc:NumericUpDown   Value="{Binding CurrentK_28V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0"  />
                    </dxlc:LayoutItem>
                    <dxlc:LayoutItem Label="28V电流B">
                        <hc:NumericUpDown   Value="{Binding CurrentB_28V,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,0,5,0" />
                    </dxlc:LayoutItem>
                </dxlc:LayoutGroup>
            </dxlc:LayoutGroup>
        </dxlc:LayoutControl>
        <Button Content="保存" 
                Height="34"
                HorizontalAlignment="Stretch" 
                Margin="30,30,30,0" 
                IsEnabled="{Binding IsChanged}"
                Click="Button_Click" FontSize="16" FontWeight="Bold"
                />
        <!--<TextBlock Text="{Binding SaveResultText}" Foreground="{Binding SaveResultBrush}"  HorizontalAlignment="Center" Margin="0,5,0,0"></TextBlock>-->

    </StackPanel>
</dxwui:NavigationPage>