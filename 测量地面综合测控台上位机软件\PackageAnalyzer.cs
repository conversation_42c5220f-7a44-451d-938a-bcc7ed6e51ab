﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 解析从LVDS卡接收到的存储器打包数据
    /// </summary>
    public class PackageAnalyzer
    {
        /// <summary>
        /// 存储原始数据的缓冲区
        /// </summary>
        private readonly ConcurrentQueue<byte[]> rawDataBuffer = new();

        /// <summary>
        /// 数据缓冲区最大容量
        /// </summary>
        public int MaxBufferSize { get; set; } = 10485760;

        /// <summary>
        /// 原始数据缓冲区当前字节数
        /// </summary>
        private int bufferCurrentSize = 0;

        List<byte> frameBuffer = [];

        /// <summary>
        /// 数据到达通知信号（唤醒解析线程）
        /// </summary>
        private readonly ManualResetEventSlim dataAvailableEvent = new(false);

        /// <summary>
        /// 解析线程控制标志
        /// </summary>
        private volatile bool isProcessing = true;

        /// <summary>
        /// 解析线程
        /// </summary>
        private Thread? processingThread;


        public event EventHandler<byte[]>? OnCCQMNLReceived;
        public event EventHandler<byte[]>? OnCCQStateReceived;
        public event EventHandler<byte[]>? OnFSJReceived;
        public event EventHandler<byte[]>? OnGZReceived;
        public event EventHandler<byte[]>? OnQSSLBReceived;
        public event EventHandler<byte[]>? OnSSKZXTZTReceived;
        //public event EventHandler<byte[]>? OnTYReceived;
        public event EventHandler<byte[]>? OnXTZTReceived;
        public event EventHandler<byte[]>? OnWXReceived;

        public void Reset()
        {
            rawDataBuffer.Clear();
            frameBuffer.Clear();
            bufferCurrentSize = 0;
        }

        /// <summary>
        /// 获取缓冲区中原始数据大小/字节
        /// </summary>
        /// <returns></returns>
        public int GetRawDataSize()
        {
            return bufferCurrentSize;
        }

        /// <summary>
        /// 获取缓冲区中帧数/帧
        /// </summary>
        /// <returns></returns>
        public int GetFrameCount()
        {
            return rawDataBuffer.Count;
        }

        public void ClearRawDataBuffer()
        {
            rawDataBuffer.Clear();
            bufferCurrentSize = 0;
        }

        public void AddRawData(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return;
            }

            if (bufferCurrentSize + data.Length < MaxBufferSize)
            {
                rawDataBuffer.Enqueue(data);
                bufferCurrentSize += data.Length;
            }
            else
            {
                while (!rawDataBuffer.IsEmpty && bufferCurrentSize + data.Length > MaxBufferSize)
                {
                    if (rawDataBuffer.TryDequeue(out byte[] dataDisposed))
                    {
                        bufferCurrentSize -= dataDisposed.Length;
                        Trace.TraceWarning($"LVDS包:原始数据缓冲区堆积过多数据");
                    }
                }

                rawDataBuffer.Enqueue(data);
                bufferCurrentSize += data.Length;
            }
            dataAvailableEvent.Set();
        }

        private void AddFrame(byte[] frame)
        {
            if (frameBuffer.Count > 10000)
            {
                frameBuffer.RemoveRange(0, 1000);
                Trace.TraceWarning($"LVDS包:帧数据缓冲区堆积过多数据");
            }
            frameBuffer.AddRange(frame);
        }


        private void ProcessingLoop()
        {
            List<byte> buffer = [];
            
           

            isProcessing = true;

            while (isProcessing)
            {
                dataAvailableEvent.Wait(TimeSpan.FromMilliseconds(20));
                dataAvailableEvent.Reset();

                while (rawDataBuffer.TryDequeue(out byte[] rawData))
                {
                    buffer.AddRange(rawData);


                    while (buffer.Count > 4)
                    {
                        if (buffer[0] != 0xEB)
                        {
                            buffer.RemoveAt(0);
                            continue;
                        }
                        if (buffer[1] != 0x90 || buffer[4] != 0x01)
                        {
                            buffer.RemoveRange(0, 2);
                            continue;
                        }

                        int frameLen = (buffer[2] << 8) | buffer[3];
                        if (buffer.Count >= frameLen)
                        {
                            if (buffer[frameLen - 2] == 0x14 && buffer[frameLen - 1] == 0x6F)
                            {
                                AddFrame([.. buffer.Skip(9).Take(frameLen - 11)]);
                                buffer.RemoveRange(0, frameLen);

                                while(frameBuffer.Count>=308)
                                {
                                    if (frameBuffer[0] != 0xFD)
                                    {
                                        frameBuffer.RemoveAt(0);
                                        continue;
                                    }

                                    if (frameBuffer[1] != 0xB1)
                                    {
                                        frameBuffer.RemoveRange(0, 2);
                                        continue;
                                    }

                                    if (frameBuffer[306] != 0x85 || frameBuffer[307] != 0x40)
                                    {
                                        frameBuffer.RemoveRange(0, 2);
                                        continue;
                                    }

                                    switch (frameBuffer[2])
                                    {
                                       case 0xA1 :
                                            OnCCQMNLReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0xB2 :
                                            OnWXReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0xC3 :
                                            OnGZReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0xD4 : 
                                            OnFSJReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0xE5 : 
                                            OnCCQStateReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0xF6 : 
                                            OnQSSLBReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0x97 : 
                                            OnXTZTReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                       case 0x88:
                                            OnSSKZXTZTReceived?.Invoke(this, [.. frameBuffer.Skip(6).Take(300)]);
                                            frameBuffer.RemoveRange(0, 308);
                                            break;
                                        default:
                                            frameBuffer.RemoveRange(0, 2);
                                            break;
                                    }
                                }
                            }
                            else
                            {
                                buffer.RemoveRange(0, 2);
                                continue;
                            }
                        }
                        else
                            break;
                    }
                }
            }

        }

        public bool StartAnalyse(ThreadPriority priority = ThreadPriority.Normal)
        {
            if (processingThread != null)
                return false;

            Reset();

            processingThread = new Thread(ProcessingLoop)
            {
                IsBackground = true,
                Name = $"解析LVDS包",
                Priority = priority
            };
            processingThread.Start();
            return true;
        }

        public void StopAnalyse()
        {
            isProcessing = false;
            processingThread?.Join();
            processingThread = null;
        }

        protected virtual void Dispose(bool disposing)
        {
            isProcessing = false;
            processingThread?.Join();  // 等待线程退出
            dataAvailableEvent.Dispose();
            GC.SuppressFinalize(this);
        }

        ~PackageAnalyzer()
        {
            Dispose(disposing: false);
        }
        public void Dispose()
        {
            Dispose(disposing: true);
        }
    }
    }
