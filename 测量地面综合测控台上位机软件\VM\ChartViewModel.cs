﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public partial class ChartViewModel : ObservableObject
    {
        [ObservableProperty]
        public System.Windows.Visibility visibility = System.Windows.Visibility.Collapsed;

        public List<ChSeries> ChSeries { get; private set; }
        public List<ChPane> ChPanes { get; private set; }

        public string FrameName { get; private set; }

        public ChartViewModel()
        {
            ChPane pane1 = new ChPane { ShowXAxis = true, Weight = 4, Title = "通道1" };
            ChPane pane2 = new ChPane { ShowXAxis = true, Weight = 4, Title = "通道2" };

            ChSeries = new List<ChSeries> {
                new ChSeries {
                    ChannelName = "Smartphones",
                    Values = new List<ChInfo> {
                        new ChInfo { time = 2015, value = 11.065},
                        new ChInfo { time = 2014, value = 10.482},
                        new ChInfo { time = 2013, value = 9.607},
                        new ChInfo { time = 2012, value = 8.561},
                        new ChInfo { time = 2011, value = 7.573},
                        new ChInfo { time = 2010, value = 6.101},
                        new ChInfo { time = 2009, value = 5.11},
                        new ChInfo { time = 2008, value = 4.598}
                    },
                    Pane = pane1
                },
                new ChSeries {
                    ChannelName = "Laptops",
                    Values = new List<ChInfo> {
                        new ChInfo { time = 2015, value = 4.383},
                        new ChInfo { time = 2014, value = 4.849},
                        new ChInfo { time = 2013, value = 5.156},
                        new ChInfo { time = 2012, value = 6.203},
                        new ChInfo { time = 2011, value = 6.157},
                        new ChInfo { time = 2010, value = 5.7},
                        new ChInfo { time = 2009, value = 5.231},
                        new ChInfo { time = 2008, value = 5.038}
                    },
                    Pane = pane2
                }
            };
            ChPanes = new List<ChPane> { pane1, pane2 };
        }

        public ChartViewModel(string frameName, List<Channel>? chs)
        {
            FrameName = frameName;
            if (chs == null || chs.Count == 0)
            {
                ChSeries = new List<ChSeries>();
                ChPanes = new List<ChPane>();
                Visibility = System.Windows.Visibility.Collapsed;
                return;
            }
            Visibility = System.Windows.Visibility.Visible;
            ChSeries = new List<ChSeries>();
            ChPanes = new List<ChPane>();
            foreach (var ch in chs)
            {
                ChPane chPane = new ChPane()
                {
                    ShowXAxis = true,
                    ShowYAxis = true,
                    Title = ch.ChannelDescription,
                    XAxisTitle = "时间",
                    YAxisTitle = GetYAxisTitle(ch),
                    XAxisVisible = true,
                    YAxisVisible = true,
                    XAxisLabelsVisible = true,
                    YAxisLabelsVisible = true,
                    XAxisGridLinesVisible = true,
                    YAxisGridLinesVisible = true,
                    XAxisLabelFormat = "",
                    YAxisLabelFormat = GetYAxisFormat(ch),
                    XAxisAutoRange = true,
                    YAxisAutoRange = true
                };

                ChSeries chSeries = new ChSeries()
                {
                    ChannelName = ch.ChannelName,
                    ChannelDescription = ch.ChannelDescription,
                    Pane = chPane,
                    Values = []
                };

                //Random random = new Random();

                //if (ch.ChannelDescription.Contains("电流"))
                //{
                //    for (int i = 0; i < 100; i++)
                //        chSeries.Values.Add(new ChInfo() { time = i, value = random.NextDouble() * 10 });
                //}
                //else
                //{
                //    for (int i = 0; i < 100; i++)
                //        chSeries.Values.Add(new ChInfo() { time = 5 * i * random.NextDouble(), value = 6 * random.NextDouble() * 1000 });
                //}



                ChSeries.Add(chSeries);
                ChPanes.Add(chPane);
            }
        }

        [ObservableProperty]
        public string dataType = "数字量";

        public void Update(Frame frame)
        {
            if (frame.FrameName != FrameName)
                return;

            foreach (var series in ChSeries)
            {
                // 根据DataType选择对应的Dictionary
                Dictionary<string, ConcurrentQueue<Frame.SamplePoint>> targetDictionary = DataType switch
                {
                    "数字量" => frame.AllChannelsSamplesDig,
                    "电压量" => frame.AllChannelsSamplesVol,
                    "物理量" => frame.AllChannelsSamplesPhy,
                    _ => frame.AllChannelsSamplesDig // 默认使用数字量
                };

                // 使用TryGetValue避免双重查找
                if (targetDictionary.TryGetValue(series.ChannelName, out var sampleQueue))
                {
                    // 将SamplePoint转换为ChInfo
                    series.Values = [.. sampleQueue.Select(sp => new ChInfo { time = sp.time, value = sp.value })];
                }
                else
                {
                    // 如果通道不存在，清空数据
                    series.Values.Clear();
                }
            }

            //ChSeries? series = ChSeries.Find(o => o.ChannelName == channelDes);
            //if (series != null)
            //    series.Values = val;
        }

        /// <summary>
        /// 根据通道信息获取Y轴标题
        /// </summary>
        private string GetYAxisTitle(Channel channel)
        {
            if (DataType == "电压量" && !string.IsNullOrEmpty(channel.Unit1))
                return $"电压量 ({channel.Unit1})";
            else if (DataType == "物理量" && !string.IsNullOrEmpty(channel.Unit2))
                return $"物理量 ({channel.Unit2})";
            else if (DataType == "数字量")
                return "数字量";
            else
                return "数值";
        }

        /// <summary>
        /// 根据通道信息获取Y轴格式
        /// </summary>
        private string GetYAxisFormat(Channel channel)
        {
            // 根据通道类型和数据类型返回合适的格式
            if (DataType == "数字量")
                return "F0"; // 整数格式
            else if (DataType == "电压量" || DataType == "物理量")
                return "F3"; // 三位小数
            else
                return "F2"; // 默认两位小数
        }

    }

    public class ChPane
    {
        public bool ShowXAxis { get; set; }
        public bool ShowYAxis { get; set; } = true;
        public string Title { get; set; } = "";
        public byte Weight { get; set; }

        // X轴配置
        public string XAxisTitle { get; set; } = "时间";
        public bool XAxisVisible { get; set; } = true;
        public bool XAxisLabelsVisible { get; set; } = true;
        public bool XAxisGridLinesVisible { get; set; } = true;
        public string XAxisLabelFormat { get; set; } = "";

        // Y轴配置
        public string YAxisTitle { get; set; } = "数值";
        public bool YAxisVisible { get; set; } = true;
        public bool YAxisLabelsVisible { get; set; } = true;
        public bool YAxisGridLinesVisible { get; set; } = true;
        public string YAxisLabelFormat { get; set; } = "F2";

        // 轴范围配置
        public bool XAxisAutoRange { get; set; } = true;
        public double XAxisMinValue { get; set; } = 0;
        public double XAxisMaxValue { get; set; } = 100;

        public bool YAxisAutoRange { get; set; } = true;
        public double YAxisMinValue { get; set; } = 0;
        public double YAxisMaxValue { get; set; } = 100;
    }
    public partial class ChSeries:ObservableObject
    {
        public string ChannelName { get; set; } = "";
        public string ChannelDescription { get; set; } = "";

        [ObservableProperty]
        public List<ChInfo> values = new List<ChInfo>();
        public ChPane Pane { get; set; } = new ChPane();
    }
    public class ChInfo
    {
        public double time { get; set; }
        public double value { get; set; }
    }
}
