﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Linq;
using System.Text;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 测量收发一体机自检信息
    /// </summary>
    public partial class SelfTestInfo_CLSFYTJ : ObservableObject
    {
        public static readonly byte[] FrameHead = [0x5A, 0x54, 0x81, 0x43];

        public static readonly byte[] FrameTail = [0x20, 0x5A, 0xFE];

        #region 自检结果
        [ObservableProperty]
        private string selfTestResult = string.Empty;


        #endregion

        #region 测量收发一体机状态

        /// <summary>
        /// 加密状态
        /// <para>True:密文  False:明文</para>
        /// </summary>
        [ObservableProperty]
        private bool jMState = false;


        /// <summary>
        /// 功放状态
        /// <para>True:工作  False:静默</para>
        /// </summary>
        [ObservableProperty]
        private bool gFState = false;


        /// <summary>
        /// 供电状态
        /// <para>True:供电  False:不供电</para>
        /// </summary>
        [ObservableProperty]
        private bool gDState = false;



        [ObservableProperty]
        /// <summary>
        /// 测量收发一体机设备工作电压K
        /// </summary>
        private decimal cLSFYTJDeviceWorkVoltageK = 1;

        /// <summary>
        /// 测量收发一体机设备工作电压B
        /// </summary>
        [ObservableProperty]
        private decimal cLSFYTJDeviceWorkVoltageB = 0;

        /// <summary>
        /// 测量收发一体机设备工作电压
        /// </summary>
        [ObservableProperty]
        private decimal cLSFYTJDeviceWorkVoltage = decimal.Zero;





        #endregion


        #region 姿态测量发射机状态

        /// <summary>
        /// 锂电池电压K
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryVoltageK = 1;

        /// <summary>
        /// 锂电池电压B
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryVoltageB = 0;

        /// <summary>
        /// 锂电池电压
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryVoltage = decimal.Zero;




        /// <summary>
        /// 锂电池电量K
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryElectricityK = 1;

        /// <summary>
        /// 锂电池电量B
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryElectricityB = 0;

        /// <summary>
        /// 锂电池电量
        /// </summary>
        [ObservableProperty]
        private decimal liBatteryElectricity = decimal.Zero;


        /// <summary>
        /// 姿态测量发射机设备工作电压K
        /// </summary>
        [ObservableProperty]
        private decimal zTCLFSJDeviceWorkVoltageK = 1;

        /// <summary>
        /// 姿态测量发射机设备工作电压B
        /// </summary>
        [ObservableProperty]
        private decimal zTCLFSJDeviceWorkVoltageB = 0;

        /// <summary>
        /// 姿态测量发射机设备工作电压
        /// </summary>
        [ObservableProperty]
        private decimal zTCLFSJDeviceWorkVoltage = decimal.Zero;

        /// <summary>
        /// 设备工作状态
        /// </summary>
        [ObservableProperty]
        private byte deviceWorkState = 0;



        /// <summary>
        /// IMU温度
        /// </summary>
        [ObservableProperty]
        private decimal iMUTemperature = decimal.Zero;




        [ObservableProperty]
        private decimal angleXK = 1;

        [ObservableProperty]
        private decimal angleXB = 0;

        /// <summary>
        /// 俯仰角X
        /// </summary>
        [ObservableProperty]
        private decimal angleX;




        [ObservableProperty]
        private decimal angleYK = 1;

        [ObservableProperty]
        private decimal angleYB = 0;

        /// <summary>
        /// 滚转角Y
        /// </summary>
        [ObservableProperty]
        private decimal angleY;



        [ObservableProperty]
        private decimal angleZK = 1;


        [ObservableProperty]
        private decimal angleZB = 0;

        /// <summary>
        /// 偏航角Z
        /// </summary>
        [ObservableProperty]
        private decimal angleZ;

        [ObservableProperty]
        private decimal accelerationXK = 1;

        [ObservableProperty]
        private decimal accelerationXB = 0;

        /// <summary>
        /// 加速度计X轴数据
        /// </summary>
        [ObservableProperty]
        private decimal accelerationX;


        [ObservableProperty]
        private decimal accelerationYK = 1;

        [ObservableProperty]
        private decimal accelerationYB = 0;

        /// <summary>
        /// 加速度计Y轴数据
        /// </summary>
        [ObservableProperty]
        private decimal accelerationY;





        [ObservableProperty]
        private decimal accelerationZK = 1;
        [ObservableProperty]
        private decimal accelerationZB = 0;

        /// <summary>
        /// 加速度计Z轴数据
        /// </summary>
        [ObservableProperty]
        private decimal accelerationZ;

        #endregion


        [ObservableProperty]
        private string softwareVersion_CLFSYTJ = string.Empty;


        [ObservableProperty]
        private string softwareVersion_ZTCLFSJ = string.Empty;


        /// <summary>
        /// 根据测量收发一体机自检回报帧更新自检信息
        /// </summary>
        /// <param name="data">测量收发一体机自检回报帧</param>
        /// <returns>True:更新信息有效</returns>
        public bool Update(byte[] data)
        {
            if (data == null || data.Length != 39)
            {
                System.Diagnostics.Trace.Assert(data != null && data.Length == 39, "数据长度不足，无法解析帧");

                return false;
            }
            if (data[0] != FrameHead[0] || data[1] != FrameHead[1] || data[2] != FrameHead[2] || data[3] != FrameHead[3])
            {
                System.Diagnostics.Trace.TraceError("帧头不匹配");
                return false;
            }
            if (data[^3] != FrameTail[0] || data[^2] != FrameTail[1] || data[^1] != FrameTail[2])
            {
                System.Diagnostics.Trace.TraceError("帧尾不匹配");
                return false;
            }

            byte checkSum = 0;//TODO:校验和计算范围不明确
            for (int i = 4; i < 34; i++)
            {
                checkSum += data[i];
            }

            if (data[^4] != checkSum)
            {
                System.Diagnostics.Trace.TraceError("校验和不匹配");
                return false;
            }

            if (data[4] == 0xAA)
            {
                SelfTestResult = "串联式遥测装备自检正常";
            }
            else
            {
                StringBuilder sb = new();

                if (!data[4].IsBitEqual1(7))
                    sb.AppendLine("测量收发一体机状态异常");

                if (!data[4].IsBitEqual1(5))
                    sb.AppendLine("姿态测量发射机异常");

                if (!data[4].IsBitEqual1(3))
                    sb.AppendLine("惯组异常");

                if (!data[4].IsBitEqual1(1))
                    sb.AppendLine("锂电池异常");

                SelfTestResult = sb.ToString();
            }


            JMState = data[5].IsBitEqual1(5);
            GFState = data[5].IsBitEqual1(6);
            GDState = data[5].IsBitEqual1(7);

            CLSFYTJDeviceWorkVoltage = data[6] * CLSFYTJDeviceWorkVoltageK + CLSFYTJDeviceWorkVoltageB;

            LiBatteryVoltage = data[7] * LiBatteryVoltageK + LiBatteryVoltageB;
            LiBatteryElectricity = data[8] * LiBatteryElectricityK + LiBatteryElectricityB;
            ZTCLFSJDeviceWorkVoltage = data[9] * ZTCLFSJDeviceWorkVoltageK + ZTCLFSJDeviceWorkVoltageB;

            DeviceWorkState = data[10];
            IMUTemperature = data[11];

            AngleX = ((data[12] << 16) | (data[13] << 8) | data[14]) * AngleXK + AngleXB;
            AngleY = ((data[15] << 16) | (data[16] << 8) | data[17]) * AngleYK + AngleYB;
            AngleZ = ((data[18] << 16) | (data[19] << 8) | data[20]) * AngleZK + AngleZ;

            AccelerationX = ((data[21] << 16) | (data[22] << 8) | data[23]) * AccelerationXK + AccelerationXB;
            AccelerationY = ((data[24] << 16) | (data[25] << 8) | data[26]) * AccelerationYK + AccelerationYB;
            AccelerationZ = ((data[27] << 16) | (data[28] << 8) | data[29]) * AccelerationZK + AccelerationZB;

            SoftwareVersion_CLFSYTJ = $"{data[30]}.{data[31]:D2}";
            SoftwareVersion_ZTCLFSJ = $"{data[32]}.{data[33]:D2}";
            return true;
        }
    }
}
