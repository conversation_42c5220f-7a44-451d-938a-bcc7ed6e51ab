﻿using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace 测量地面综合测控台上位机软件
{
    public partial class PowerCardViewModel : ObservableObject
    {
        /// <summary>
        /// 电源卡
        /// </summary>
        public PCICard PowerCard
        {
            get { return Global.Card_Power; }
        }



        public ICommand PowerCardCommand { get; set; }

        /// <summary>
        /// 电源信息
        /// </summary>
        public PowerInfo PowerInfo
        {
            get;
        }


        [ObservableProperty]
        private bool is28VPowerOn;


        [ObservableProperty]
        private bool is5VPowerOn;

        //private void UpdatePowerStatus()
        //{
        //    Is28VPowerOn = PowerInfo.Voltage28 > 25;
        //    Is5VPowerOn = PowerInfo.Voltage5 > 4;
        //}


        public Timer timer_getPowerCardState;

        private FileStream fileOut;
        BufferedStream bufferStream;
        public PowerCardViewModel(PowerInfo powerInfo)
        {
            PowerCardCommand = new MyCustomCommand(SetPowerCard, IsPowerCardCanExcute);

            PowerInfo = powerInfo;
            PowerInfo.PropertyChanged += PowerInfo_PropertyChanged;

            Directory.CreateDirectory($"{AppDomain.CurrentDomain.BaseDirectory}数据\\电源数据");

            fileOut = new FileStream($"{AppDomain.CurrentDomain.BaseDirectory}数据\\电源数据\\{DateTime.Now:yyyy-MM-dd-HH--mm-ss}.dat", FileMode.Create);
            bufferStream = new BufferedStream(fileOut);

           // timer_getPowerCardState = new Timer(GetPowerCardState, null, 1000, 400);   //TODO:测试完成后取消注释
        }

        ~PowerCardViewModel()
        {
            fileOut?.Close();
        }

        public void StartTimer()
        {
            if (timer_getPowerCardState == null)
            {
                timer_getPowerCardState = new Timer(GetPowerCardState, null, 1000, 400);
            }
        }

        public void StopTimer()
        {
            timer_getPowerCardState?.Dispose();
            timer_getPowerCardState = null;
            fileOut?.Close();
            fileOut = null;
            bufferStream?.Close();
            bufferStream = null;
        }

        private void PowerInfo_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            //if (e.PropertyName == nameof(PowerInfo.Voltage28) || 
            //    e.PropertyName == nameof(PowerInfo.Voltage5)  )
            //{
            //    UpdatePowerStatus();
            //}
        }

        private void GetPowerCardState(object? state)
        {
            if (PowerCard == null)
            {
                //Trace.WriteLine("电源卡为空");
                return;
            }

            if (!PowerCard.Online)
                return;

            byte[] pwData = PowerCard.ReadData("电源卡状态");

            if (pwData.Length > 0)
            {
                //fileOut?.Write(pwData);
                //fileOut?.Flush();
                
                bufferStream.Write(pwData, 0, pwData.Length);
                bufferStream.FlushAsync();
            }

            if (pwData.Length >= 19)
            {
                ReceiveFrame_PowerCard receiveFrame = new(pwData);

                if (receiveFrame.Valid)
                {
                    PowerInfo.Voltage28Raw = receiveFrame.Voltage28;
                    PowerInfo.Current28Raw = receiveFrame.Current28;

                    PowerInfo.Voltage5Raw = receiveFrame.Voltage5;
                    PowerInfo.Current5Raw = receiveFrame.Current5;
                }
            }
        }


        private bool IsPowerCardCanExcute(object? o)
        {
            return PowerCard.Online;
        }


        private void SetPowerCard(object? state)
        {
            if (state == null)
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, "SetPowerCard方法的参数state为null，无法执行指令");

                return;
            }

            switch (state.ToString())
            {
                case "1":
                    SetV28();
                    break;
                case "5":
                    SetV5();
                    break;
                case "2":
                case "3":
                case "4":
                case "6":
                case "7":
                    SendSignal(Convert.ToByte(state));
                    break;
                default:
                    break;
            }
        }


        private void SetV28()
        {
            bool setState;
            string stateStr;

            if (!Is28VPowerOn)
            {
                setState = true;
                stateStr = "28V供电";
            }
            else
            {
                setState = false;
                stateStr = "28V断电";
            }
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, $"发送指令【{stateStr}】");


            SendFrame_PowerCard cmdFrame = new(0x01, Global.FrameCounter_PowerCard++, setState);
            byte[] cmd = cmdFrame.ToBytes();

            int ret = PowerCard.SendData(cmd, stateStr);

            if (ret == cmd.Length)
            {
                Is28VPowerOn = !Is28VPowerOn;
            }

            Task.Run(async () =>
            {
                await Task.Delay(50); // 等待50ms
                GetPowerCardState(null); // 获取电源卡状态
            });


            

        }

        private void SetV5()
        {
            bool setState;
            string stateStr;

            if (!Is5VPowerOn)
            {
                setState = true;
                stateStr = "5V供电";
            }
            else
            {
                setState = false;
                stateStr = "5V断电";
            }
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, $"发送指令【{stateStr}】");

            SendFrame_PowerCard cmdFrame = new(0x05, Global.FrameCounter_PowerCard++, setState);

            byte[] cmd = cmdFrame.ToBytes();
            int ret = PowerCard.SendData(cmd, stateStr);

            if (ret == cmd.Length)
            {
                Is5VPowerOn = !Is5VPowerOn;
            }

            Task.Run(async () =>
            {
                await Task.Delay(50); // 等待50ms
                GetPowerCardState(null); // 获取电源卡状态
            });

        }

        private void SendSignal(byte ch)
        {
            string stateStr = "";
            switch (ch)
            {
                case 0x02:
                    stateStr = "激活信号1";
                    break;
                case 0x03:
                    stateStr = "激活信号2";
                    break;
                case 0x04:
                    stateStr = "起飞信号";
                    break;
                case 0x06:
                    stateStr = "有源信号1";
                    break;
                case 0x07:
                    stateStr = "有源信号2";
                    break;
            }
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, $"发送指令【{stateStr}】");
            SendFrame_PowerCard cmdFrame = new(ch, Global.FrameCounter_PowerCard++, true);
            PowerCard.SendData(cmdFrame.ToBytes(), stateStr);
        }

    }
}
