﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style x:Key="LogListViewItemStyle"  TargetType="ListViewItem">
        <!--默认样式-->
        <Setter Property="Background" Value="{DynamicResource RegionBrush}" />
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Margin" Value="0,0,0,4" />
        <Setter Property="Padding" Value="{StaticResource DefaultControlPadding}" />
        <Setter Property="MinHeight" Value="40" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border CornerRadius="4" x:Name="Bd" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}" SnapsToDevicePixels="true">
                        <GridViewRowPresenter Content="{TemplateBinding Content}" Columns="{TemplateBinding GridView.ColumnCollection}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <!--根据日志类型动态改变颜色-->
        <Style.Triggers>
            <DataTrigger Binding="{Binding Type}" Value="调试">
                <Setter Property="Foreground" Value="Blue"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Type}" Value="信息">
                <Setter Property="Foreground" Value="Black"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Type}" Value="警告">
                <Setter Property="Foreground" Value="Orange"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Type}" Value="错误">
                <Setter Property="Foreground" Value="Red"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding Type}" Value="致命">
                <Setter Property="Foreground" Value="Red"/>
                <Setter Property="FontWeight" Value="Bold"/>
            </DataTrigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{DynamicResource DarkDefaultBrush}" />
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <!--<Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />-->
                <Setter Property="Background" Value="{DynamicResource DarkDefaultBrush}" />
                <!--<Setter Property="TextElement.Foreground" Value="{DynamicResource TextIconBrush}" />-->
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsSelected" Value="true" />
                    <Condition Property="Selector.IsSelectionActive" Value="false" />
                </MultiTrigger.Conditions>
                <!--<Setter Property="Background" Value="{DynamicResource RegionBrush}" />-->
                <Setter Property="Background" Value="{DynamicResource DarkDefaultBrush}" />
                <!--<Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />-->
            </MultiTrigger>

            <Trigger Property="GridView.ColumnCollection" Value="{x:Null}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListViewItem">
                            <Border CornerRadius="4" x:Name="Bd" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}" SnapsToDevicePixels="true">
                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>