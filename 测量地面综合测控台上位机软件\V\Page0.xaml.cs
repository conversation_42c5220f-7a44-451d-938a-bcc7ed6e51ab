﻿using DevExpress.Xpf.WindowsUI;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace 测量地面综合测控台上位机软件
{
    public partial class Page0 : NavigationPage
    {

        private Page0ViewModel ViewModel;
        public Page0()
        {
            InitializeComponent();
            //this.DataContext = App.Current.MainWindow.DataContext;

            var powerInfo = App.ServiceProvider.GetRequiredService<PowerInfo>();
            ViewModel = new Page0ViewModel(powerInfo);
            this.DataContext = ViewModel;
        }

        private async void Button_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                var powerInfo = App.ServiceProvider.GetRequiredService<PowerInfo>();
                ViewModel.UpdatePowerInfoConfig(powerInfo);
                bool result = Global.SavePowerCardConfig();

                if (result)
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, "保存电源卡配置成功");
                else
                    App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, "保存电源卡配置失败");

                #region 动画效果
                var tip = new TextBlock
                {
                    Text = result ? "保存成功" : "保存失败",
                    Foreground = result ? System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Margin = new System.Windows.Thickness(0, 5, 0, 0)
                };

                // 添加上浮变换
                var transform = new TranslateTransform(0, 0);
                tip.RenderTransform = transform;

                // 新tip添加到panel底部
                this.panel.Children.Add(tip);

                // 5秒后淡出并移除
                await Task.Delay(5000);

                // 记录要移除的tip的索引
                int removedIndex = this.panel.Children.IndexOf(tip);

                // 记录tip的高度（确保布局已完成）
                this.panel.UpdateLayout();
                double floatHeight = tip.ActualHeight + tip.Margin.Top + tip.Margin.Bottom;


                // 淡出动画
                var fadeOut = new DoubleAnimation
                {
                    To = 0,
                    Duration = TimeSpan.FromMilliseconds(800),
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
                };
                fadeOut.Completed += (s, _) =>
                {
                    // 记录要上浮的tip
                    var tipsToFloat = new System.Collections.Generic.List<TextBlock>();
                    for (int i = 0; i < removedIndex; i++)
                    {
                        if (this.panel.Children[i] is TextBlock tb)
                            tipsToFloat.Add(tb);
                    }


                    // 移除tip
                    this.panel.Children.Remove(tip);

                    // 让被移除tip之上的所有tip上浮一个高度
                    foreach (var tb in tipsToFloat)
                    {
                        var tbTransform = tb.RenderTransform as TranslateTransform;
                        if (tbTransform == null)
                        {
                            tbTransform = new TranslateTransform(0, 0);
                            tb.RenderTransform = tbTransform;
                        }
                        var floatUp = new DoubleAnimation
                        {
                            To = tbTransform.Y - floatHeight,
                            Duration = TimeSpan.FromMilliseconds(2000),
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };
                        tbTransform.BeginAnimation(TranslateTransform.YProperty, floatUp);
                    }
                };
                tip.BeginAnimation(TextBlock.OpacityProperty, fadeOut);
                #endregion
            }
            catch (Exception ex)
            {
                App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, ex.Message,null, ex);
            }
        }
    }
}
