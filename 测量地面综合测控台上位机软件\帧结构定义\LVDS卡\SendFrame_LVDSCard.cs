﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    class SendFrame_LVDSCard
    {
        /// <summary>
        /// 帧头
        /// </summary>
        public static readonly byte[] FrameHead = [0xEB, 0x90];
        /// <summary>
        /// 帧尾
        /// </summary>
        public static readonly byte[] FrameTail = [0x14, 0x6F];

       

        /// <summary>
        /// 帧长
        /// </summary>
        public ushort FrameLength { get; }

        /// <summary>
        /// 通道号
        /// </summary>
        public const byte ID = 0x01;

        /// <summary>
        /// 帧计数
        /// </summary>
        public byte FrameCounter { get; set; }


        /// <summary>
        /// 数据
        /// </summary>
        public byte[] Data { get; set; } = [];


        public SendFrame_LVDSCard(byte counter, byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                throw new ArgumentException("数据区不能为空");
            }

            FrameCounter = counter;
            Data = data;

            // 计算帧长度
            FrameLength = (ushort)(8 + data.Length);
        }


        /// <summary>
        /// 指令/数据帧转换为字节数组
        /// </summary>
        /// <returns></returns>
        public byte[] ToBytes()
        {
            byte[] bytes = new byte[FrameLength];

            // 填充帧头
            Buffer.BlockCopy(FrameHead, 0, bytes, 0, FrameHead.Length);
            // 填充通道号
            bytes[2] = ID;
            // 填充帧长度
            bytes[3] = (byte)(FrameLength >> 8);
            bytes[4] = (byte)(FrameLength & 0xFF);
            // 填充帧计数
            bytes[5] = FrameCounter ;
            // 填充数据区
            Buffer.BlockCopy(Data, 0, bytes, 6, Data.Length);
            // 填充帧尾
            Buffer.BlockCopy(FrameTail, 0, bytes, FrameLength - 2, FrameTail.Length);
            return bytes;
        }
    }
}
