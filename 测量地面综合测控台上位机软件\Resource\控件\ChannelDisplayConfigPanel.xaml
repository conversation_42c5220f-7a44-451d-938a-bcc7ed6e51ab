﻿<UserControl x:Class="测量地面综合测控台上位机软件.ChannelDisplayConfigPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="300" Width="250"
             DataContext="{Binding RelativeSource={RelativeSource Self}}">
    
    <Border Background="White" BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="10">
        <ScrollViewer>
        <StackPanel>
           
            <TextBlock Text="通道显示配置" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>

            <!-- 数值类型选择 -->
            <Grid Margin="0,5,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Text="数值类型:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cbValueType" SelectedIndex="1">
                    <ComboBoxItem Content="数字量(Hex)" Tag="LastHexValue"/>
                    <ComboBoxItem Content="数字量(Dec)" Tag="LastDecValue"/>
                    <ComboBoxItem Content="电压量" Tag="LastVolValue"/>
                    <ComboBoxItem Content="物理量" Tag="LastPhyValue"/>
                </ComboBox>
            </Grid>


            <!-- 配置应用模式 -->
            <Grid Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Text="应用模式:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cbApplyMode" SelectedIndex="0">
                    <!-- 选项将在代码中动态生成 -->
                </ComboBox>
            </Grid>

            
            
            <!-- 显示内容 -->
            <Grid Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Text="显示内容:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cbDisplayContent" SelectedIndex="1">
                    <ComboBoxItem Content="通道名称" Tag="False"/>
                    <ComboBoxItem Content="通道描述" Tag="True"/>
                </ComboBox>
            </Grid>
            
            <!-- 数值格式 -->
            <Grid Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Text="数值格式:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cbValueFormat" SelectedIndex="0" IsEditable="False">
                    <ComboBoxItem Content="默认" Tag=""/>
                    <ComboBoxItem Content="整数 (F0)" Tag="F0"/>
                    <ComboBoxItem Content="1位小数 (F1)" Tag="F1"/>
                    <ComboBoxItem Content="2位小数 (F2)" Tag="F2"/>
                    <ComboBoxItem Content="3位小数 (F3)" Tag="F3"/>
                    <ComboBoxItem Content="科学计数法 (E2)" Tag="E2"/>
                </ComboBox>
            </Grid>
            
            <!-- 布局设置 -->
            <Expander Header="布局设置" Margin="0,10,0,0" IsExpanded="True">
                <StackPanel Margin="0,5">
                    <!-- 通道列数 -->
                    <Grid Margin="0,3">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="通道列数:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <Slider Grid.Column="1" x:Name="sliderChannelColumns" Minimum="1" Maximum="6" Value="1"
                                TickFrequency="1" IsSnapToTickEnabled="True"/>
                        <TextBlock Grid.Column="2" Text="{Binding Value, ElementName=sliderChannelColumns, StringFormat=F0}"
                                   VerticalAlignment="Center" Margin="5,0,0,0" MinWidth="30"/>
                    </Grid>
                    <TextBlock Text="说明：设置每行显示的通道数量" FontSize="10" Foreground="Gray" Margin="0,0,0,5"/>
                </StackPanel>
            </Expander>

            <!-- 尺寸设置 -->
            <Expander Header="尺寸设置" Margin="0,10,0,0" IsExpanded="True">
                <StackPanel Margin="0,5">
                    <!-- 行高 -->
                    <Grid Margin="0,3">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="行高:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <Slider Grid.Column="1" x:Name="sliderRowHeight" Minimum="20" Maximum="50" Value="25"
                                TickFrequency="5" IsSnapToTickEnabled="True"/>
                        <TextBlock Grid.Column="2" Text="{Binding Value, ElementName=sliderRowHeight, StringFormat=F0}"
                                   VerticalAlignment="Center" Margin="5,0,0,0" MinWidth="30"/>
                    </Grid>
                </StackPanel>
            </Expander>
            
             <!--应用按钮--> 
            <Button x:Name="btnApply" Content="应用设置" Margin="0,10,0,0" Height="30" 
                    Background="#FF007ACC" Foreground="White" Click="BtnApply_Click"/>


            <CheckBox Margin="0,10,10,0" IsChecked="{Binding ParentControl.IsCustomization, Mode=TwoWay}">编辑通道布局</CheckBox>

                
        </StackPanel>
        </ScrollViewer>
    </Border>
</UserControl>
