﻿<dxwui:NavigationPage
    x:Class="测量地面综合测控台上位机软件.MonitorPage1"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
    xmlns:dxc="http://schemas.devexpress.com/winfx/2008/xaml/charts"
    xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
    xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
    xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Unloaded="MonitorPage1_Unloaded">



    <dxwui:NavigationPage.Resources>
        <!-- 引用图表样式 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary  Source="../Resource/样式/ChartStyle.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </dxwui:NavigationPage.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!-- 左侧配置面板 -->
        <Expander
            Grid.Column="0"
            ExpandDirection="Right"
            Header="配置面板"
            IsExpanded="False">
            <!--<Border Background="White" BorderBrush="LightGray" BorderThickness="1">-->
            <local:ChannelDisplayConfigPanel x:Name="configPanel" Width="230" />
            <!--</Border>-->
        </Expander>

        <!-- 中间监控区域和右侧图表面板的组合 -->
        <Grid Grid.Column="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition x:Name="ChartColumn" Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- 中间监控区域 -->
            <ScrollViewer
                Grid.Column="0"
                Background="White"
                HorizontalScrollBarVisibility="Auto"
                VerticalScrollBarVisibility="Auto">
                <dxlc:LayoutControl
                    x:Name="monitorLayoutControl"
                    AllowAvailableItemsDuringCustomization="False"
                    AllowNewItemsDuringCustomization="False"
                    Background="White"
                    IsCustomization="False"
                    Orientation="Horizontal" />
            </ScrollViewer>

            <!-- 拖拽分隔条 -->
            <GridSplitter
                Grid.Column="1"
                Width="5"
                HorizontalAlignment="Center"
                VerticalAlignment="Stretch"
                Background="LightGray"
                DragCompleted="GridSplitter_DragCompleted"
                ResizeBehavior="PreviousAndNext"
                ResizeDirection="Columns"
                ShowsPreview="True" />

            <!-- 右侧图表面板 -->
            <Expander
                Grid.Column="2"
                Collapsed="ChartExpander_Collapsed"
                ExpandDirection="Left"
                Expanded="ChartExpander_Expanded"
                Header="图表面板"
                IsExpanded="True">

                <ScrollViewer HorizontalAlignment="Stretch">
                    <dxlc:LayoutGroup
                        HorizontalAlignment="Stretch"
                        Orientation="Vertical"
                        View="Group">
                        <UniformGrid
                            HorizontalAlignment="Stretch"
                            Columns="1"
                            Name="gridChart">

                            <!-- 统一图表 -->
                            <!--<local:UnifiedChartPage x:Name="unifiedChart"
                                                   HorizontalAlignment="Stretch"
                                                   Height="Auto"
                                                   Margin="5"/>-->

                        </UniformGrid>
                    </dxlc:LayoutGroup>
                </ScrollViewer>
            </Expander>
        </Grid>

    </Grid>

</dxwui:NavigationPage>