﻿using DevExpress.Mvvm.Native;
using DevExpress.Xpf.LayoutControl;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using ComboBox = System.Windows.Controls.ComboBox;
using UserControl = System.Windows.Controls.UserControl;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// ChannelDisplayConfigPanel.xaml 的交互逻辑
    /// </summary>
    public partial class ChannelDisplayConfigPanel : UserControl, INotifyPropertyChanged
    {
        /// <summary>
        /// 配置应用事件
        /// </summary>
        public event EventHandler<ChannelDisplayConfigEventArgs> ConfigApplied;

        /// <summary>
        /// 包含帧通道信息面板的LayoutControl
        /// </summary>
        private LayoutControl? _parentControl;

        /// <summary>
        /// 父控件，用于绑定IsCustomization属性
        /// </summary>
        public LayoutControl? ParentControl
        {
            get => _parentControl;
            set => SetField(ref _parentControl, value);
        }

        /// <summary>
        /// 当前选中的AutoChannelLayoutGroup
        /// </summary>
        private AutoChannelLayoutGroup? _currentLayoutGroup;

        /// <summary>
        /// 所有需要统一配置的AutoChannelLayoutGroup列表
        /// </summary>
        private List<AutoChannelLayoutGroup> _allLayoutGroups = [];


        private bool configChangedByUser = true;


        public ChannelDisplayConfigPanel()
        {
            InitializeComponent();

            // 绑定事件
            cbApplyMode.SelectionChanged += CbApplyMode_SelectionChanged;

            cbValueType.SelectionChanged += CbValueType_SelectionChanged;
            //cbValueType.SelectionChanged += OnConfigChanged;
            //cbDisplayContent.SelectionChanged += OnConfigChanged;
            //cbValueFormat.SelectionChanged += OnConfigChanged;
            //cbValueFormat.LostFocus += OnValueFormatLostFocus; // 监听失去焦点事件
            //sliderChannelColumns.ValueChanged += OnConfigChanged;
            //sliderRowHeight.ValueChanged += OnConfigChanged;
        }

        private void CbValueType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            foreach (var layoutGroup in _allLayoutGroups)
            {
                layoutGroup.ValueBindingPath = GetSelectedTag(cbValueType) ?? "LastDecValue";
            }
        }

        /// <summary>
        /// 数值格式失去焦点事件处理
        /// </summary>
        private void OnValueFormatLostFocus(object sender, RoutedEventArgs e)
        {
            // 当用户在ComboBox中输入自定义格式并失去焦点时触发配置变化
            if (configChangedByUser)
            {
                BtnApply_Click(this, new RoutedEventArgs());
            }
        }

        private void CbApplyMode_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cbApplyMode.SelectedItem != null)
            {
                _currentLayoutGroup = _allLayoutGroups.Find(o => o.Header == (cbApplyMode.SelectedItem as ComboBoxItem).Tag.ToString());

                if (_currentLayoutGroup != null)
                {
                    configChangedByUser = false;

                    //cbValueType.SelectedIndex = SetSelectedByTag(_currentLayoutGroup.ValueBindingPath);

                    SetSelectedByTag(cbValueType, _currentLayoutGroup.ValueBindingPath);

                    cbDisplayContent.SelectedIndex = _currentLayoutGroup.ShowChannelDescription ? 1 : 0;

                    //int index = GetComboIndexByValueFormat(_currentLayoutGroup.ValueFormat);
                    //if (index != -1)
                    //    cbValueFormat.SelectedIndex = index;
                    //else
                    //    cbValueFormat.SelectedValue = _currentLayoutGroup.ValueFormat;

                    SetSelectedByTag(cbValueFormat, _currentLayoutGroup.ValueFormat);

                    sliderChannelColumns.Value = _currentLayoutGroup.ChannelColumns;
                    sliderRowHeight.Value = _currentLayoutGroup.RowHeight;
                    configChangedByUser = true;
                }

            }
        }

        private int GetComboIndexByValueBindingPath(string path)
        {
            switch (path)
            {
                case "LastHexValue":
                    return 0;
                case "LastDecValue":
                    return 1;
                case "LastVolValue":
                    return 2;
                case "LastPhyValue":
                    return 3;
                default:
                    return -1;

            }
        }

        private int GetComboIndexByValueFormat(string format)
        {
            switch (format)
            {
                case "":
                    return 0;
                case "F0":
                    return 1;
                case "F1":
                    return 2;
                case "F2":
                    return 3;
                case "F3":
                    return 4;
                case "E2":
                    return 5;
                default:
                    return -1;
            }
        }


        /// <summary>
        /// 配置变化事件处理
        /// </summary>
        private void OnConfigChanged(object sender, RoutedEventArgs e)
        {
            //if (configChangedByUser)
            //    BtnApply_Click(this, new RoutedEventArgs());
        }



        /// <summary>
        /// 设置当前选中的AutoChannelLayoutGroup
        /// </summary>
        public void SetCurrentLayoutGroup(AutoChannelLayoutGroup layoutGroup)
        {
            _currentLayoutGroup = layoutGroup;
            if (layoutGroup != null)
            {
                // 更新配置面板显示当前控件的配置
                var config = ChannelDisplayConfig.FromLayoutGroup(layoutGroup);
                SetConfig(config);

                // 更新应用模式选择为当前控件
                UpdateApplyModeSelection(layoutGroup.Name);
            }
        }

        /// <summary>
        /// 更新应用模式选择
        /// </summary>
        private void UpdateApplyModeSelection(string? layoutGroupName)
        {
            if (string.IsNullOrEmpty(layoutGroupName))
                return;

            // 查找对应的选项并选中
            for (int i = 0; i < cbApplyMode.Items.Count; i++)
            {
                if (cbApplyMode.Items[i] is ComboBoxItem item &&
                    item.Tag?.ToString() == layoutGroupName)
                {
                    configChangedByUser = false;
                    cbApplyMode.SelectedIndex = i;
                    configChangedByUser = true;
                    break;
                }
            }
        }

        /// <summary>
        /// 设置所有需要统一配置的AutoChannelLayoutGroup列表
        /// </summary>
        public void SetAllLayoutGroups(List<AutoChannelLayoutGroup> layoutGroups)
        {
            _allLayoutGroups = layoutGroups ?? [];
            UpdateApplyModeOptions();
        }

        /// <summary>
        /// 更新应用模式选项
        /// </summary>
        private void UpdateApplyModeOptions()
        {
            cbApplyMode.Items.Clear();

            // 添加统一设置选项
            var allItem = new ComboBoxItem
            {
                Content = "统一修改所有控件",
                Tag = "All"
            };
            cbApplyMode.Items.Add(allItem);

            // 添加各个控件的选项
            foreach (var layoutGroup in _allLayoutGroups)
            {
                var item = new ComboBoxItem
                {
                    //Content = $"仅修改 {layoutGroup.Name ?? "未命名控件"}",
                    //Tag = layoutGroup.Name ?? ""
                };

                System.Windows.Data.Binding binding = new System.Windows.Data.Binding();
                binding.Source = layoutGroup;
                binding.Path = new PropertyPath("Header");

                item.SetBinding(ComboBoxItem.ContentProperty, binding);
                item.SetBinding(ComboBoxItem.TagProperty, binding);

                cbApplyMode.Items.Add(item);
            }



            // 默认选择第一个选项
            if (cbApplyMode.Items.Count > 0)
            {
                configChangedByUser = false;
                cbApplyMode.SelectedIndex = 0;
                configChangedByUser = true;
            }
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object sender, RoutedEventArgs e)
        {
            var config = GetCurrentConfig();

            // 根据应用模式决定应用到哪些控件
            string applyMode = GetSelectedTag(cbApplyMode) ?? "";
            if (applyMode == "All")
            {
                // 统一修改所有控件
                foreach (var layoutGroup in _allLayoutGroups)
                {
                    config.ApplyTo(layoutGroup);
                }
            }
            else
            {
                // 根据控件名称查找并修改指定控件
                var targetLayoutGroup = _allLayoutGroups.Find(lg => lg.Header == applyMode);
                if (targetLayoutGroup != null)
                {
                    config.ApplyTo(targetLayoutGroup);
                }
                //else if (_currentLayoutGroup != null)
                //{
                //    // 如果没找到指定控件，则修改当前选中的控件
                //    config.ApplyTo(_currentLayoutGroup);
                //}
            }

            ConfigApplied?.Invoke(this, new ChannelDisplayConfigEventArgs(config));
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public ChannelDisplayConfig GetCurrentConfig()
        {
            //// 获取数值格式，优先使用用户输入的文本，如果为空则使用选中项的Tag
            //string valueFormat = "";
            //if (!string.IsNullOrWhiteSpace(cbValueFormat.Text))
            //{
            //    // 用户输入了自定义格式
            //    valueFormat = cbValueFormat.Text;
            //}
            //else if (cbValueFormat.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            //{
            //    // 使用预设选项的Tag值
            //    valueFormat = selectedItem.Tag.ToString() ?? "";
            //}

            return new ChannelDisplayConfig
            {
                ValueBindingPath = GetSelectedTag(cbValueType) ?? "LastDecValue",
                ShowChannelDescription = bool.Parse(GetSelectedTag(cbDisplayContent) ?? "True"),
                ValueFormat = GetSelectedTag(cbValueFormat)??"",
                ChannelColumns = (int)(sliderChannelColumns?.Value ?? 1),
                RowHeight = sliderRowHeight?.Value ?? 25
            };
        }




        /// <summary>
        /// 设置配置
        /// </summary>
        public void SetConfig(ChannelDisplayConfig config)
        {
            SetSelectedByTag(cbValueType, config.ValueBindingPath);
            SetSelectedByTag(cbDisplayContent, config.ShowChannelDescription.ToString());
            SetSelectedByTag(cbValueFormat, config.ValueFormat);
            configChangedByUser = false;
            sliderChannelColumns.Value = config.ChannelColumns;
            sliderRowHeight.Value = config.RowHeight;
            configChangedByUser = true;
        }

        /// <summary>
        /// 获取选中项的Tag值
        /// </summary>
        private string GetSelectedTag(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item)
            {
                return item.Tag?.ToString() ?? "";
            }
            return "";
        }

        /// <summary>
        /// 根据Tag值设置选中项
        /// </summary>
        private void SetSelectedByTag(ComboBox comboBox, string tag)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Tag?.ToString() == tag)
                {
                    configChangedByUser = false;
                    comboBox.SelectedItem = item;
                    configChangedByUser = true;
                    break;
                }
            }
        }

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetField<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// 通道显示配置
    /// </summary>
    public class ChannelDisplayConfig
    {
        public string ValueBindingPath { get; set; } = "LastDecValue";
        public bool ShowChannelDescription { get; set; } = true;
        public string ValueFormat { get; set; } = "F2";
        public int ChannelColumns { get; set; } = 1;
        public double RowHeight { get; set; } = 25;

        /// <summary>
        /// 应用配置到AutoChannelLayoutGroup
        /// </summary>
        public void ApplyTo(AutoChannelLayoutGroup layoutGroup)
        {
            if (layoutGroup == null) return;

            layoutGroup.ValueBindingPath = ValueBindingPath;
            layoutGroup.ShowChannelDescription = ShowChannelDescription;
            layoutGroup.ValueFormat = ValueFormat;
            layoutGroup.ChannelColumns = ChannelColumns;
            layoutGroup.RowHeight = RowHeight;
        }

        /// <summary>
        /// 从AutoChannelLayoutGroup获取配置
        /// </summary>
        public static ChannelDisplayConfig FromLayoutGroup(AutoChannelLayoutGroup layoutGroup)
        {
            if (layoutGroup == null) return new ChannelDisplayConfig();

            return new ChannelDisplayConfig
            {
                ValueBindingPath = layoutGroup.ValueBindingPath,
                ShowChannelDescription = layoutGroup.ShowChannelDescription,
                ValueFormat = layoutGroup.ValueFormat,
                ChannelColumns = layoutGroup.ChannelColumns,
                RowHeight = layoutGroup.RowHeight
            };
        }


        public override string ToString()
        {
            return $" 数值类型:{ValueBindingPath},数值格式:{ValueFormat},通道列数:{ChannelColumns},行高:{RowHeight}";
        }
    }

    /// <summary>
    /// 配置应用事件参数
    /// </summary>
    public class ChannelDisplayConfigEventArgs : EventArgs
    {
        public ChannelDisplayConfig Config { get; }

        public ChannelDisplayConfigEventArgs(ChannelDisplayConfig config)
        {
            Config = config;
        }

        public override string ToString()
        {
            return $"数值类型:{Config.ValueBindingPath},数值格式:{Config.ValueFormat},通道列数:{Config.ChannelColumns},行高:{Config.RowHeight}";
        }

    }

    /// <summary>
    /// 布尔值取反转换器
    /// </summary>
    public class BooleanToInverseConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }
}
