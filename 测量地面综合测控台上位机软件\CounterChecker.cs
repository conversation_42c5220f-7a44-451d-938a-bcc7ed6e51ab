﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class CounterChecker
    {
        /// <summary>
        /// 首个帧计数
        /// </summary>
        public ulong FirstCounter { get; private set; }

        /// <summary>
        /// 最后一个帧计数
        /// </summary>
        public ulong LastCounter { get; private set; }

        /// <summary>
        /// 总个数
        /// </summary>
        public ulong TotalCount { get; private set; }

        /// <summary>
        /// 丢帧次数
        /// </summary>
        public ulong LostCount { get; private set; }

        /// <summary>
        /// 重复次数
        /// </summary>
        public ulong RepeatCount { get; private set; }

        /// <summary>
        /// 清零次数
        /// </summary>
        public ulong ZeroCount { get; private set; }

        private StringBuilder detailL = new StringBuilder();

        /// <summary>
        /// 是否在报告中显示清零信息
        /// </summary>
        public bool ShowZeroInfo { get; set; } = true;

        /// <summary>
        /// 是否在报告中显示重复信息
        /// </summary>
        public bool ShowRepeatInfo { get; set; } = true;

        /// <summary>
        /// 丢帧信息
        /// </summary>
        public string Detail_lost
        {
            get
            {
                return detailL.ToString().Trim(new char[] { '\r', '\n' });
            }
        }

        private StringBuilder detailZ = new StringBuilder();

        /// <summary>
        /// 清零信息
        /// </summary>
        public string Detail_zero
        {
            get
            {
                return detailZ.ToString().Trim(new char[] { '\r', '\n' });
            }
        }

        private Dictionary<ulong, ulong> repeatCount = new Dictionary<ulong, ulong>();
        private Dictionary<ulong, List<long>> repeatPosition = new Dictionary<ulong, List<long>>();
        private StringBuilder detailR = new StringBuilder();

        /// <summary>
        /// 重复信息
        /// </summary>
        public string Detail_repeat
        {
            get
            {
                detailR.Clear();
                foreach (var item in repeatCount)
                {
                    detailR.AppendLine(item.Key.ToString(FormattedCounter) + " 重复次数:" + item.Value.ToString());

                    detailR.AppendLine("重复位置：");
                    foreach (long pos in repeatPosition[item.Key])
                        detailR.AppendLine($"{pos}【{pos:X}】");

                }
                return detailR.ToString().Trim(new char[] { '\r', '\n' });
            }
        }

        /// <summary>
        /// 帧计数校验报告
        /// </summary>
        public string Report
        {
            get
            {
                StringBuilder sb = new StringBuilder();

                sb.AppendLine(string.Format("共解析出{0}帧", TotalCount));
                sb.AppendLine(string.Format("首帧计数[H]:{0}", FirstCounter.ToString(FormattedCounter)));
                sb.AppendLine(string.Format("末帧计数[H]:{0}", LastCounter.ToString(FormattedCounter)));
                if (ShowRepeatInfo)
                {
                    sb.AppendLine(string.Format("帧计数重复次数:{0}", RepeatCount));
                    if (RepeatCount > 0)
                        sb.AppendLine(Detail_repeat);
                }
                if (ShowZeroInfo)
                {
                    sb.AppendLine(string.Format("帧计数清零次数:{0}", ZeroCount));
                    if (ZeroCount > 0)
                        sb.AppendLine(Detail_zero);
                }
                sb.AppendLine(string.Format("丢帧次数:{0}", LostCount));
                if (LostCount > 0)
                    sb.AppendLine(Detail_lost);
                return sb.ToString();
            }
        }

        /// <summary>
        /// 帧计数递增值 默认为1
        /// </summary>
        public ulong Interval { get; private set; } = 1;

        /// <summary>
        /// 帧计数输出格式
        /// </summary>
        public string FormattedCounter = "";

        /// <summary>
        /// 帧计数上限
        /// </summary>
        public ulong MaxCounter { get; private set; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="interval">帧计数间隔</param>
        /// <param name="formattedCounter">帧计数输出格式</param>
        /// <param name="maxCounter">帧计数上限</param>
        public CounterChecker(ulong interval = 1, string formattedCounter = "X4", ulong maxCounter = 65535)
        {
            Interval = interval;
            FormattedCounter = formattedCounter;
            MaxCounter = maxCounter;
        }

        /// <summary>
        /// 添加帧计数
        /// </summary>
        /// <param name="counter"></param>
        /// <param name="position">帧在数据文件中的位置</param>
        public void AddCounter(ulong counter, long position)
        {
            if (TotalCount == 0)
            {
                FirstCounter = counter;
            }
            else
            {
                if (counter == LastCounter)
                {
                    RepeatCount++;
                    if (repeatCount.ContainsKey(LastCounter))
                    {
                        repeatCount[LastCounter] = repeatCount[LastCounter] + 1;
                        repeatPosition[LastCounter].Add(position);
                    }
                    else
                    {
                        repeatCount.Add(LastCounter, 1);
                        repeatPosition.Add(LastCounter, new List<long>() { position });
                    }
                }
                else
                {
                    if (counter == 0)
                    {
                        ZeroCount++;
                        detailZ.AppendLine($"帧计数在{LastCounter.ToString(FormattedCounter)}之后清零。位置：{position} ({position:X})");

                        //if (LastCounter != MaxCounter)
                        //{
                        //    LostCount++;
                        //    detailL.AppendLine($"帧计数在{LastCounter.ToString(FormattedCounter)}到{counter.ToString(FormattedCounter)}之间不连续。位置：{position} ({position:X})");
                        //}
                    }
                    else
                    {

                        if (LastCounter + Interval > MaxCounter)
                        {
                            if (counter != Interval - (MaxCounter - LastCounter) - 1)
                            {
                                LostCount++;
                                detailL.AppendLine($"帧计数在{LastCounter.ToString(FormattedCounter)}到{counter.ToString(FormattedCounter)}之间不连续。位置：{position} ({position:X})");
                            }
                            else
                            {
                                ZeroCount++;
                                detailZ.AppendLine($"帧计数在{LastCounter.ToString(FormattedCounter)}之后清零。位置：{position} ({position:X})");
                            }
                        }
                        else if (counter != LastCounter + Interval)
                        {
                            LostCount++;
                            detailL.AppendLine($"帧计数在{LastCounter.ToString(FormattedCounter)}到{counter.ToString(FormattedCounter)}之间不连续。位置：{position} ({position:X})");
                        }

                    }



                }
            }
            LastCounter = counter;
            TotalCount++;
        }

        /// <summary>
        /// 添加帧计数
        /// </summary>
        /// <param name="counter">帧计数数组。高位在前</param>
        public void AddCounter(byte[] counter, long position)
        {
            AddCounter(counter.ToUlong(), position);
        }

        /// <summary>
        /// 添加帧计数
        /// </summary>
        /// <param name="counter"></param>
        public void AddCounter(int counter, long position)
        {
            AddCounter(Convert.ToUInt64(counter), position);
        }

        /// <summary>
        /// 重置状态
        /// </summary>
        public void Reset()
        {
            FirstCounter = 0;
            LastCounter = 0;
            TotalCount = 0;
            ZeroCount = 0;
            LostCount = 0;
            detailL.Clear();
            detailZ.Clear();
            repeatCount.Clear();
            repeatPosition.Clear();
        }
    }
}
