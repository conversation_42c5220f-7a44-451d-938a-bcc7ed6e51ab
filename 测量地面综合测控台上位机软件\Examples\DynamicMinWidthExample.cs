﻿using System;
using System.Windows.Controls;

namespace 测量地面综合测控台上位机软件.Examples
{
    /// <summary>
    /// 动态最小宽度功能示例
    /// </summary>
    //public class DynamicMinWidthExample
    //{
    //    /// <summary>
    //    /// 计算并更新最大宽度的最小值
    //    /// </summary>
    //    /// <param name="labelWidthSlider">标签宽度滑块</param>
    //    /// <param name="valueWidthSlider">值宽度滑块</param>
    //    /// <param name="maxWidthSlider">最大宽度滑块</param>
    //    public static void UpdateMaxWidthMinimum(Slider labelWidthSlider, Slider valueWidthSlider, Slider maxWidthSlider)
    //    {
    //        // 计算单个通道的最小宽度（标签宽度 + 值宽度 + 边距缓冲）
    //        double labelWidth = labelWidthSlider.Value;
    //        double valueWidth = valueWidthSlider.Value;
    //        double marginBuffer = 20; // 边距预留空间
            
    //        double minChannelWidth = labelWidth + valueWidth + marginBuffer;
            
    //        // 设置最大宽度滑块的最小值，确保至少能显示一个完整的通道
    //        double absoluteMinimum = 200; // 绝对最小值
    //        double newMinimum = Math.Max(absoluteMinimum, minChannelWidth);
            
    //        maxWidthSlider.Minimum = newMinimum;
            
    //        // 如果当前值小于新的最小值，则调整当前值
    //        if (maxWidthSlider.Value < newMinimum)
    //        {
    //            maxWidthSlider.Value = newMinimum;
    //        }
            
    //        // 优化刻度间隔
    //        double range = maxWidthSlider.Maximum - maxWidthSlider.Minimum;
    //        maxWidthSlider.TickFrequency = Math.Max(10, range / 20); // 大约20个刻度
    //    }

    //    /// <summary>
    //    /// 创建带有动态最小值功能的配置面板示例
    //    /// </summary>
    //    public static StackPanel CreateDynamicConfigPanel()
    //    {
    //        var panel = new StackPanel();

    //        // 标签宽度滑块
    //        var labelWidthSlider = new Slider
    //        {
    //            Minimum = 50,
    //            Maximum = 200,
    //            Value = 120,
    //            TickFrequency = 10,
    //            IsSnapToTickEnabled = true
    //        };

    //        // 值宽度滑块
    //        var valueWidthSlider = new Slider
    //        {
    //            Minimum = 50,
    //            Maximum = 150,
    //            Value = 80,
    //            TickFrequency = 10,
    //            IsSnapToTickEnabled = true
    //        };

    //        // 最大宽度滑块
    //        var maxWidthSlider = new Slider
    //        {
    //            Minimum = 200,
    //            Maximum = 1200,
    //            Value = 800,
    //            TickFrequency = 50,
    //            IsSnapToTickEnabled = true
    //        };

    //        // 绑定事件处理器
    //        labelWidthSlider.ValueChanged += (s, e) =>
    //        {
    //            UpdateMaxWidthMinimum(labelWidthSlider, valueWidthSlider, maxWidthSlider);
    //            Console.WriteLine($"标签宽度变化: {e.NewValue:F0}, 最大宽度最小值: {maxWidthSlider.Minimum:F0}");
    //        };

    //        valueWidthSlider.ValueChanged += (s, e) =>
    //        {
    //            UpdateMaxWidthMinimum(labelWidthSlider, valueWidthSlider, maxWidthSlider);
    //            Console.WriteLine($"值宽度变化: {e.NewValue:F0}, 最大宽度最小值: {maxWidthSlider.Minimum:F0}");
    //        };

    //        maxWidthSlider.ValueChanged += (s, e) =>
    //        {
    //            Console.WriteLine($"最大宽度设置: {e.NewValue:F0}");
    //        };

    //        // 添加标签和滑块到面板
    //        panel.Children.Add(new Label { Content = "标签宽度:" });
    //        panel.Children.Add(labelWidthSlider);
            
    //        panel.Children.Add(new Label { Content = "值宽度:" });
    //        panel.Children.Add(valueWidthSlider);
            
    //        panel.Children.Add(new Label { Content = "最大宽度:" });
    //        panel.Children.Add(maxWidthSlider);

    //        // 初始化最小值
    //        UpdateMaxWidthMinimum(labelWidthSlider, valueWidthSlider, maxWidthSlider);

    //        return panel;
    //    }

    //    /// <summary>
    //    /// 测试不同配置下的最小值计算
    //    /// </summary>
    //    public static void TestMinimumCalculation()
    //    {
    //        Console.WriteLine("=== 动态最小宽度计算测试 ===");

    //        var testCases = new[]
    //        {
    //            new { Label = 80, Value = 60, Expected = 200, Scenario = "小尺寸控件" },
    //            new { Label = 120, Value = 80, Expected = 220, Scenario = "标准尺寸控件" },
    //            new { Label = 150, Value = 100, Expected = 270, Scenario = "大尺寸控件" },
    //            new { Label = 200, Value = 150, Expected = 370, Scenario = "超大尺寸控件" }
    //        };

    //        foreach (var testCase in testCases)
    //        {
    //            double minChannelWidth = testCase.Label + testCase.Value + 20; // 20为边距
    //            double actualMinimum = Math.Max(200, minChannelWidth);
                
    //            Console.WriteLine($"场景: {testCase.Scenario}");
    //            Console.WriteLine($"  标签宽度: {testCase.Label}px, 值宽度: {testCase.Value}px");
    //            Console.WriteLine($"  计算最小宽度: {minChannelWidth}px");
    //            Console.WriteLine($"  实际最小值: {actualMinimum}px");
    //            Console.WriteLine($"  预期最小值: {testCase.Expected}px");
    //            Console.WriteLine($"  结果: {(actualMinimum == testCase.Expected ? "✓ 通过" : "✗ 失败")}");
    //            Console.WriteLine();
    //        }
    //    }

    //    /// <summary>
    //    /// 计算在给定最大宽度下能容纳的通道数
    //    /// </summary>
    //    public static int CalculateChannelsPerRow(double labelWidth, double valueWidth, double maxWidth)
    //    {
    //        double channelWidth = labelWidth + valueWidth + 20; // 20为边距
    //        int maxChannels = Math.Max(1, (int)(maxWidth / channelWidth));
            
    //        Console.WriteLine($"标签宽度: {labelWidth}px, 值宽度: {valueWidth}px");
    //        Console.WriteLine($"单个通道宽度: {channelWidth}px");
    //        Console.WriteLine($"最大宽度: {maxWidth}px");
    //        Console.WriteLine($"每行可容纳通道数: {maxChannels}");
            
    //        return maxChannels;
    //    }

    //    /// <summary>
    //    /// 演示动态调整的完整流程
    //    /// </summary>
    //    public static void DemonstrateFullWorkflow()
    //    {
    //        Console.WriteLine("=== 动态最小宽度完整演示 ===");

    //        // 模拟滑块
    //        var labelSlider = new Slider { Value = 100 };
    //        var valueSlider = new Slider { Value = 80 };
    //        var maxSlider = new Slider { Minimum = 200, Maximum = 1200, Value = 600 };

    //        Console.WriteLine("初始状态:");
    //        UpdateMaxWidthMinimum(labelSlider, valueSlider, maxSlider);
    //        Console.WriteLine($"最大宽度范围: {maxSlider.Minimum:F0} - {maxSlider.Maximum:F0}, 当前值: {maxSlider.Value:F0}");
    //        Console.WriteLine();

    //        Console.WriteLine("调整标签宽度到150:");
    //        labelSlider.Value = 150;
    //        UpdateMaxWidthMinimum(labelSlider, valueSlider, maxSlider);
    //        Console.WriteLine($"最大宽度范围: {maxSlider.Minimum:F0} - {maxSlider.Maximum:F0}, 当前值: {maxSlider.Value:F0}");
    //        Console.WriteLine();

    //        Console.WriteLine("调整值宽度到120:");
    //        valueSlider.Value = 120;
    //        UpdateMaxWidthMinimum(labelSlider, valueSlider, maxSlider);
    //        Console.WriteLine($"最大宽度范围: {maxSlider.Minimum:F0} - {maxSlider.Maximum:F0}, 当前值: {maxSlider.Value:F0}");
    //        Console.WriteLine();

    //        Console.WriteLine("尝试设置最大宽度为200 (小于最小值):");
    //        maxSlider.Value = 200;
    //        UpdateMaxWidthMinimum(labelSlider, valueSlider, maxSlider);
    //        Console.WriteLine($"最大宽度范围: {maxSlider.Minimum:F0} - {maxSlider.Maximum:F0}, 当前值: {maxSlider.Value:F0}");
    //        Console.WriteLine("注意: 当前值被自动调整到最小值");
    //    }
    //}

    ///// <summary>
    ///// 控制台测试程序
    ///// </summary>
    //public class DynamicMinWidthTestProgram
    //{
    //    public static void Main()
    //    {
    //        DynamicMinWidthExample.TestMinimumCalculation();
    //        Console.WriteLine();
            
    //        DynamicMinWidthExample.DemonstrateFullWorkflow();
    //        Console.WriteLine();
            
    //        Console.WriteLine("=== 通道容纳数计算示例 ===");
    //        DynamicMinWidthExample.CalculateChannelsPerRow(120, 80, 600);
    //        Console.WriteLine();
    //        DynamicMinWidthExample.CalculateChannelsPerRow(150, 100, 800);
            
    //        Console.WriteLine("\n按任意键退出...");
    //        Console.ReadKey();
    //    }
    //}
}
