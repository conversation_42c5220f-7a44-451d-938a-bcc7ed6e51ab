﻿
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public static class Function
    {

        /// <summary>
        /// 大端排序的字节数组转64位无符号整数
        /// </summary>
        /// <param name="array"></param>
        /// <returns></returns>
        public static ulong ToUlong(this byte[] array)
        {
            ulong value = 0;
            int index = 0;
            foreach (byte a in array)
            {
                value = (value << 8) | a;
                ++index;
                if (index == 8)
                    break;
            }
            return value;
        }


        /// <summary>
        /// 复制数组
        /// </summary>
        /// <param name="src">源数组</param>
        /// <param name="srcOffset">源数组起始位置</param>
        /// <param name="count">复制元素数量。 -1表示复制整个数组</param>
        /// <returns>返回复制的数组</returns>
        public static byte[] BlockCopy(this byte[] src, int srcOffset = 0, int count = -1)
        {
            if (count == 0)
                return [];

            if (count < 0)
                count = src.Length - srcOffset;

            byte[] bts = new byte[count];
            Buffer.BlockCopy(src, srcOffset, bts, 0, bts.Length);
            return bts;
        }

        /// <summary>
        /// 字节数组转十六进制字符串，字节间以分隔符分隔
        /// </summary>
        /// <param name="bytes">字节数组</param>
        /// <param name="span">分隔符</param>
        /// <returns>结果</returns>
        public static string ToHexString(this byte[] bytes, string span = " ")
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            StringBuilder sb = new();
            if (string.IsNullOrEmpty(span))
            {
                foreach (byte b in bytes)
                    sb.Append(b.ToString("X2"));
                return sb.ToString();
            }
            else
            {
                foreach (byte b in bytes)
                {
                    sb.Append(b.ToString("X2"));
                    sb.Append(span);
                }
                return sb.ToString()[..(sb.Length - span.Length)];
            }
        }

        public static byte[] ToHexBytes(this string str, char[] separator = null)
        {
            if (str.Length < 1)
                return null;
            if (separator == null || separator.Length == 0)
                separator = [' ', ',', '，'];
            string[] by = str.Replace("\r\n",",").Split(separator, StringSplitOptions.RemoveEmptyEntries);
            List<byte> values = new();
            for (int i = 0; i < by.Length; i++)
            {
                int temp = by[i].Length / 2;
                for (int j = 0; j < temp; j++)
                    values.Add(byte.Parse(by[i].Substring(2 * j, 2), NumberStyles.AllowHexSpecifier));
            }
            return [.. values];
        }

        /// <summary>
        /// 指定的字符串是否为null或空字符串""
        /// </summary>
        /// <param name="s"></param>
        /// <returns></returns>
        public static bool IsNullOrEmpty(this string s)
        {
            return string.IsNullOrEmpty(s);
        }

        public static bool IsNullOrEmpty(this Array source)
        {
            if (source != null)
            {
                return source.Length == 0;
            }

            return true;
        }

        /// <summary>
        /// 字符串右侧按长度截取后的字符串
        /// </summary>
        /// <param name="s">字符串</param>
        /// <param name="length">长度</param>
        /// <returns>字符串</returns>
        public static string Right(this string s, int length)
        {
            if (s.IsNullOrEmpty())
                return s;
            return length >= s.Length ? s : s.Substring(s.Length - length, length);
        }



        /// <summary>
        /// b的指定bit是否为1
        /// </summary>
        /// <param name="b"></param>
        /// <param name="index">bit位置索引，从0开始</param>
        /// <returns></returns>
        public static bool IsBitEqual1(this byte b, int index)
        {
            return (b & (1 << index)) > 0;
        }


        public static byte[] Xor(this byte[] data, int wordLength, int startIndex = 0, int length = -1)
        {
            if (wordLength <= 1)
            {
                return new byte[1] { data.XorEachByte(startIndex, length) };
            }

            if (data.IsNullOrEmpty())
            {
                throw new ArgumentNullException("data");
            }

            if (data.Length <= startIndex || startIndex + length > data.Length || (length > 0 && length < wordLength))
            {
                throw new ArgumentException("索引超出数组范围");
            }

            byte[] array = new byte[wordLength];
            for (int i = 0; i < wordLength; i++)
            {
                array[i] = data[startIndex + i];
            }

            int num = ((length > 0) ? (length / wordLength - 1) : ((data.Length - startIndex) / wordLength - 1));
            for (int i = 0; i < num; i++)
            {
                for (int j = 0; j < wordLength; j++)
                {
                    array[j] ^= data[startIndex + wordLength + i * wordLength + j];
                }
            }

            return array;
        }

        public static byte XorEachByte(this byte[] data, int startIndex = 0, int length = -1)
        {
            if (data.IsNullOrEmpty())
            {
                throw new ArgumentNullException("data");
            }

            if (data.Length <= startIndex || startIndex + length > data.Length)
            {
                throw new ArgumentException("索引超出数组范围");
            }

            byte b = data[startIndex];
            int num = ((length > 0) ? (length - 1) : (data.Length - startIndex - 1));
            for (int i = 0; i < num; i++)
            {
                b ^= data[startIndex + 1 + i];
            }

            return b;
        }


        public static string getMemory(object obj)
        {
            GCHandle handle = GCHandle.Alloc(obj, GCHandleType.WeakTrackResurrection);
            IntPtr addr = GCHandle.ToIntPtr(handle);
            return $"0x{addr.ToString("X")}";
        }

        /// <summary>
        /// 将以字节为单位的文件大小转为以 B/KB/MB/GB 为单位 保留两位小数
        /// </summary>
        /// <param name="size">文件大小</param>
        /// <returns>转换后的格式化字符串</returns>
        public static string ConvertToFileSize(this long size)
        {
            long kb = 1024;
            long mb = 1048576;      //1024*1024
            long gb = 1073741824;   //1024*1024*1024
            if (size >= gb)
                return string.Format("{0:0.##} GB", (float)size / gb);
            else if (size >= mb)
                return string.Format("{0:0.##} MB", (float)size / mb);
            else if (size >= kb)
                return string.Format("{0:0.##} KB", (float)size / kb);
            else if (size > 0)
                return string.Format("{0} B", size);
            else
                return "0.0 B";
        }
    }
}
