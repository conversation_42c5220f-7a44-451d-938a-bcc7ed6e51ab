﻿<UserControl xmlns:hc="https://handyorg.github.io/handycontrol"  x:Class="测量地面综合测控台上位机软件.ChannelDisplayBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
             mc:Ignorable="d" 
             d:DesignHeight="50" d:DesignWidth="200">
    <Grid Background="Transparent" >
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" MinWidth="20"/>
            <ColumnDefinition Width="Auto" MinWidth="20"/>
        </Grid.ColumnDefinitions>

        <TextBox Grid.Column="0" TextWrapping="Wrap" Text="{Binding ChannelName,RelativeSource={RelativeSource AncestorType=UserControl}}" BorderThickness="0" IsReadOnly="True" Background="Transparent"/>
        <TextBox Grid.Column="1" TextWrapping="Wrap" Text="{Binding ChannelValue,RelativeSource={RelativeSource AncestorType=UserControl}}" BorderThickness="0" IsReadOnly="True" Background="Transparent"/>

    </Grid>
</UserControl>
