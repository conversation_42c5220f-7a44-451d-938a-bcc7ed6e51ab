﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dxc="http://schemas.devexpress.com/winfx/2008/xaml/charts"
    xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006">

    <!-- 统一图表控件样式 -->
    <Style x:Key="ChartControlStyle" TargetType="dxc:ChartControl">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="dxc:ChartControl">
                    <dxc:ChartControl MinHeight="200" HorizontalAlignment="Stretch">
                        <dxc:XYDiagram2D EnableAxisXNavigation="False" PaneOrientation="Vertical">

                            <!-- 系列模板 -->
                            <dxc:XYDiagram2D.SeriesItemTemplate>
                                <DataTemplate>
                                    <dxc:LineSeries2D ArgumentDataMember="time" MarkerVisible="False">
                                        <dxc:LineSeries2D.LineStyle>
                                            <dxc:LineStyle Thickness="2" />
                                        </dxc:LineSeries2D.LineStyle>
                                    </dxc:LineSeries2D>
                                </DataTemplate>
                            </dxc:XYDiagram2D.SeriesItemTemplate>

                            <!-- X轴配置 -->
                            <dxc:XYDiagram2D.AxisX>
                                <dxc:AxisX2D GridLinesVisible="True">
                                    <dxc:AxisX2D.WholeRange>
                                        <dxc:Range SideMarginsValue="0" />
                                    </dxc:AxisX2D.WholeRange>
                                </dxc:AxisX2D>
                            </dxc:XYDiagram2D.AxisX>

                            
                            
                            <!-- 主Y轴 -->
                            <dxc:XYDiagram2D.AxisY>
                                <dxc:AxisY2D
                                    Alignment="Near"
                                    GridLinesVisible="True"
                                    Visible="True">

                                    <dxc:AxisY2D.WholeRange>
                                        <dxc:Range dxc:AxisY2D.AlwaysShowZeroLevel="False" />
                                    </dxc:AxisY2D.WholeRange>

                                </dxc:AxisY2D>

                            </dxc:XYDiagram2D.AxisY>

                            <!-- 独立Y轴模板 -->
                            <dxc:XYDiagram2D.SecondaryAxisYItemTemplate>
                                <DataTemplate>
                                    <dxc:SecondaryAxisY2D Alignment="Near" GridLinesVisible="True">

                                        <dxc:SecondaryAxisY2D.WholeRange>
                                            <dxc:Range dxc:AxisY2D.AlwaysShowZeroLevel="False" />
                                        </dxc:SecondaryAxisY2D.WholeRange>
                                    </dxc:SecondaryAxisY2D>
                                </DataTemplate>
                            </dxc:XYDiagram2D.SecondaryAxisYItemTemplate>

                            <!-- 默认面板（隐藏） -->
                            <!--
                            <dxc:XYDiagram2D.DefaultPane>
                                <dxc:Pane Visibility="Collapsed" />
                            </dxc:XYDiagram2D.DefaultPane>-->

                            <!-- Pane布局 -->
                            <dxc:XYDiagram2D.PaneLayout>
                                <dxc:GridLayout>
                                    <dxc:GridLayout.ColumnDefinitions>
                                        <dxc:LayoutDefinition Size="*" />
                                    </dxc:GridLayout.ColumnDefinitions>
                                </dxc:GridLayout>
                            </dxc:XYDiagram2D.PaneLayout>

                            <!-- 面板模板 -->
                            <dxc:XYDiagram2D.PaneItemTemplate>
                                <DataTemplate>
                                    <dxc:Pane>
                                        <dxc:Pane.Title>
                                            <dxc:PaneTitle
                                                FontSize="12"
                                                FontWeight="Bold"
                                                Visible="True" />
                                        </dxc:Pane.Title>
                                        <dxc:Pane.AxisXScrollBarOptions>
                                            <dxc:ScrollBarOptions Visible="False" />
                                        </dxc:Pane.AxisXScrollBarOptions>
                                    </dxc:Pane>
                                </DataTemplate>
                            </dxc:XYDiagram2D.PaneItemTemplate>

                        </dxc:XYDiagram2D>

                        <!-- 图例 -->
                        <dxc:ChartControl.Legend>
                            <dxc:Legend
                                HorizontalPosition="Center"
                                Orientation="Horizontal"
                                VerticalPosition="BottomOutside" />
                        </dxc:ChartControl.Legend>

                    </dxc:ChartControl>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>