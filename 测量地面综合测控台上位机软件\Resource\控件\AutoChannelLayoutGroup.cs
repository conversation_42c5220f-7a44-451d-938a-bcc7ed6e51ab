﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.LayoutControl;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 自动通道布局组，根据给定的Channel列表自动生成TextBlock
    /// </summary>
    public class AutoChannelLayoutGroup : LayoutGroup
    {
        private ChannelLayoutViewModel _viewModel;


        static AutoChannelLayoutGroup()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(AutoChannelLayoutGroup), 
                new FrameworkPropertyMetadata(typeof(LayoutGroup)));
        }

        public AutoChannelLayoutGroup()
        {
            _viewModel = new ChannelLayoutViewModel();
            _viewModel.LayoutChanged += OnLayoutChanged;
            
            // 设置默认属性
            View = LayoutGroupView.GroupBox;
            IsCollapsible = true;
        }

        #region 依赖属性

        /// <summary>
        /// 通道列表依赖属性
        /// </summary>
        public static readonly DependencyProperty ChannelsProperty =
            DependencyProperty.Register(nameof(Channels), typeof(IEnumerable<Channel>),
                typeof(AutoChannelLayoutGroup),
                new FrameworkPropertyMetadata(null, OnChannelsChanged));

        /// <summary>
        /// 通道列表
        /// </summary>
        public IEnumerable<Channel>? Channels
        {
            get { return (IEnumerable<Channel>?)GetValue(ChannelsProperty); }
            set { SetValue(ChannelsProperty, value); }
        }

        /// <summary>
        /// 值绑定路径依赖属性
        /// </summary>
        public static readonly DependencyProperty ValueBindingPathProperty =
            DependencyProperty.Register(nameof(ValueBindingPath), typeof(string), 
                typeof(AutoChannelLayoutGroup), 
                new FrameworkPropertyMetadata("LastDecValue", OnValueBindingPathChanged));

        /// <summary>
        /// 值绑定路径（LastHexValue, LastDecValue, LastVolValue, LastPhyValue）
        /// </summary>
        public string ValueBindingPath
        {
            get { return (string)GetValue(ValueBindingPathProperty); }
            set { SetValue(ValueBindingPathProperty, value); }
        }



        /// <summary>
        /// 是否显示通道描述依赖属性
        /// </summary>
        public static readonly DependencyProperty ShowChannelDescriptionProperty =
            DependencyProperty.Register(nameof(ShowChannelDescription), typeof(bool), 
                typeof(AutoChannelLayoutGroup), 
                new FrameworkPropertyMetadata(false, OnShowChannelDescriptionChanged));

        /// <summary>
        /// 是否显示通道描述（否则显示通道名称）
        /// </summary>
        public bool ShowChannelDescription
        {
            get { return (bool)GetValue(ShowChannelDescriptionProperty); }
            set { SetValue(ShowChannelDescriptionProperty, value); }
        }

        /// <summary>
        /// 数值格式化字符串依赖属性
        /// </summary>
        public static readonly DependencyProperty ValueFormatProperty =
            DependencyProperty.Register(nameof(ValueFormat), typeof(string), 
                typeof(AutoChannelLayoutGroup), 
                new FrameworkPropertyMetadata("F2", OnValueFormatChanged));

        /// <summary>
        /// 数值格式化字符串
        /// </summary>
        public string ValueFormat
        {
            get { return (string)GetValue(ValueFormatProperty); }
            set { SetValue(ValueFormatProperty, value); }
        }

        /// <summary>
        /// 通道列数依赖属性
        /// </summary>
        public static readonly DependencyProperty ChannelColumnsProperty =
            DependencyProperty.Register(nameof(ChannelColumns), typeof(int),
                typeof(AutoChannelLayoutGroup),
                new FrameworkPropertyMetadata(1, OnChannelColumnsChanged));

        /// <summary>
        /// 通道列数
        /// </summary>
        public int ChannelColumns
        {
            get { return (int)GetValue(ChannelColumnsProperty); }
            set { SetValue(ChannelColumnsProperty, value); }
        }

        /// <summary>
        /// 行高依赖属性
        /// </summary>
        public static readonly DependencyProperty RowHeightProperty =
            DependencyProperty.Register(nameof(RowHeight), typeof(double),
                typeof(AutoChannelLayoutGroup),
                new FrameworkPropertyMetadata(25.0, OnRowHeightChanged));

        /// <summary>
        /// 行高
        /// </summary>
        public double RowHeight
        {
            get { return (double)GetValue(RowHeightProperty); }
            set { SetValue(RowHeightProperty, value); }
        }





        #endregion

        #region 属性变化处理

        private static void OnChannelsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AutoChannelLayoutGroup control)
            {
                control._viewModel.SetChannels(e.NewValue as IEnumerable<Channel>);
                control.RefreshLayout();
            }
        }

        private static void OnValueBindingPathChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AutoChannelLayoutGroup control)
            {
                control._viewModel.ValueBindingPath = e.NewValue as string ?? "LastDecValue";
            }
        }



        private static void OnShowChannelDescriptionChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AutoChannelLayoutGroup control)
            {
                control._viewModel.ShowChannelDescription = (bool)e.NewValue;
            }
        }

        private static void OnValueFormatChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AutoChannelLayoutGroup control)
            {
                control._viewModel.ValueFormat = e.NewValue as string ?? "F2";
            }
        }

        private static void OnRowHeightChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AutoChannelLayoutGroup control)
            {
                control._viewModel.RowHeight = (double)e.NewValue;
            }
        }

        private static void OnChannelColumnsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AutoChannelLayoutGroup control)
            {
                control._viewModel.ChannelColumns = (int)e.NewValue;
            }
        }

        #endregion

        /// <summary>
        /// 布局变化处理
        /// </summary>
        private void OnLayoutChanged(object sender, EventArgs e)
        {
            RefreshLayout();
        }

        /// <summary>
        /// 刷新布局
        /// </summary>
        public void RefreshLayout()
        {
            if (IsLoaded)
            {
                _viewModel.GenerateLayout(this);

                //_viewModel.AddChannelItem(this);
            }
        }

        /// <summary>
        /// 控件加载完成
        /// </summary>
        protected override void OnLoaded()
        {
            base.OnLoaded();
            RefreshLayout();
        }

        /// <summary>
        /// 设置通道列表的便捷方法
        /// </summary>
        /// <param name="channels">通道列表</param>
        /// <param name="bindingPath">绑定路径</param>
        public void SetChannels(IEnumerable<Channel>? channels, string bindingPath = "LastDecValue")
        {
            Channels = channels;
            ValueBindingPath = bindingPath;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        protected override void OnUnloaded()
        {
            if (_viewModel != null)
            {
                _viewModel.LayoutChanged -= OnLayoutChanged;
            }
            base.OnUnloaded();
        }
    }



}
