﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// WindowDownload.xaml 的交互逻辑
    /// </summary>
    public partial class WindowDownload : Window
    {
        public WindowDownload()
        {
            InitializeComponent();
            this.DataContext = Global.Card_LVDS;
            Global.Card_LVDS.DataDownloadCompleted += LVDSCard_DataDownloadCompleted;
        }

        private void LVDSCard_DataDownloadCompleted(object? sender, EventArgs e)
        {
            MessageBox.Show("数据下载完成");
            if(File.Exists(savedFilePath.Text))
                Process.Start("explorer.exe", new FileInfo(savedFilePath.Text).DirectoryName);
        }

        private void CustomColorProgressButton_Click(object sender, RoutedEventArgs e)
        {
#if !DEBUG
            if (Global.Card_LVDS == null)
            {
                MessageBox.Show("板卡未初始化");
                return;
            }
#endif
            if (Global.Card_LVDS.IsDownloading)
            {
                Global.Card_LVDS.StopDownload();
            }
            else
            {
                string defaultDir = $"{AppDomain.CurrentDomain.BaseDirectory}数据\\下载数据";

                if (!Directory.Exists(defaultDir))
                {
                    Directory.CreateDirectory(defaultDir);
                }
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Title = "保存数据文件",
                    Filter = "数据文件 (*.dat)|*.dat|所有文件 (*.*)|*.*",
                    FileName = $"下载数据_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.dat",
                    DefaultDirectory = defaultDir
                };
                if (saveFileDialog.ShowDialog() != true)
                {
                    return;
                }
                //savedFilePath.Text = saveFileDialog.FileName;

                Global.Card_LVDS.SaveDataFileName = saveFileDialog.FileName;

                if (rdb1.IsChecked == true)
                {
                    Global.Card_LVDS.MaxReadDataMB = 102400;//100GB  TODO:
                    Global.Card_LVDS.CurrentReadDataMB = 102400;
                }
                else
                {
                    Global.Card_LVDS.MaxReadDataMB = 102400;//100GB  TODO:
                    Global.Card_LVDS.CurrentReadDataMB = Convert.ToInt32(readNum.Value);
                }

                Global.Card_LVDS?.StartDownloadData();
            }
        }

        private void FilePathHyperlink_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前选择的文件路径
                string? filePath = Global.Card_LVDS?.SaveDataFileName;

                if (string.IsNullOrEmpty(filePath))
                {
                    //MessageBox.Show("请先选择一个文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"文件不存在：\n{filePath}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 使用Windows资源管理器打开文件所在文件夹并选中文件
                string argument = $"/select,\"{filePath}\"";

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = argument,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件所在文件夹：\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
