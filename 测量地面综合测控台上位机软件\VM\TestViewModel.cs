﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace 测量地面综合测控台上位机软件
{
    public class TestViewModel : ObservableObject
    {
        public Frame frame1 => Global.Frame_CCQMNL;
        public Frame frame2 => Global.Frame_CCQState;
        public Frame frame3 => Global.Frame_FSJ;
        public Frame frame4 => Global.Frame_GZ;
        public Frame frame5 => Global.Frame_QSSLB;
        public Frame frame6 => Global.Frame_SSKZXTZT;
        public Frame frame7 => Global.Frame_TY;
        public Frame frame8 => Global.Frame_XTZT;

        public ChartViewModel chart1 { get; set; }
        public ChartViewModel chart2 { get; set; }
        public ChartViewModel chart3 { get; set; }
        public ChartViewModel chart4 { get; set; }
        public ChartViewModel chart5 { get; set; }
        public ChartViewModel chart6 { get; set; }
        public ChartViewModel chart7 { get; set; }
        public ChartViewModel chart8 { get; set; }

        public TestViewModel()
        {
            //frame1.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\存储器模拟量.xml");
            //frame2.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\存储器状态.xml");
            //frame3.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\发射机.xml");
            //frame4.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\惯组.xml");
            //frame5.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\七室水冷泵.xml");
            //frame6.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\三室控制系统状态.xml");
            //frame7.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\体遥.xml");
            //frame8.ReadFrameStructureFromXml(AppDomain.CurrentDomain.BaseDirectory + "配置文件\\系统状态.xml");

            chart1 = new ChartViewModel(frame1.FrameName, frame1.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart2 = new ChartViewModel(frame2.FrameName, frame2.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart3 = new ChartViewModel(frame3.FrameName, frame3.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart4 = new ChartViewModel(frame4.FrameName, frame4.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart5 = new ChartViewModel(frame5.FrameName, frame5.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart6 = new ChartViewModel(frame6.FrameName, frame6.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart7 = new ChartViewModel(frame7.FrameName, frame7.CurrentDeviceChannels?.FindAll(o=>o.ShowCurve));
            chart8 = new ChartViewModel(frame8.FrameName,frame8.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
        }

        



    }
}
