﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Windows;
using System.Xml;
using static 测量地面综合测控台上位机软件.Channel;
using MessageBox = System.Windows.MessageBox;

namespace 测量地面综合测控台上位机软件
{
    public partial class Device : ObservableObject
    {
        public readonly Frame Parent;


        [ObservableProperty]
        [Display(Name = "设备名称", Description = "设备名称", Order = 0)]
        private string deviceName = string.Empty;

        [ObservableProperty]
        [Display(Name = "当前设备", Description = "是否为当前设备", Order = 1)]
        private bool isCurrent = false;

        private List<Channel> channels = [];

        /// <summary>
        /// 是否可以删除此设备（帧中有多个设备时才可删除）
        /// </summary>
        public bool CanDelete => Parent?.Devices.Count > 1;

        /// <summary>
        /// 是否可以取消当前设备状态（帧中有多个设备时才可取消）
        /// </summary>
        public bool CanClearCurrent => Parent?.Devices.Count > 1;

        /// <summary>
        /// 刷新可用性属性
        /// </summary>
        public void RefreshAvailability()
        {
            OnPropertyChanged(nameof(CanDelete));
            OnPropertyChanged(nameof(CanClearCurrent));
        }

        /// <summary>
        /// IsCurrent属性变化处理
        /// </summary>
        partial void OnIsCurrentChanged(bool value)
        {
            if (Parent != null)
            {
                if (value)
                {
                    // 设为当前设备时，更新Frame.CurrentDeviceName
                    Parent.CurrentDeviceName = DeviceName;

                    // 清除同一Frame下其他设备的当前状态
                    foreach (var device in Parent.Devices)
                    {
                        if (device != this && device.IsCurrent)
                        {
                            device.isCurrent = false; // 直接设置字段，避免递归调用
                            device.OnPropertyChanged(nameof(IsCurrent));
                        }
                    }
                }
                else
                {
                    // 如果帧中只有一个设备，不允许取消当前状态
                    if (Parent.Devices.Count <= 1)
                    {
                        // 强制保持为当前设备
                        isCurrent = true;
                        OnPropertyChanged(nameof(IsCurrent));
                        return;
                    }

                    // 取消当前设备时，如果这是当前设备，则清空Frame.CurrentDeviceName
                    if (Parent.CurrentDeviceName == DeviceName)
                    {
                        Parent.CurrentDeviceName = string.Empty;
                    }
                }
            }
        }

        public List<Channel> Channels
        {
            get => channels;
        }

        public EventHandler<LogEventArg>? LogOccurred;

        public Device(Frame parent, string dname)
        {
            Parent = parent;
            DeviceName = dname;
        }


        public Device(Frame parent, string dname, List<Channel> channelsTemplate)
        {
            Parent= parent;
            DeviceName = dname;
            channels.AddRange(channelsTemplate);

            foreach (var channel in channels)
            {
                channel.GainCoef = 1;
                channel.OffsetCoef = 0;
            }
        }

        public bool ReadFromXmlNode(System.Xml.XmlNode node)
        {
            if (node == null)
                return false;
            try
            {
                DeviceName = node.Attributes["设备名称"].Value;
                channels.Clear();
                XmlNodeList channelNodes = node.SelectNodes("./通道");
                foreach (XmlNode channelNode in channelNodes)
                {
                    Channel channel = new(this);
                    channel.LogOccurred += LogOccurred;
                    if (channel.ReadFromXMLNode(channelNode))
                    {
                        channels.Add(channel);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取设备{DeviceName}的配置信息失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public void SaveToXmlNode(ref XmlDocument xmlDoc)
        {
            try
            {
                XmlElement deviceNode = xmlDoc.CreateElement("设备");
                deviceNode.SetAttribute("设备名称", DeviceName);
                foreach (var channel in channels)
                {
                    XmlElement chNode = xmlDoc.CreateElement("通道");
                    channel.SaveToXMLNode(ref chNode);
                    deviceNode.AppendChild(chNode);
                }
                xmlDoc.SelectSingleNode("//帧结构")?.AppendChild(deviceNode);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设备{DeviceName}的配置信息失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

        }
    }
}
