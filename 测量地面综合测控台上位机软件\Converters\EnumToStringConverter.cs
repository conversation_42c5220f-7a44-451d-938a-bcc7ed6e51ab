﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace 测量地面综合测控台上位机软件.Converters
{
    /// <summary>
    /// 枚举与字符串之间的转换器
    /// </summary>
    public class EnumToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return string.Empty;

            if (value is Enum enumValue)
            {
                return enumValue.ToString();
            }

            return value.ToString();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || string.IsNullOrEmpty(value.ToString()))
                return null;

            try
            {
                if (targetType.IsEnum)
                {
                    return Enum.Parse(targetType, value.ToString());
                }
            }
            catch
            {
                // 如果转换失败，返回默认值
                if (targetType.IsEnum)
                {
                    var enumValues = Enum.GetValues(targetType);
                    if (enumValues.Length > 0)
                        return enumValues.GetValue(0);
                }
            }

            return null;
        }
    }
    
}
