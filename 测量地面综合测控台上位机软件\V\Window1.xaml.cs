﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// Window1.xaml 的交互逻辑
    /// </summary>
    public partial class Window1 : Window
    {
        public Window1()
        {
           
            InitializeComponent();

            mysigcard.SignalIndex = Global.Card_Signal.CardIndex;
            mysigcard.OnInfoOccurred += Mysigcard_OnInfoOccurred;
            mysigcard.OnErrorOccurred += Mysigcard_OnErrorOccurred;

            mysigcard.SendCmdEvent += Mysigcard_SendCmdEvent;
            mysigcard.SendDataEvent += Mysigcard_SendDataEvent;
        }

        private int Mysigcard_SendDataEvent(int signalCardIndex, byte[] data, uint dataLength)
        {
            return PCICard.HNFS_WriteData(signalCardIndex, data, dataLength);
        }

        private int Mysigcard_SendCmdEvent(int signalCardIndex, byte[] cmdData)
        {
           return PCICard.HNFS_SendCmdWord(signalCardIndex, cmdData);
        }

        private void Mysigcard_OnInfoOccurred(object? sender, string e)
        {
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.INFO, e);
        }
        private void Mysigcard_OnErrorOccurred(object? sender, string e)
        {
            App.ServiceProvider.GetRequiredService<MainWindowViewModel>().AddLog(LogHelper.LogLevel.ERROR, e);
        }
    }
}
