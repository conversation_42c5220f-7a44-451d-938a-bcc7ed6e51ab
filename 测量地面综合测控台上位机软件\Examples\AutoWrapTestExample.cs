﻿using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.LayoutControl;

namespace 测量地面综合测控台上位机软件.Examples
{
    ///// <summary>
    ///// 自动换行功能测试示例
    ///// </summary>
    //public partial class AutoWrapTestExample : Window
    //{
    //    private AutoChannelLayoutGroup _testLayoutGroup;
    //    private List<Channel> _testChannels;

    //    public AutoWrapTestExample()
    //    {
    //        InitializeComponent();
    //        CreateTestData();
    //        SetupUI();
    //    }

    //    /// <summary>
    //    /// 创建测试数据
    //    /// </summary>
    //    private void CreateTestData()
    //    {
    //        // 创建测试设备
    //        var testDevice = new Device(null) { DeviceName = "测试设备" };

    //        // 创建测试通道
    //        _testChannels = new List<Channel>();
    //        for (int i = 1; i <= 12; i++)
    //        {
    //            var channel = new Channel(testDevice)
    //            {
    //                ChannelName = $"CH{i:D2}",
    //                ChannelDescription = $"通道{i}描述",
    //                Monitor = true
    //            };
                
    //            // 模拟一些数据
    //            // 注意：在实际应用中，这些值会通过数据更新机制设置
    //            _testChannels.Add(channel);
    //        }
    //    }

    //    /// <summary>
    //    /// 设置UI界面
    //    /// </summary>
    //    private void SetupUI()
    //    {
    //        // 创建主容器
    //        var mainGrid = new Grid();
    //        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
    //        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

    //        // 创建控制面板
    //        var controlPanel = CreateControlPanel();
    //        Grid.SetRow(controlPanel, 0);
    //        mainGrid.Children.Add(controlPanel);

    //        // 创建测试布局组
    //        _testLayoutGroup = new AutoChannelLayoutGroup
    //        {
    //            Header = "自动换行测试",
    //            Margin = new Thickness(10),
    //            ValueBindingPath = "LastDecValue",
    //            LayoutOrientation = Orientation.Horizontal,
    //            EnableAutoWrap = true,
    //            MaxHorizontalWidth = 600,
    //            LabelWidth = 80,
    //            ValueWidth = 60,
    //            RowHeight = 25,
    //            ShowChannelDescription = true
    //        };

    //        _testLayoutGroup.SetChannels(_testChannels);

    //        Grid.SetRow(_testLayoutGroup, 1);
    //        mainGrid.Children.Add(_testLayoutGroup);

    //        Content = mainGrid;
    //    }

    //    /// <summary>
    //    /// 创建控制面板
    //    /// </summary>
    //    private StackPanel CreateControlPanel()
    //    {
    //        var panel = new StackPanel
    //        {
    //            Orientation = Orientation.Horizontal,
    //            Margin = new Thickness(10),
    //            Background = System.Windows.Media.Brushes.LightGray
    //        };

    //        // 布局方向切换
    //        var orientationButton = new Button
    //        {
    //            Content = "切换到垂直布局",
    //            Margin = new Thickness(5),
    //            Padding = new Thickness(10, 5, 10, 5)
    //        };
    //        orientationButton.Click += (s, e) =>
    //        {
    //            if (_testLayoutGroup.LayoutOrientation == Orientation.Horizontal)
    //            {
    //                _testLayoutGroup.LayoutOrientation = Orientation.Vertical;
    //                orientationButton.Content = "切换到水平布局";
    //            }
    //            else
    //            {
    //                _testLayoutGroup.LayoutOrientation = Orientation.Horizontal;
    //                orientationButton.Content = "切换到垂直布局";
    //            }
    //        };
    //        panel.Children.Add(orientationButton);

    //        // 自动换行切换
    //        var wrapButton = new Button
    //        {
    //            Content = "禁用自动换行",
    //            Margin = new Thickness(5),
    //            Padding = new Thickness(10, 5, 10, 5)
    //        };
    //        wrapButton.Click += (s, e) =>
    //        {
    //            _testLayoutGroup.EnableAutoWrap = !_testLayoutGroup.EnableAutoWrap;
    //            wrapButton.Content = _testLayoutGroup.EnableAutoWrap ? "禁用自动换行" : "启用自动换行";
    //        };
    //        panel.Children.Add(wrapButton);

    //        // 宽度调整
    //        var widthLabel = new Label
    //        {
    //            Content = "最大宽度:",
    //            VerticalAlignment = VerticalAlignment.Center,
    //            Margin = new Thickness(10, 0, 0, 0)
    //        };
    //        panel.Children.Add(widthLabel);

    //        var widthSlider = new Slider
    //        {
    //            Minimum = 300,
    //            Maximum = 1000,
    //            Value = 600,
    //            Width = 150,
    //            VerticalAlignment = VerticalAlignment.Center,
    //            Margin = new Thickness(5)
    //        };
    //        widthSlider.ValueChanged += (s, e) =>
    //        {
    //            _testLayoutGroup.MaxHorizontalWidth = e.NewValue;
    //            widthValueLabel.Content = $"{e.NewValue:F0}px";
    //        };
    //        panel.Children.Add(widthSlider);

    //        var widthValueLabel = new Label
    //        {
    //            Content = "600px",
    //            VerticalAlignment = VerticalAlignment.Center,
    //            MinWidth = 50
    //        };
    //        panel.Children.Add(widthValueLabel);

    //        // 绑定路径切换
    //        var bindingCombo = new ComboBox
    //        {
    //            Margin = new Thickness(10, 5, 5, 5),
    //            Width = 120,
    //            VerticalAlignment = VerticalAlignment.Center
    //        };
    //        bindingCombo.Items.Add("LastHexValue");
    //        bindingCombo.Items.Add("LastDecValue");
    //        bindingCombo.Items.Add("LastVolValue");
    //        bindingCombo.Items.Add("LastPhyValue");
    //        bindingCombo.SelectedItem = "LastDecValue";
    //        bindingCombo.SelectionChanged += (s, e) =>
    //        {
    //            if (bindingCombo.SelectedItem != null)
    //            {
    //                _testLayoutGroup.ValueBindingPath = bindingCombo.SelectedItem.ToString();
    //            }
    //        };
    //        panel.Children.Add(bindingCombo);

    //        // 添加通道按钮
    //        var addChannelButton = new Button
    //        {
    //            Content = "添加通道",
    //            Margin = new Thickness(5),
    //            Padding = new Thickness(10, 5, 10, 5)
    //        };
    //        addChannelButton.Click += (s, e) =>
    //        {
    //            var device = _testChannels[0].Parent;
    //            var newChannel = new Channel(device)
    //            {
    //                ChannelName = $"CH{_testChannels.Count + 1:D2}",
    //                ChannelDescription = $"新通道{_testChannels.Count + 1}",
    //                Monitor = true
    //            };
    //            _testChannels.Add(newChannel);
    //            _testLayoutGroup.SetChannels(_testChannels);
    //        };
    //        panel.Children.Add(addChannelButton);

    //        // 移除通道按钮
    //        var removeChannelButton = new Button
    //        {
    //            Content = "移除通道",
    //            Margin = new Thickness(5),
    //            Padding = new Thickness(10, 5, 10, 5)
    //        };
    //        removeChannelButton.Click += (s, e) =>
    //        {
    //            if (_testChannels.Count > 1)
    //            {
    //                _testChannels.RemoveAt(_testChannels.Count - 1);
    //                _testLayoutGroup.SetChannels(_testChannels);
    //            }
    //        };
    //        panel.Children.Add(removeChannelButton);

    //        return panel;
    //    }

    //    /// <summary>
    //    /// 初始化组件（如果使用XAML需要）
    //    /// </summary>
    //    private void InitializeComponent()
    //    {
    //        Title = "AutoChannelLayoutGroup 自动换行测试";
    //        Width = 900;
    //        Height = 600;
    //        WindowStartupLocation = WindowStartupLocation.CenterScreen;
    //    }
    //}

    ///// <summary>
    ///// 简化的启动类，用于独立测试
    ///// </summary>
    //public class AutoWrapTestApp
    //{
    //    [STAThread]
    //    public static void Main()
    //    {
    //        var app = new Application();
    //        var window = new AutoWrapTestExample();
    //        app.Run(window);
    //    }
    //}
}
