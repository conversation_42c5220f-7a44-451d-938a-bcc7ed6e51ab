﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Threading;

namespace 测量地面综合测控台上位机软件
{
    public partial class MonitorViewModel:ObservableObject
    {
        /// <summary>
        /// 存储器模拟量
        /// </summary>
        public Frame? Frame_CCQMNL { get => Global.Frame_CCQMNL; }

        /// <summary>
        /// 存储器状态
        /// </summary>
        public  Frame? Frame_CCQState { get => Global.Frame_CCQState; }

        /// <summary>
        /// 存储器状态422
        /// </summary>
        public Frame? Frame_CCQState422 { get => Global.Frame_CCQState422; }

        /// <summary>
        /// 发射机
        /// </summary>
        public Frame? Frame_FSJ {  get => Global.Frame_FSJ; }

        /// <summary>
        /// 惯组
        /// </summary>
        public  Frame? Frame_GZ {  get => Global.Frame_GZ; }

        /// <summary>
        /// 七室水冷泵
        /// </summary>
        public  Frame? Frame_QSSLB { get => Global.Frame_QSSLB; }

        /// <summary>
        /// 三室控制系统状态
        /// </summary>
        public  Frame? Frame_SSKZXTZT { get=>Global.Frame_SSKZXTZT; }

        /// <summary>
        /// 体遥
        /// </summary>
        public Frame? Frame_TY {  get => Global.Frame_TY; }

        /// <summary>
        /// 系统状态
        /// </summary>
        public  Frame? Frame_XTZT { get => Global.Frame_XTZT; }

        public Frame? Frame_121WX { get => Global.Frame_121WX; }

        public Frame? Frame_122WX { get => Global.Frame_122WX; }

        //public ChartViewModel ChartCCQMNL { get; set; }
        //public ChartViewModel ChartCCQState { get; set; }
        //public ChartViewModel ChartFSJ { get; set; }
        //public ChartViewModel ChartGZ { get; set; }
        //public ChartViewModel ChartQSSLB { get; set; }
        //public ChartViewModel ChartSSKZXTZT { get; set; }
        //public ChartViewModel ChartTY { get; set; }
        //public ChartViewModel ChartXTZT { get; set; }


        [ObservableProperty]
        public string dataShowType;

        //public MVVMSerializationViewModel SerializationViewModel { get; set; }

        DispatcherTimer timer = new DispatcherTimer();

        // 图表更新事件
        public event Action<string>? ChartUpdateRequested;



        public MonitorViewModel() 
        {
            //ChartCCQMNL = new ChartViewModel(Frame_CCQMNL.FrameName, Frame_CCQMNL.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartCCQState = new ChartViewModel(Frame_CCQState.FrameName, Frame_CCQState.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartFSJ = new ChartViewModel(Frame_FSJ.FrameName, Frame_FSJ.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartGZ = new ChartViewModel(Frame_GZ.FrameName, Frame_GZ.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartQSSLB = new ChartViewModel(Frame_QSSLB.FrameName, Frame_QSSLB.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartSSKZXTZT = new ChartViewModel(Frame_SSKZXTZT.FrameName, Frame_SSKZXTZT.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartTY = new ChartViewModel(Frame_TY.FrameName, Frame_TY.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));
            //ChartXTZT = new ChartViewModel(Frame_XTZT.FrameName, Frame_XTZT.CurrentDeviceChannels?.FindAll(o => o.ShowCurve));


            //SerializationViewModel = new MVVMSerializationViewModel([Frame_CCQMNL, Frame_CCQState, Frame_FSJ, Frame_GZ, Frame_QSSLB, Frame_SSKZXTZT, Frame_TY, Frame_XTZT]);

            timer.Interval = TimeSpan.FromMilliseconds(300);

            timer.Tick += Timer_Tick;

        }



        private void Timer_Tick(object? sender, EventArgs e)
        {
            //Trace.WriteLine("更新");
            Global.DataAnalyzer_CCQMNL.UpdateChannelLastValue();
            Global.DataAnalyzer_CCQState.UpdateChannelLastValue();
            Global.DataAnalyzer_CCQState422.UpdateChannelLastValue();
            Global.DataAnalyzer_FSJ.UpdateChannelLastValue();
            Global.DataAnalyzer_GZ.UpdateChannelLastValue();
            Global.DataAnalyzer_QSSLB.UpdateChannelLastValue();
            Global.DataAnalyzer_SSKZXTZT.UpdateChannelLastValue();
            Global.DataAnalyzer_TY.UpdateChannelLastValue();
            Global.DataAnalyzer_XTZT.UpdateChannelLastValue();

            // 更新传统图表
            //MonitorVM.ChartCCQMNL.Update(Global.Frame_CCQMNL);
            //MonitorVM.ChartCCQState.Update(Global.Frame_CCQState);
            //MonitorVM.ChartFSJ.Update(Global.Frame_FSJ);
            //MonitorVM.ChartGZ.Update(Global.Frame_GZ);
            //MonitorVM.ChartQSSLB.Update(Global.Frame_QSSLB);
            //MonitorVM.ChartSSKZXTZT.Update(Global.Frame_SSKZXTZT);
            //MonitorVM.ChartTY.Update(Global.Frame_TY);
            //MonitorVM.ChartXTZT.Update(Global.Frame_XTZT);

            //// 触发统一图表更新事件
            ChartUpdateRequested?.Invoke("数字量");
        }

        public void StartUpdateUI()
        {
            timer.Start();
        }

        public void StopUpdateUI() { timer.Stop(); }


    }
}
