﻿using DevExpress.Office.Utils;
using DevExpress.Xpf.Charts;
using DevExpress.Xpf.Docking;
using DevExpress.Xpf.LayoutControl;
using DevExpress.Xpf.WindowsUI;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// MonitorPage1.xaml 的交互逻辑
    /// </summary>
    public partial class MonitorPage1 : NavigationPage
    {
        private List<AutoChannelLayoutGroup> _channelGroups = new();
        private double _lastChartPanelWidth = 300; // 记住用户最后设置的图表面板宽度


        private Dictionary<Channel, LineSeries2D> chLineSeries = new Dictionary<Channel, LineSeries2D>();

        public MonitorPage1()
        {
            InitializeComponent();
            Trace.WriteLine("开始初始化监测界面");
            this.DataContext = App.ServiceProvider.GetRequiredService<MonitorViewModel>();
            if (this.DataContext is MonitorViewModel vm)
                InitUI([vm.Frame_CCQMNL, vm.Frame_CCQState,vm.Frame_CCQState422, vm.Frame_FSJ, vm.Frame_GZ, vm.Frame_QSSLB, vm.Frame_SSKZXTZT, vm.Frame_TY,vm.Frame_121WX, vm.Frame_XTZT]);

            this.configPanel.ParentControl = this.monitorLayoutControl;
            this.configPanel.ConfigApplied += ConfigPanel_ConfigApplied;

            // 订阅主窗口的图表更新事件
            SubscribeToChartUpdateEvents();
            Trace.WriteLine("监测界面初始化完成");
        }


        private void ConfigPanel_ConfigApplied(object? sender, ChannelDisplayConfigEventArgs e)
        {
            App.Log.Debug($"调整{(sender as ChannelDisplayConfigPanel).cbApplyMode.Text} 通道显示配置:{e.Config}");
        }

        private void ChartExpander_Expanded(object sender, RoutedEventArgs e)
        {
            // 当图表面板展开时，恢复用户之前设置的宽度
            var chartColumn = this.FindName("ChartColumn") as ColumnDefinition;
            if (chartColumn != null)
            {
                chartColumn.Width = new GridLength(_lastChartPanelWidth, GridUnitType.Pixel);
                //chartColumn.MinWidth = 200;
                //chartColumn.MaxWidth = 600;
            }
        }

        private void ChartExpander_Collapsed(object sender, RoutedEventArgs e)
        {
            var chartColumn = this.FindName("ChartColumn") as ColumnDefinition;
            if (chartColumn != null)
            {
                // 折叠前先保存当前宽度（如果是有效的像素宽度）
                if (chartColumn.Width.GridUnitType == GridUnitType.Pixel && chartColumn.Width.Value >= 200 && chartColumn.Width.Value <= 600)
                {
                    _lastChartPanelWidth = chartColumn.Width.Value;
                }

                // 当图表面板折叠时，将列宽度设为Auto，让它只占用必要的空间
                chartColumn.Width = GridLength.Auto;
                chartColumn.MinWidth = 0;
                chartColumn.MaxWidth = double.PositiveInfinity;
            }
        }

        private void GridSplitter_DragCompleted(object sender, System.Windows.Controls.Primitives.DragCompletedEventArgs e)
        {
            // 当用户完成拖拽后，保存新的宽度
            var chartColumn = this.FindName("ChartColumn") as ColumnDefinition;
            if (chartColumn != null && chartColumn.Width.GridUnitType == GridUnitType.Pixel)
            {
                var newWidth = chartColumn.Width.Value;
                //if (newWidth >= 200 && newWidth <= 600)
                {
                    _lastChartPanelWidth = newWidth;
                }
            }
        }

        private void InitUI(Frame[] frames)
        {
            // 清空现有内容
            chLineSeries.Clear();
            monitorLayoutControl.Children.Clear();
            _channelGroups.Clear();

            // 初始化统一图表
            InitializeUnifiedChart(frames);

            #region 初始化LayoutControl
            foreach (var frame in frames)
            {
                
                AutoChannelLayoutGroup autoLayoutGroup = new()
                {
                    //Name = $"layCtlBackup{frame.FrameName}",
                    IsCollapsible = true,
                    View = LayoutGroupView.GroupBox,
                    VerticalAlignment = VerticalAlignment.Stretch,
                    Orientation= System.Windows.Controls.Orientation.Vertical,
                    // 配置显示属性
                    ValueBindingPath = "LastDecValue",
                    ShowChannelDescription = true,
                    ValueFormat = "",
                    // 布局配置
                    ChannelColumns = 1,
                    RowHeight = 25,
                };

                // 绑定Header
                Binding binding = new()
                {
                    Source = frame,
                    Path = new PropertyPath("FrameName")
                };
                autoLayoutGroup.SetBinding(DevExpress.Xpf.LayoutControl.LayoutGroup.HeaderProperty, binding);

                // 设置通道列表
                List<Channel> chs = frame.CurrentDeviceChannels;
                autoLayoutGroup.SetChannels(chs);

                _channelGroups.Add(autoLayoutGroup);

                // 添加到主容器（备用）
                this.monitorLayoutControl.Children.Add(autoLayoutGroup);
            }
            #endregion


            #region 初始化配置面板

            // 设置配置面板
            this.configPanel.SetAllLayoutGroups(_channelGroups);

            #endregion

            #region 原来的手动网格布局代码（保留作为备用）
            /*
            foreach (var frame in frames)
            {
                LayoutGroup layoutGroup = new() { Name = $"layCtl{frame.FrameName}", IsCollapsible = true, View = LayoutGroupView.GroupBox, VerticalAlignment = VerticalAlignment.Stretch };
                Binding binding = new()
                {
                    Source = frame,
                    Path = new PropertyPath("FrameName")
                };
                layoutGroup.SetBinding(LayoutGroup.HeaderProperty, binding);

                List<Channel> chs = frame.CurrentDeviceChannels;

                #region 网格布局
                Grid grid = new() { Name = $"grid{frame.FrameName}" };

                if (chs.Count >= 20)
                {
                    ColumnDefinition columnDefinition = new() { Width = GridLength.Auto };
                    ColumnDefinition columnDefinition2 = new() { Width = new GridLength(50, GridUnitType.Star) };
                    ColumnDefinition columnDefinition3 = new() { Width = GridLength.Auto };
                    ColumnDefinition columnDefinition4 = new() { Width = new GridLength(50, GridUnitType.Star) };

                    grid.ColumnDefinitions.Add(columnDefinition);
                    grid.ColumnDefinitions.Add(columnDefinition2);
                    grid.ColumnDefinitions.Add(columnDefinition3);
                    grid.ColumnDefinitions.Add(columnDefinition4);

                    int row = 0, col = 0;
                    foreach (Channel ch in chs)
                    {
                        RowDefinition rowDefinition = new() { Height = GridLength.Auto };
                        grid.RowDefinitions.Add(rowDefinition);

                        TextBlock chName = new() { TextAlignment = TextAlignment.Left ,Margin=new Thickness(5,0,0,0)  };
                        Binding binding2 = new()
                        {
                            Source = ch,
                            Path = new PropertyPath("ChannelDescription")
                        };
                        chName.SetBinding(TextBlock.TextProperty, binding2);

                        grid.Children.Add(chName);
                        Grid.SetRow(chName, row);
                        Grid.SetColumn(chName, 2 * col);

                        TextBlock chValue = new() { Name = $"chV_{ch.ChannelName}", Text = "未知", Margin = new Thickness(5, 0, 5, 0) };

//#if EnableLastValue
                        Binding binding3 = new()
                        {
                            Source = ch,
                            Path = new PropertyPath("LastHexValue")
                        };

                       // Trace.WriteLine($"初始化界面时{ch.ChannelName}的地址为{Function.getMemory(ch)}");

                        chValue.SetBinding(TextBlock.TextProperty, binding3);
//#endif

                        grid.Children.Add(chValue);
                        Grid.SetRow(chValue, row);
                        Grid.SetColumn(chValue, 2 * col + 1);

                        col++;
                        if (col == 2)
                        {
                            col = 0;
                            row++;
                        }

                    }

                }
                else
                {
                    ColumnDefinition columnDefinition = new() { Width = new GridLength(70, GridUnitType.Star) };
                    ColumnDefinition columnDefinition2 = new() { Width = new GridLength(30, GridUnitType.Star) };

                    grid.ColumnDefinitions.Add(columnDefinition);
                    grid.ColumnDefinitions.Add(columnDefinition2);

                    int row = 0;
                    foreach (Channel ch in chs)
                    {
                        RowDefinition rowDefinition = new() { Height = GridLength.Auto };
                        grid.RowDefinitions.Add(rowDefinition);

                        TextBlock chName = new() { TextAlignment = TextAlignment.Left };
                        Binding binding2 = new()
                        {
                            Source = ch,
                            Path = new PropertyPath("ChannelDescription")
                        };
                        chName.SetBinding(TextBlock.TextProperty, binding2);

                        grid.Children.Add(chName);
                        Grid.SetRow(chName, row);
                        Grid.SetColumn(chName, 0);

                        TextBlock chValue = new() { Name = $"chV_{ch.ChannelName}", Text = "未知", Margin = new Thickness(10, 0, 5, 0) };

                        Binding binding3 = new()
                        {
                            Source = ch,
                            Path = new PropertyPath("LastHexValue")
                        };
                        chValue.SetBinding(TextBlock.TextProperty, binding3);

                        grid.Children.Add(chValue);
                        Grid.SetRow(chValue, row++);
                        Grid.SetColumn(chValue, 1);
                    }
                }
                layoutGroup.Children.Add(grid);
                #endregion

                #region 瀑布流布局

                //WrapPanel wrapPanel = new WrapPanel();
                //foreach (Channel ch in chs)
                //{
                //    ChannelDisplayBox box = new ChannelDisplayBox() { Width=150};
                //    Binding binding2 = new()
                //    {
                //        Source = ch,
                //        Path = new PropertyPath("ChannelDescription")
                //    };
                //    box.SetBinding(ChannelDisplayBox.ChannelNameProperty, binding2);

                //    wrapPanel.Children.Add(box);
                //}

                //layoutGroup.Children.Add(wrapPanel);
                #endregion


                this.monitorLayoutControl.Children.Add(layoutGroup);

            }
            */
            #endregion



            #region 初始化绘图区域
            foreach (var frame in frames)
            {
                List<Channel> chs = frame.Devices.First(o => o.DeviceName == frame.CurrentDeviceName).Channels.FindAll(o => o.ShowCurve == true);

                if (chs.Count == 0)
                    continue;

                foreach (Channel ch in chs)
                {
                    ChartControl chartControl = new()
                    {
                        Name = $"chart{frame.FrameName}",
                        IsDirectXSurfaceRendering = true
                    };

                    // 应用图表样式
                    //Style? chartStyle = null;

                    //if (this.Resources["ChartControlStyle"] is Style pageStyle)
                    //{
                    //    chartStyle = pageStyle;
                    //}
                    //if (chartStyle != null)
                    //{
                    //    chartControl.Style = chartStyle;
                    //}

                    XYDiagram2D diagram = new()
                    {
                        PaneOrientation = System.Windows.Controls.Orientation.Vertical
                    };

                    chartControl.Diagram = diagram;

                    //diagram.ActualAxisY.Alignment = AxisAlignment.Near;
                    //diagram.ActualAxisY.GridLinesVisible = true;
                    //diagram.ActualAxisY.GridLinesMinorVisible = false;
                    //diagram.ActualAxisY.Interlaced = true;
                    //diagram.ActualAxisY.LabelAlignment = AxisLabelAlignment.Auto;
                    //diagram.ActualAxisY.LabelPosition = AxisLabelPosition.Outside;
                    //diagram.ActualAxisY.TitlePosition = AxisTitlePosition.Outside;

                    AxisTitle title = new AxisTitle() { Content = ch.ChannelDescription, Visible = true, Alignment = TitleAlignment.Center };
                    diagram.ActualAxisY.Title = title;

                    //diagram.ActualAxisX.Alignment = AxisAlignment.Near;
                    //diagram.ActualAxisX.LabelAlignment = AxisLabelAlignment.Auto;
                    //diagram.ActualAxisX.LabelPosition = AxisLabelPosition.Outside;
                   

                    LineSeries2D line = new LineSeries2D()
                    {
                        DisplayName = $"{frame.FrameName}-{ch.ChannelName}",
                        Pane= diagram.ActualDefaultPane
                    };

                    diagram.Series.Add(line);


                    gridChart.Children.Add(chartControl);

                    chLineSeries.Add(ch, line);
                }
            }
            #endregion
        }
        /// <summary>
        /// 初始化统一图表
        /// </summary>
        /// <param name="frames">所有帧数据</param>
        private void InitializeUnifiedChart(Frame[] frames)
        {
            //if (unifiedChart != null)
            //{
            //    // 设置帧数据
            //    unifiedChart.SetFrames([.. frames]);

            //    // 如果有ViewModel，可以监听数据更新事件
            //    if (this.DataContext is MonitorViewModel vm)
            //    {
            //        // 这里可以添加定时器或事件监听来更新图表数据
            //        // 例如：vm.DataUpdated += (s, e) => unifiedChart.UpdateChart();

            //    }
            //}
        }



        /// <summary>
        /// 更新统一图表数据
        /// </summary>
        public void UpdateUnifiedChart()
        {
            //unifiedChart?.UpdateChart();
        }

        public void UpdateCharts(string dataType)
        {
            foreach(var a in chLineSeries)
            {
                a.Value.Points.Clear();

                // 直接使用Channel的Parent.Parent，它应该就是Global中的Frame实例
                Frame targetFrame = a.Key.Parent.Parent;

                // 调试输出：比较两个Frame实例的地址
                Frame? globalFrame = GetFrameByChannel(a.Key);

                var samplePoints = targetFrame.GetChannelSamplePoints(a.Key.ChannelName, dataType);

                a.Value.Points.AddRange([.. samplePoints.Select(sp => new SeriesPoint(sp.time, sp.value))]);
            }
        }

        /// <summary>
        /// 根据Channel找到对应的Global中的Frame
        /// </summary>
        /// <param name="channel">通道</param>
        /// <returns>对应的Frame实例</returns>
        private static Frame? GetFrameByChannel(Channel channel)
        {
            // 获取Channel所属的Frame名称
            string frameName = channel.Parent.Parent.FrameName;

            // 直接通过Frame名称字符串匹配Global中的Frame实例
            return frameName switch
            {
                "存储器模拟量" => Global.Frame_CCQMNL,
                "存储器状态(LVDS)" => Global.Frame_CCQState,
                "存储器状态(422)" => Global.Frame_CCQState422,
                "发射机" => Global.Frame_FSJ,
                "惯组" => Global.Frame_GZ,
                "七室水冷泵" => Global.Frame_QSSLB,
                "三室控制系统状态" => Global.Frame_SSKZXTZT,
                "体遥" => Global.Frame_TY,
                "系统状态" => Global.Frame_XTZT,
                "121无线" => Global.Frame_121WX,
                "122无线" => Global.Frame_122WX,
                _ => null
            };
        }

        /// <summary>
        /// 根据Frame名称获取对应的FrameAnalyzer
        /// </summary>
        /// <param name="frameName">Frame名称</param>
        /// <returns>对应的FrameAnalyzer实例</returns>
        private static FrameAnalyzer? GetFrameAnalyzerByFrameName(string frameName)
        {
            return frameName switch
            {
                "存储器模拟量" => Global.DataAnalyzer_CCQMNL,
                "存储器状态" => Global.DataAnalyzer_CCQState,
                "发射机" => Global.DataAnalyzer_FSJ,
                "惯组" => Global.DataAnalyzer_GZ,
                "七室水冷泵" => Global.DataAnalyzer_QSSLB,
                "三室控制系统状态" => Global.DataAnalyzer_SSKZXTZT,
                "体遥" => Global.DataAnalyzer_TY,
                "系统状态" => Global.DataAnalyzer_XTZT,
                _ => null
            };
        }


        /// <summary>
        /// 设置统一图表的数据类型
        /// </summary>
        /// <param name="dataType">数据类型：数字量、电压量、物理量</param>
        public void SetUnifiedChartDataType(string dataType)
        {
            //unifiedChart?.SetDataType(dataType);
        }
        /// <summary>
        /// 订阅图表更新事件
        /// </summary>
        private void SubscribeToChartUpdateEvents()
        {
            // 获取主窗口的ViewModel
            //var mainWindowVM = App.ServiceProvider.GetRequiredService<MainWindowViewModel>();
            //if (mainWindowVM != null)
            //{
            //    // 订阅图表更新事件
            //    //mainWindowVM.ChartUpdateRequested += OnChartUpdateRequested;



            //}

            var monitorVM= App.ServiceProvider.GetRequiredService<MonitorViewModel>();
            if(monitorVM != null)
            {
                monitorVM.ChartUpdateRequested+= OnChartUpdateRequested;
            }

        }

        /// <summary>
        /// 处理图表更新请求
        /// </summary>
        private void OnChartUpdateRequested(string dataType)
        {
            // 在UI线程中更新图表
            Dispatcher.BeginInvoke(() =>
            {
                //UpdateUnifiedChart();
                UpdateCharts(dataType);
            });
        }

        /// <summary>
        /// 页面卸载时取消订阅
        /// </summary>
        private void MonitorPage1_Unloaded(object sender, RoutedEventArgs e)
        {
            //// 取消订阅事件，避免内存泄漏
            //var mainWindowVM = App.ServiceProvider.GetRequiredService<MainWindowViewModel>();
            //if (mainWindowVM != null)
            //{
            //    mainWindowVM.ChartUpdateRequested -= OnChartUpdateRequested;
            //}
        }





    }
}
