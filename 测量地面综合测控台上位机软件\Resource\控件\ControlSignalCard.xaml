﻿<UserControl
    x:Class="测量地面综合测控台上位机软件.ControlSignalCard"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:测量地面综合测控台上位机软件.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    d:DesignHeight="450"
    d:DesignWidth="800">

    <UserControl.Resources>
        
        <!-- 按钮样式 -->
        <Style x:Key="ButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonDefault}">
            <Setter Property="Margin" Value="0,0,10,2" />
            <Setter Property="Height" Value="40" />
            <Setter Property="FontSize" Value="15" />
            <Setter Property="Padding" Value="10" />
        </Style>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" MinHeight="50" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <WrapPanel Grid.Row="0" Margin="10,10,10,0">
            <Button
                x:Name="BtnOpenConfig"
                Click="BtnOpenConfig_Click"
                Content="打开配置"
                Style="{StaticResource ButtonStyle}"
                ToolTip="{Binding CurrentConfigFile}" />

            <Button
                x:Name="BtnSaveConfig"
                Click="BtnSaveConfig_Click"
                Content="保存配置"
                Style="{StaticResource ButtonStyle}" />

            <Button
                x:Name="BtnSaveAsConfig"
                Click="BtnSaveAsConfig_Click"
                Content="另存为"
                Style="{StaticResource ButtonStyle}" />

            <Separator Margin="10,0" />

            <Button
                x:Name="BtnGenerateChannels"
                Click="BtnGenerateChannels_Click"
                Content="生成通道"
                Style="{StaticResource ButtonStyle}" />

            <Button
                x:Name="BtnDownloadSignal"
                Click="BtnDownloadSignal_Click"
                Content="信源下载"
                Style="{StaticResource ButtonStyle}" />

            <Separator Margin="10,0" />

            <Button
                x:Name="BtnStartSignal"
                Click="BtnStartSignal_Click"
                Content="启动信源"
                Style="{StaticResource ButtonStyle}" />

            <Button
                x:Name="BtnStopSignal"
                Click="BtnStopSignal_Click"
                Content="停止信源"
                Style="{StaticResource ButtonStyle}" />
        </WrapPanel>

        <WrapPanel Grid.Row="1" Margin="10,5,0,0">
            <TextBlock
                Margin="5,0,5,0"
                VerticalAlignment="Center"
                Text="">
                <Run Text="通道波形:" />
            </TextBlock>
            <ComboBox
                x:Name="CboSignalType"
                Width="80"
                Margin="5,0,5,0"
                SelectedIndex="0" SelectionChanged="CboSignalType_SelectionChanged">
                <ComboBoxItem Content="直流" />
                <ComboBoxItem Content="正弦波" />
                <ComboBoxItem Content="三角波" />
                <ComboBoxItem Content="锯齿波" />
                <ComboBoxItem Content="方波" />
            </ComboBox>
            <TextBlock
                Margin="5,0,5,0"
                VerticalAlignment="Center"
                Text="下限值:" />


            <hc:NumericUpDown
                x:Name="NumLow"
                Width="65"
                Margin="5,0,5,0"
                Maximum="7"
                Minimum="-1" />
            <TextBlock
                Margin="5,0,5,0"
                VerticalAlignment="Center"
                Text="上限值:" />


            <hc:NumericUpDown
                x:Name="NumUp"
                Width="65"
                Margin="5,0,5,0"
                Maximum="7"
                Minimum="-1" />
            <TextBlock
                Margin="5,0,5,0"
                VerticalAlignment="Center"
                Text="频率:" />


            <hc:NumericUpDown
                x:Name="NumFrequency"
                Width="65"
                Margin="5,0,5,0"
                Maximum="100"
                Minimum="0" />
            <TextBlock
                Margin="5,0,5,0"
                VerticalAlignment="Center"
                Text="相位:" />


            <hc:NumericUpDown
                x:Name="NumPhase"
                Width="65"
                Margin="5,0,5,0"
                Maximum="360"
                Minimum="0" />
            <TextBlock
                Margin="5,0,5,0"
                VerticalAlignment="Center"
                Text="占空比:" />

            <hc:NumericUpDown
                x:Name="NumDutyCycle"
                Width="65"
                Margin="5,0,5,0"
                Maximum="100"
                Minimum="0" />
        </WrapPanel>

        <DataGrid
            Grid.Row="2"
            x:Name="DgChannels"
            AutoGenerateColumns="False"
            CanUserSortColumns="False"
            Background="White"
            CanUserAddRows="False"
            CanUserDeleteRows="False"
            CanUserReorderColumns="False"
            CellEditEnding="DgChannels_CellEditEnding"
            HeadersVisibility="All"
            LoadingRow="DgChannels_LoadingRow"
            SelectionMode="Single"
            SelectionUnit="Cell">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="120"
                    Binding="{Binding ChannelName}"
                    Header="通道名称" />



                <DataGridComboBoxColumn Header="信号类型" 
                        SelectedItemBinding="{Binding SignalType}" 
                        Width="100">
                    <DataGridComboBoxColumn.ElementStyle>
                        <Style TargetType="ComboBox">
                            <Setter Property="ItemsSource">
                                <Setter.Value>
                                    <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                        <sys:String>正弦波</sys:String>
                                        <sys:String>三角波</sys:String>
                                        <sys:String>锯齿波</sys:String>
                                        <sys:String>方波</sys:String>
                                        <sys:String>直流</sys:String>
                                    </x:Array>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DataGridComboBoxColumn.ElementStyle>
                    <DataGridComboBoxColumn.EditingElementStyle>
                        <Style TargetType="ComboBox">
                            <Setter Property="ItemsSource">
                                <Setter.Value>
                                    <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                        <sys:String>正弦波</sys:String>
                                        <sys:String>三角波</sys:String>
                                        <sys:String>锯齿波</sys:String>
                                        <sys:String>方波</sys:String>
                                        <sys:String>直流</sys:String>
                                    </x:Array>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DataGridComboBoxColumn.EditingElementStyle>
                </DataGridComboBoxColumn>

                <DataGridTextColumn Header="下限值" 
                    Binding="{Binding Minimum}" 
                    Width="80"/>

                <DataGridTextColumn Header="上限值" 
                    Binding="{Binding Maximum}" 
                    Width="80"/>

                <DataGridTextColumn Header="频率" 
                    Binding="{Binding Frequency}" 
                    Width="80"/>

                <DataGridTextColumn Header="相位" 
                    Binding="{Binding Phase}" 
                    Width="80"/>

                <DataGridTextColumn Header="占空比" 
                    Binding="{Binding DutyCycle}" 
                    Width="80"/>

                <DataGridTextColumn Header="标定系数K" 
                    Binding="{Binding CoefK}" 
                    Width="*"/>

                <DataGridTextColumn Header="标定系数B" 
                    Binding="{Binding CoefB}" 
                    Width="*"/>


                
            </DataGrid.Columns>
        </DataGrid>

    </Grid>
</UserControl>