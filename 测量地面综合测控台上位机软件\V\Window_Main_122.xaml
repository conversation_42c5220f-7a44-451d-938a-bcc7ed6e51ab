﻿<Window
    x:Class="测量地面综合测控台上位机软件.Window_Main_122"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
    xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    
    Width="1280"
    Height="708"
    mc:Ignorable="d"
    FontSize="14"
    Title="BYD68-122地面综合测控台上位机软件"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized">
    <DockPanel LastChildFill="True">
        <!-- 顶部工具栏 -->

        <ToolBar
            Background="White"
            DockPanel.Dock="Top"
            Name="toolBar"
            ToolBarTray.IsLocked="True">
            <Button Click="Button_Click" Content="配置" />

        </ToolBar>
        <ToolBar
            Background="White"
            DockPanel.Dock="Top"
            Name="toolBar2"
            ToolBarTray.IsLocked="True">
            <Label VerticalAlignment="Stretch" BorderThickness="0">
                <TextBlock>测量收发一体机:</TextBlock>
            </Label>
            <Button
                Margin="5,0,5,0"
                Command="{Binding DigitalCardCommand}"
                CommandParameter="测量收发一体机自检"
                Content="自检"
                >               
            </Button>
            <Button
                Margin="5,0,5,0"
                Command="{Binding DigitalCardCommand}"
                CommandParameter="大功率启动"
                Content="大功率启动"
                >                
            </Button>
            <Button
                Margin="5,0,5,0"
                Command="{Binding DigitalCardCommand}"
                CommandParameter="测量收发一体机锂电池转电"
                Content="锂电池转电"
                >                
            </Button>
            
            
            <Label VerticalAlignment="Stretch" BorderThickness="0" Margin="30,0,0,0">
                <TextBlock>姿态测量发射机:</TextBlock>
            </Label>
            <Button
    Margin="5,0,5,0"
    Command="{Binding DigitalCardCommand}"
    CommandParameter="姿态测量发射机自检"
    Content="自检">
            </Button>
            <Button
    Margin="5,0,5,0"
    Command="{Binding DigitalCardCommand}"
    CommandParameter="姿态测量发射机锂电池转电"
    Content="锂电池转电">
            </Button>
            <Button
    Margin="5,0,5,0"
    Command="{Binding DigitalCardCommand}"
    CommandParameter="锂电池停止工作"
    Content="锂电池停止工作">
            </Button>

        </ToolBar>

        




        <!-- 底部状态栏 -->
        <StatusBar Height="30" DockPanel.Dock="Bottom">
            <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,4,0"
                    VerticalAlignment="Center"
                    Text="{Binding PowerCardVM.PowerCard.CardName, StringFormat=\{0\}:}" />
                <Image Margin="0,0,0,0" VerticalAlignment="Center">
                    <Image.Style>
                        <Style TargetType="Image">
                            <Style.Triggers>
                                <DataTrigger Value="True" Binding="{Binding PowerCardVM.PowerCard.Online}">
                                    <Setter Property="Source" Value="/Image/green.png" />
                                </DataTrigger>
                                <DataTrigger Value="False" Binding="{Binding PowerCardVM.PowerCard.Online}">
                                    <Setter Property="Source" Value="/Image/gray.png" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>

                <TextBlock
                    Margin="15,0,4,0"
                    VerticalAlignment="Center"
                    Text="{Binding DigitalCard.CardName, StringFormat=\{0\}:}" />
                <Image Margin="0,0,0,0" VerticalAlignment="Center">
                    <Image.Style>
                        <Style TargetType="Image">
                            <Style.Triggers>
                                <DataTrigger Value="True" Binding="{Binding DigitalCard.Online}">
                                    <Setter Property="Source" Value="/Image/green.png" />
                                </DataTrigger>
                                <DataTrigger Value="False" Binding="{Binding DigitalCard.Online}">
                                    <Setter Property="Source" Value="/Image/gray.png" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>


            </StackPanel>
        </StatusBar>



        <!-- 中间内容区域 -->
        <Grid UseLayoutRounding="True">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition
                    Height="280"
                    MinHeight="105"
                    MaxHeight="280" />
            </Grid.RowDefinitions>
            <ScrollViewer>
            <StackPanel Grid.Row="0" Grid.Column="0">
                <TextBlock
                    Height="Auto"
                    Margin="10,10,10,10"
                    HorizontalAlignment="Center"
                    FontFamily="SimHei"
                    FontSize="30"
                    FontWeight="Bold"
                    Text="BYD68-122" />

                <dxlc:GroupBox
                    Margin="5"
                    Background="Transparent"
                    Header="28V电源"
                    TitleBackground="Transparent">
                        <StackPanel Width="149">
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Voltage28, StringFormat=供电电压：\{0:F1\} V}" />
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Current28, StringFormat=供电电流：\{0:F1\} mA}" />

                        <local:MySwitchButton
                            x:Name="btn28V"
                            Width="129"
                            Height="30"
                            Margin="10,10,10,10"
                            HorizontalAlignment="Stretch"
                            Background="#FAFAFA"
                            Command="{Binding PowerCardVM.PowerCardCommand}"
                            CommandParameter="1"
                            ContextWhenChecked="28V断电"
                            ContextWhenUnChecked="28V供电"
                            hc:BorderElement.CornerRadius="12"
                            IsChecked="{Binding PowerCardVM.Is28VPowerOn}"
                            Style="{StaticResource SwitchButtonStyle}" />

                           
                        </StackPanel>
                </dxlc:GroupBox>

                <dxlc:GroupBox
                    Margin="5"
                    Background="Transparent"
                    Header="5V电源"
                    TitleBackground="Transparent"
                    Visibility="Visible">
                    <StackPanel Width="149">
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Voltage5, StringFormat=供电电压：\{0:F1\} V}" />
                        <TextBlock
                            Margin="5,5,0,5"
                            HorizontalAlignment="Stretch"
                            Text="{Binding PowerCardVM.PowerInfo.Current5, StringFormat=供电电流：\{0:F1\} mA}" />

                        <local:MySwitchButton
                            x:Name="btn5V"
                            Width="129"
                            Height="30"
                            Margin="10,10,10,10"
                            HorizontalAlignment="Stretch"
                            Background="#FAFAFA"
                            Command="{Binding PowerCardVM.PowerCardCommand}"
                            CommandParameter="5"
                            ContextWhenChecked="5V断电"
                            ContextWhenUnChecked="5V供电"
                            hc:BorderElement.CornerRadius="12"
                            IsChecked="{Binding PowerCardVM.Is5VPowerOn}"
                            Style="{StaticResource SwitchButtonStyle}" />

                           
                    </StackPanel>
                </dxlc:GroupBox>

                <!--<Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="2"
                    Content="激活信号1"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="3"
                    Content="激活信号2"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="4"
                    Content="起飞信号"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="6"
                    Content="有源信号1"
                    hc:BorderElement.CornerRadius="12" />
                <Button
                    Width="129"
                    Height="30"
                    Margin="10,10,10,10"
                    Command="{Binding PowerCardVM.PowerCardCommand}"
                    CommandParameter="7"
                    Content="有源信号2"
                    hc:BorderElement.CornerRadius="12" />-->


            </StackPanel>
            </ScrollViewer>
            <dxwui:NavigationFrame
                x:Name="mainFrame"
                Grid.Row="0"
                Grid.Column="1"
                Grid.ColumnSpan="2"
                Background="Azure"
                 
                Source="Page1"
                />

            <GridSplitter
                Grid.Row="1"
                Grid.ColumnSpan="2"
                Height="2"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center" />
            <ListView
                Grid.Row="2"
                Grid.ColumnSpan="2"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                HorizontalContentAlignment="Stretch"
                VerticalContentAlignment="Center"
                Background="White"
                DataContext="{Binding Logs}"
                ItemContainerStyle="{StaticResource LogListViewItemStyle}"
                ItemsSource="{Binding}"
                Name="LogList"
                ScrollViewer.HorizontalScrollBarVisibility="Auto"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                SizeChanged="LogList_SizeChanged">

                <ListView.View>
                    <GridView>
                        <GridViewColumn
                            Width="120"
                            DisplayMemberBinding="{Binding Time}"
                            Header="时间" />
                        <GridViewColumn
                            Width="80"
                            DisplayMemberBinding="{Binding Type}"
                            Header="类型" />
                        <GridViewColumn Header="消息">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Width="Auto"
                                        Text="{Binding Msg}"
                                        TextWrapping="Wrap"
                                        ToolTip="{Binding Msg}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>
        </Grid>
    </DockPanel>
</Window>