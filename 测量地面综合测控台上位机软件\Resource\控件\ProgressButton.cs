﻿using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// 进度条按钮控件 - 支持显示进度和多种状态文本
    /// </summary>
    public class ProgressButton : Button
    {
        static ProgressButton()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(ProgressButton), new FrameworkPropertyMetadata(typeof(ProgressButton)));
        }

        #region 依赖属性

        /// <summary>
        /// 进度值 (0-100)
        /// </summary>
        public static readonly DependencyProperty ProgressProperty =
            DependencyProperty.Register("Progress", typeof(double), typeof(ProgressButton), 
                new PropertyMetadata(0.0, OnProgressChanged));

        /// <summary>
        /// 是否正在进行中
        /// </summary>
        public static readonly DependencyProperty IsInProgressProperty =
            DependencyProperty.Register("IsInProgress", typeof(bool), typeof(ProgressButton), 
                new PropertyMetadata(false, OnIsInProgressChanged));

        /// <summary>
        /// 默认文本（如"开始下载"）
        /// </summary>
        public static readonly DependencyProperty DefaultTextProperty =
            DependencyProperty.Register("DefaultText", typeof(string), typeof(ProgressButton), 
                new PropertyMetadata("开始下载"));

        /// <summary>
        /// 进行中的文本（如"3.57MB"）
        /// </summary>
        public static readonly DependencyProperty ProgressTextProperty =
            DependencyProperty.Register("ProgressText", typeof(string), typeof(ProgressButton), 
                new PropertyMetadata(""));

        /// <summary>
        /// 悬停时的文本（如"停止下载"）
        /// </summary>
        public static readonly DependencyProperty HoverTextProperty =
            DependencyProperty.Register("HoverText", typeof(string), typeof(ProgressButton), 
                new PropertyMetadata("停止下载"));

        /// <summary>
        /// 进度条背景色
        /// </summary>
        public static readonly DependencyProperty ProgressBackgroundProperty =
            DependencyProperty.Register("ProgressBackground", typeof(Brush), typeof(ProgressButton), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(230, 230, 230))));

        /// <summary>
        /// 进度条前景色
        /// </summary>
        public static readonly DependencyProperty ProgressForegroundProperty =
            DependencyProperty.Register("ProgressForeground", typeof(Brush), typeof(ProgressButton), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(0, 120, 215))));

        /// <summary>
        /// 当前显示的文本（内部使用）
        /// </summary>
        public static readonly DependencyProperty CurrentTextProperty =
            DependencyProperty.Register("CurrentText", typeof(string), typeof(ProgressButton), 
                new PropertyMetadata("开始下载"));

        /// <summary>
        /// 是否鼠标悬停（内部使用）
        /// </summary>
        public static readonly DependencyProperty IsMouseOverInternalProperty =
            DependencyProperty.Register("IsMouseOverInternal", typeof(bool), typeof(ProgressButton), 
                new PropertyMetadata(false, OnIsMouseOverInternalChanged));

        #endregion

        #region 属性包装器

        public double Progress
        {
            get { return (double)GetValue(ProgressProperty); }
            set { SetValue(ProgressProperty, value); }
        }

        public bool IsInProgress
        {
            get { return (bool)GetValue(IsInProgressProperty); }
            set { SetValue(IsInProgressProperty, value); }
        }

        public string DefaultText
        {
            get { return (string)GetValue(DefaultTextProperty); }
            set { SetValue(DefaultTextProperty, value); }
        }

        public string ProgressText
        {
            get { return (string)GetValue(ProgressTextProperty); }
            set { SetValue(ProgressTextProperty, value); }
        }

        public string HoverText
        {
            get { return (string)GetValue(HoverTextProperty); }
            set { SetValue(HoverTextProperty, value); }
        }

        public Brush ProgressBackground
        {
            get { return (Brush)GetValue(ProgressBackgroundProperty); }
            set { SetValue(ProgressBackgroundProperty, value); }
        }

        public Brush ProgressForeground
        {
            get { return (Brush)GetValue(ProgressForegroundProperty); }
            set { SetValue(ProgressForegroundProperty, value); }
        }

        public string CurrentText
        {
            get { return (string)GetValue(CurrentTextProperty); }
            set { SetValue(CurrentTextProperty, value); }
        }

        public bool IsMouseOverInternal
        {
            get { return (bool)GetValue(IsMouseOverInternalProperty); }
            set { SetValue(IsMouseOverInternalProperty, value); }
        }

        #endregion

        #region 事件处理

        protected override void OnMouseEnter(MouseEventArgs e)
        {
            base.OnMouseEnter(e);
            IsMouseOverInternal = true;
        }

        protected override void OnMouseLeave(MouseEventArgs e)
        {
            base.OnMouseLeave(e);
            IsMouseOverInternal = false;
        }

        private static void OnProgressChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ProgressButton button)
            {
                button.UpdateCurrentText();
            }
        }

        private static void OnIsInProgressChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ProgressButton button)
            {
                button.UpdateCurrentText();
            }
        }

        private static void OnIsMouseOverInternalChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ProgressButton button)
            {
                button.UpdateCurrentText();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新当前显示的文本
        /// </summary>
        private void UpdateCurrentText()
        {
            if (IsInProgress)
            {
                if (IsMouseOverInternal)
                {
                    CurrentText = HoverText;
                }
                else
                {
                    CurrentText = string.IsNullOrEmpty(ProgressText) ? DefaultText : ProgressText;
                }
            }
            else
            {
                CurrentText = DefaultText;
            }
        }

        #endregion
    }

    /// <summary>
    /// 进度值到宽度的转换器
    /// </summary>
    public class ProgressToWidthConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length == 2 &&
                values[0] is double progress &&
                values[1] is double totalWidth)
            {
                // 确保进度值在0-100范围内
                progress = Math.Max(0, Math.Min(100, progress));

                // 计算进度条宽度，减去边框宽度
                double progressWidth = (totalWidth - 2) * progress / 100.0;
                return Math.Max(0, progressWidth);
            }

            return 0.0;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
