﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class ReceiveFrame_DigitalCard
    {
        /// <summary>
        /// 帧头
        /// </summary>
        public static readonly byte[] FrameHead = [0xEB, 0x90];

        /// <summary>
        /// 帧尾
        /// </summary>
        public static readonly byte[] FrameTail = [0x14, 0x6F];

        /// <summary>
        /// 帧长
        /// </summary>
        public ushort FrameLength { get; }

        /// <summary>
        /// 帧类型
        /// <para>前三路为BYD68-122通道，后4路为BYD68-121通道</para>
        /// <para>0x00:第1路数字量接收到的数据</para>
        /// <para>0x01:第2路数字量接收到的数据</para>
        /// <para>...</para>
        /// <para>0x0F:第16路数字量接收到的数据</para>
        /// <para>__________________________________</para>
        /// <para>0x10:第1路指令/数据发送成功</para>
        /// <para>0x11:第2路指令/数据发送成功</para>
        /// <para>0x12:第3路指令/数据发送成功</para>
        /// <para>...</para>
        /// <para>0x1F:第16路指令/数据发送成功</para>
        /// </summary>
        public byte FrameType { get; }

        /// <summary>
        /// 全帧计数
        /// </summary>
        public byte CounterF { get; }

        /// <summary>
        /// 子帧计数 
        /// </summary>
        public byte CounterS { get; }

        /// <summary>
        /// 计时时间/ms
        /// </summary>
        public ushort TimeStamp { get; }

        /// <summary>
        /// 数据区
        /// </summary>
        public byte[]? Data { get; } = [];



        /// <summary>
        /// 是否有效
        /// </summary>
        public bool Valid { get; }



        /// <summary>
        /// 从数据中解析出有效帧数据
        /// </summary>
        /// <param name="data">数据</param>
        public ReceiveFrame_DigitalCard(byte[] data)
        {
            Valid = false;

            byte[]? frame = null;

            if (data == null || data.Length < 16)
            {
                //throw new ArgumentException("数据长度不足，无法解析帧");
                System.Diagnostics.Trace.Assert(data != null && data.Length >= 16, "数据长度不足，无法解析帧");
                return;
            }

            for (int i = 0; i < data.Length - 3; i++)
            {
                // 找到帧头
                if (data[i] == FrameHead[0] && data[i + 1] == FrameHead[1])
                {
                    ushort fLen = (ushort)((data[i + 2] << 8) | data[i + 3]);

                    if (i + fLen <= data.Length)
                    {
                        // 找到帧尾
                        if (data[i + fLen - 2] == FrameTail[0] && data[i + fLen - 1] == FrameTail[1])
                        {
                            frame = data.BlockCopy(i, fLen);
                            FrameLength = fLen;
                            break;
                        }
                    }
                }
            }

            if (frame == null || frame.Length < 16)
            {
                System.Diagnostics.Trace.TraceWarning($"数字量卡数据未找到有效帧");
                return;
            }

            FrameType = frame[4];
            CounterF = frame[5];
            CounterS = frame[6];
            TimeStamp = (ushort)((frame[7] << 8) | frame[8]);
            Data = frame.BlockCopy(9, FrameLength - 11);


            Valid = true;
        }

        public override string ToString()
        {
            return $"是否有效:{Valid}  帧长:{FrameLength}  通道:{FrameType:X2}  全帧计数:{CounterF}  子帧计数:{CounterS}  计时时间:{TimeStamp} ms  数据:{Data?.ToHexString()}";
        }

    }

}
