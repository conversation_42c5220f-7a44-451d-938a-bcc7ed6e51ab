﻿using DevExpress.Data.Browsing;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Xml.Linq;
using Path = System.IO.Path;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// ControlSignalCard.xaml 的交互逻辑
    /// </summary>
    public partial class ControlSignalCard : UserControl, INotifyPropertyChanged
    {
        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        #region 字段

        private ObservableCollection<ChannelConfig> _channels;
        private string _currentConfigFile = "";
        private int _channelCount = 32;
        private int _bitNum = 16;
        private int _samples = 1024;
        private double _defaultLow = -1.0;
        private double _defaultUp = 7.0;

        private string dataName = AppDomain.CurrentDomain.BaseDirectory + "配置文件\\SignalData\\SigData.dat";
        private string dataPath = AppDomain.CurrentDomain.BaseDirectory + "配置文件\\SignalData";
        #endregion

        bool changedByUser = false;

        public ControlSignalCard()
        {
            InitializeComponent();
            DataContext = this;
            InitializeChannels();
            InitializeDefaultConfig();

            if (!Directory.Exists(dataPath))
                Directory.CreateDirectory(dataPath);
            changedByUser = true;
        }

        #region 依赖属性

        /// <summary>
        /// 信号源板卡索引号
        /// </summary>
        public static readonly DependencyProperty SignalIndexProperty =
            DependencyProperty.Register("SignalIndex", typeof(int), typeof(ControlSignalCard),
                new PropertyMetadata(-1));

        /// <summary>
        /// 信号源板卡名称
        /// </summary>
        public static readonly DependencyProperty CardNameProperty =
            DependencyProperty.Register("CardName", typeof(string), typeof(ControlSignalCard),
                new PropertyMetadata("信号源卡"));

        /// <summary>
        /// 通道个数
        /// </summary>
        public static readonly DependencyProperty ChannelCountProperty =
            DependencyProperty.Register("ChannelCount", typeof(int), typeof(ControlSignalCard),
                new PropertyMetadata(32, OnChannelCountChanged));

        /// <summary>
        /// 数据位数
        /// </summary>
        public static readonly DependencyProperty BitNumProperty =
            DependencyProperty.Register("BitNum", typeof(int), typeof(ControlSignalCard),
                new PropertyMetadata(16));

        /// <summary>
        /// 采样点数
        /// </summary>
        public static readonly DependencyProperty SamplesProperty =
            DependencyProperty.Register("Samples", typeof(int), typeof(ControlSignalCard),
                new PropertyMetadata(1024));

        /// <summary>
        /// 默认配置文件路径
        /// </summary>
        public static readonly DependencyProperty XmlPathProperty =
            DependencyProperty.Register("XmlPath", typeof(string), typeof(ControlSignalCard),
                new PropertyMetadata("配置文件\\SignalParam\\param.xml", OnXmlPathChanged));

        #endregion

        #region 属性包装器

        public int SignalIndex
        {
            get { return (int)GetValue(SignalIndexProperty); }
            set { SetValue(SignalIndexProperty, value); }
        }

        public string CardName
        {
            get { return (string)GetValue(CardNameProperty); }
            set { SetValue(CardNameProperty, value); }
        }

        public int ChannelCount
        {
            get { return (int)GetValue(ChannelCountProperty); }
            set { SetValue(ChannelCountProperty, value); }
        }

        public int BitNum
        {
            get { return (int)GetValue(BitNumProperty); }
            set { SetValue(BitNumProperty, value); }
        }

        public int Samples
        {
            get { return (int)GetValue(SamplesProperty); }
            set { SetValue(SamplesProperty, value); }
        }

        public string XmlPath
        {
            get { return (string)GetValue(XmlPathProperty); }
            set { SetValue(XmlPathProperty, value); }
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 通道配置集合
        /// </summary>
        public ObservableCollection<ChannelConfig> Channels
        {
            get => _channels;
            set => SetProperty(ref _channels, value);
        }

        /// <summary>
        /// 当前配置文件路径
        /// </summary>
        public string CurrentConfigFile
        {
            get => _currentConfigFile;
            set => SetProperty(ref _currentConfigFile, value);
        }

        #endregion

        #region 事件定义

        /// <summary>
        /// 指令发送事件委托
        /// </summary>
        /// <param name="signalCardIndex">信号源卡索引</param>
        /// <param name="cmdData">指令数据</param>
        /// <returns>发送结果</returns>
        public delegate int SendCmdDelegate(int signalCardIndex, byte[] cmdData);

        /// <summary>
        /// 数据下发事件委托
        /// </summary>
        /// <param name="signalCardIndex">信号源卡索引</param>
        /// <param name="data">数据</param>
        /// <param name="dataLength">数据长度</param>
        /// <returns>发送结果</returns>
        public delegate int SendDataDelegate(int signalCardIndex, byte[] data, uint dataLength);



        /// <summary>
        /// 指令发送事件
        /// </summary>
        [Category("自定义事件设置"), Description("调用此函数发送命令")]
        public event SendCmdDelegate SendCmdEvent;

        /// <summary>
        /// 数据下发事件
        /// </summary>
        [Category("自定义事件设置"), Description("调用此函数发送数据")]
        public event SendDataDelegate SendDataEvent;


        public event EventHandler<string> OnInfoOccurred;
        public event EventHandler<string> OnWarnOccurred;
        public event EventHandler<string> OnErrorOccurred;


        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化通道
        /// </summary>
        private void InitializeChannels()
        {
            _channels = new ObservableCollection<ChannelConfig>();
            DgChannels.ItemsSource = _channels;
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        private void InitializeDefaultConfig()
        {
            // 创建配置文件目录
            var configDir = System.IO.Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory + XmlPath);
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            CurrentConfigFile = XmlPath;
            BtnOpenConfig.ToolTip = XmlPath;

            // 如果配置文件不存在，创建默认配置
            if (!File.Exists(XmlPath))
            {
                GenerateDefaultChannels();
                SaveConfigToFile(XmlPath);
            }
            else
            {
                LoadConfigFromFile(XmlPath,true);
            }
        }

        /// <summary>
        /// 生成默认通道配置
        /// </summary>
        private void GenerateDefaultChannels()
        {
            _channels.Clear();

            for (int i = 1; i <= ChannelCount; i++)
            {
                var channel = new ChannelConfig
                {
                    ChannelName = $"通道{i}",
                    SignalType = "直流",
                    Minimum = _defaultLow,
                    Maximum = _defaultUp,
                    Frequency = 0.0,
                    Phase = 0.0,
                    DutyCycle = 0,
                    CoefK = 1.0,
                    CoefB = 0.0
                };
                _channels.Add(channel);
            }
        }

        /// <summary>
        /// 根据卡名设置通道名称
        /// </summary>
        private void SetChannelNamesByCardName()
        {
            string[] chNames1 = {
                "δ3", "δ4", "通道3", "通道4", "δ1", "δ2", "通道7", "通道8",
                "δ6", "δ5", "通道11", "通道12", "通道13", "通道14", "通道15", "通道16",
                "通道17", "通道18", "通道19", "通道20", "通道21", "通道22", "通道23", "通道24",
                "通道25", "通道26", "通道27", "通道28", "通道29", "通道30", "通道31", "通道32"
            };

            string[] chNames2 = {
                "Tf53/Tf54", "Tf51/Tf52", "通道3", "Tf21/Tf22", "Tf23/Tf24", "通道6",
                "Tf31/Tf32", "Tf33/Tf34", "通道9", "Tf11/Tf12", "Tf13/Tf14", "通道12",
                "Tf43/Tf44", "Tf41/Tf42", "通道15", "Wx1/Wy1", "Nx1/Wz1", "Ny1/Nz1",
                "P01/T01", "T2/U5", "T1/T3", "T02/T5", "T4/T7", "P02/T6",
                "PM1/PM2", "通道26", "通道27", "通道28", "通道29", "通道30", "通道31", "通道32"
            };

            string[] selectedNames = CardName == "信号源卡1" ? chNames1 : chNames2;

            for (int i = 0; i < Math.Min(_channels.Count, selectedNames.Length); i++)
            {
                _channels[i].ChannelName = selectedNames[i];
            }
        }

        #endregion

        #region 依赖属性回调

        private static void OnChannelCountChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ControlSignalCard signalCard)
            {
                signalCard._channelCount = (int)e.NewValue;
                signalCard.GenerateDefaultChannels();
            }
        }

        private static void OnXmlPathChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ControlSignalCard signalCard)
            {
                signalCard.InitializeDefaultConfig();
            }
        }


        #endregion

        #region 事件处理

        /// <summary>
        /// 打开配置文件
        /// </summary>
        private void BtnOpenConfig_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "配置文件 (*.xml)|*.xml",
                Title = "打开配置文件",
                InitialDirectory = Path.GetDirectoryName(XmlPath) ?? Environment.CurrentDirectory
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadConfigFromFile(openFileDialog.FileName);
            }
        }

        /// <summary>
        /// 保存配置文件
        /// </summary>
        private void BtnSaveConfig_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(CurrentConfigFile))
            {
                SaveConfigToFile(CurrentConfigFile);
                OnInfoOccurred?.Invoke(this, $"{CardName}:配置已保存到 {CurrentConfigFile}");

            }
            else
            {
                BtnSaveAsConfig_Click(sender, e);
            }
        }

        /// <summary>
        /// 配置文件另存为
        /// </summary>
        private void BtnSaveAsConfig_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "配置文件 (*.xml)|*.xml",
                Title = "保存配置文件",
                InitialDirectory = Path.GetDirectoryName(XmlPath) ?? Environment.CurrentDirectory,
                FileName = Path.GetFileName(CurrentConfigFile)
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                SaveConfigToFile(saveFileDialog.FileName);
                CurrentConfigFile = saveFileDialog.FileName;
                BtnOpenConfig.ToolTip = CurrentConfigFile;
                //AddLog($"{CardName}配置另存为", "成功");
                OnInfoOccurred?.Invoke(this, $"{CardName}:配置已另存为 {CurrentConfigFile}");
            }
        }

        /// <summary>
        /// 生成通道
        /// </summary>
        private void BtnGenerateChannels_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证方波占空比
                if (CboSignalType.Text == "方波" && NumDutyCycle.Value == 0)
                {
                    MessageBox.Show("请修改方波占空比", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 应用全局参数到所有通道
                foreach (var channel in _channels)
                {
                    channel.SignalType = CboSignalType.Text;
                    channel.Minimum = NumLow.Value;
                    channel.Maximum = NumUp.Value;
                    channel.Frequency = NumFrequency.Value;
                    channel.Phase = NumPhase.Value;
                    channel.DutyCycle = (int)NumDutyCycle.Value;
                }

                // 根据卡名设置通道名称
                //SetChannelNamesByCardName();

                OnInfoOccurred?.Invoke(this, $"{CardName}:生成通道成功");
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke(this, $"{CardName}:生成通道失败：{ex.Message}");
                MessageBox.Show($"生成通道失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }




        byte[] TagData = new byte[1000];                //存放标记数据(每个通道6字节) 相位 频率 占空比

        /// <summary>
        /// 求两个数的最小公倍数
        /// </summary>
        /// <param name="m"></param>
        /// <param name="n"></param>
        /// <returns></returns>
        private int GetLcm(int m, int n)
        {
            int temp = Math.Max(m, n);
            n = Math.Min(m, n);  //n里存放最小值
            m = temp;       //m里存放最大值

            int product = m * n;
            while (n != 0)
            {
                m = m > n ? m : n;      //使m里的数大于n
                int k = m % n;
                m = n;
                n = k;
            }
            return (product / m);
        }


        /// <summary>
        /// 数据压缩
        /// </summary>
        /// <param name="Data"></param>
        /// <param name="n"></param>
        /// <param name="FileName"></param>
        /// <param name="ChNum"></param>
        private void DataContraction(double[] Data, int n, string FileName, int ChNum)
        {
            double Sum = 8;
            int nByte = 0;                //数据位数
            ulong nPack = 0;              //压缩打包后的数据
            int k = GetLcm(n, 8);         //计算n和8的最小公倍数

            string outFile = $"{dataPath}\\{FileName}.dat";
            FileStream fs = new(outFile, FileMode.Create);

            switch ((n - 1) / 8)//判断存储的数据位数(1—8bit存储为1字节 9—16存储为2字节)
            {
                case 0:
                    nByte = 1;
                    break;
                case 1:
                    nByte = 2;
                    break;
                case 2:
                    nByte = 3;
                    break;
                case 3:
                    nByte = 4;
                    break;
            }

            for (int i = 0; i < Data.Length; i++)
            {
                uint nDigital = Convert.ToUInt32((Data[i] * _channels[ChNum].CoefK + _channels[ChNum].CoefB) / Sum * (Math.Pow(2, n) - 1));//先将波形数据乘以标定系数  再从峰峰值范围(-1—7)等比扩大至0-65535数字量(数据下发时即为16位数值，范围是0-65535)
                nPack += nDigital << (8 * nByte - n);
                nPack <<= n;

                if ((i + 1) % (k / n) == 0)     //最小公倍数除以输入的数据位即压缩后数据所占位数
                {
                    byte[] bt1 = BitConverter.GetBytes(nPack);
                    fs.Write(bt1, nByte, k / 8);
                    nPack = 0;
                }
            }
            fs.Flush();
            fs.Close();
        }

        /// <summary>
        /// 生成32个通道的数据
        /// </summary>
        /// <param name="fileNames"></param>
        private void GenerateSignalData(out string[] fileNames)
        {
            fileNames = new string[32];
            int k = 0;
            double[] SigTemp = new double[1024];

            for (int i = 0; i < 32; i++)
            {
                string fileName = _channels[i].ChannelName.Replace("/", "_").Replace("\\", "_").Trim();
                fileNames[i] = fileName;
                double amplitude = (_channels[i].Maximum - _channels[i].Minimum) / 2;

                switch (_channels[i].SignalType)
                {
                    case "直流":
                        double setVal = _channels[i].Maximum + 1;
                        for (int j = 0; j < SigTemp.Length; j++)
                            SigTemp[j] = setVal;
                        break;
                    case "正弦波":
                        SigTemp = SignalGenerator.GenerateSineWave(_channels[i].Frequency, amplitude, _channels[i].Phase, amplitude + _channels[i].Minimum + 1, SigTemp.Length, SigTemp.Length);
                        break;
                    case "三角波":
                        SigTemp = SignalGenerator.GenerateTriangleWave(_channels[i].Frequency, amplitude, _channels[i].Phase, amplitude + _channels[i].Minimum + 1, SigTemp.Length, SigTemp.Length);
                        break;
                    case "锯齿波":
                        SigTemp = SignalGenerator.GenerateSawtoothWave(_channels[i].Frequency, amplitude, _channels[i].Phase, amplitude + _channels[i].Minimum + 1, SigTemp.Length, SigTemp.Length);
                        break;
                    case "方波"://占空比定值为50 频率也设为定值1 相位定值为0  实际的占空比由板卡去控制
                        SigTemp = SignalGenerator.GenerateSquareWave(1, amplitude, 0, amplitude + _channels[i].Minimum + 1, SigTemp.Length, SigTemp.Length, 50);
                        break;
                }

                DataContraction(SigTemp, BitNum, fileName, i);

                ushort nPhase = 0;
                ushort nFreq = 0;
                ushort nDutyCycle = 0;

                if (_channels[i].SignalType == "方波")
                {
                    nPhase = (ushort)(_channels[i].Phase * (1000 / _channels[i].Frequency) / 360 + 0xC000); //相位  16位整数前2位为标志位,后14位表示数据范围从0—2的14次方(16384)
                    nFreq = (ushort)(1000 / _channels[i].Frequency + 0xC000);  //频率  0xC000是1100000000000000,加C0用来确定标志位
                    nDutyCycle = (ushort)(_channels[i].DutyCycle * (1000 / _channels[i].Frequency) / 100 + 0xC000);  //占空比
                }
                byte[] a = BitConverter.GetBytes(nPhase); //将各波形的相位 频率 占空比存放在表示标志位的数组中,下位机根据16位数据的前2位来判断是否是方波(如果是方波则为11)
                byte[] b = BitConverter.GetBytes(nFreq);
                byte[] c = BitConverter.GetBytes(nDutyCycle);
                TagData[k++] = a[0];
                TagData[k++] = a[1];
                TagData[k++] = b[0];
                TagData[k++] = b[1];
                TagData[k++] = c[0];
                TagData[k++] = c[1];
            }
        }

        private void GenerateData(out FileStream fs)
        {
            DateTime ta = DateTime.Now;
            //生成单通道数据
            GenerateSignalData(out string[] files);

            string strReadFile;
            int i, j, k = 0;
            int DataCount = 4 * 32 * 1024 + 6 * 32;
            byte[,] BufTemp = new byte[32, 2 * 1024];
            long Length = 0;
            byte[] SigData = new byte[DataCount];
            fs = File.Create(dataName); //创建数据文件

            for (j = 0; j < ChannelCount; j++)
            {
                i = 0;
                strReadFile = $"{dataPath}\\{files[j]}.dat";
                if (File.Exists(strReadFile))
                {
                    using (FileStream fin = File.OpenRead(strReadFile))
                    {
                        Length = fin.Length;
                        byte[] temp = new byte[Length];
                        fin.Read(temp, 0, temp.Length);                                                              //先将文件读入temp一维数组
                        foreach (byte B in temp)                                                                    //foreach遍历数组并赋值给BufTemp
                            BufTemp[j, i++] = B;
                    }
                }
                else
                {
                    MessageBox.Show($"文件{strReadFile}不存在，请重新生成数据", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
            }
            Trace.WriteLine("s2共耗时" + (DateTime.Now - ta).TotalMilliseconds);
            long rc = Length / 2;  //读取的文件长度/2决定数组循环的次数 每个通道一次读取两字节为一个数字量
            for (j = 0; j < rc; j++)
            {
                for (i = 0; i < ChannelCount; i++)
                {
                    if (_channels[i].SignalType != "方波")
                    {
                        SigData[k] = BufTemp[i, 2 * j];         //不是方波的情况下 低八位在前高八位在后，发4字节
                        SigData[k + 1] = BufTemp[i, 2 * j + 1];
                        SigData[k + 2] = BufTemp[i, 2 * j];
                        SigData[k + 3] = BufTemp[i, 2 * j + 1];
                        k += 4;
                    }
                    else
                    {
                        SigData[k] = BufTemp[i, 0];                        //方波占空比默认为50  所以每路方波数据里一半是高电平值一半是低电平值
                        SigData[k + 1] = BufTemp[i, 1];                    //信源下载时发两路数据 一路全部为高电平 一路全部为低电平
                        SigData[k + 2] = BufTemp[i, 1024];
                        SigData[k + 3] = BufTemp[i, 1024 + 1];
                        k += 4;
                    }
                }
            }
            Trace.WriteLine("s3共耗时" + (DateTime.Now - ta).TotalMilliseconds);
            for (j = 0; j < 6 * ChannelCount; j++)    //将标志位放在数据后
                SigData[k++] = TagData[j];
            Trace.WriteLine("s4共耗时" + (DateTime.Now - ta).TotalMilliseconds);
            fs.Write(SigData, 0, SigData.Length); //将数据写入文件
            fs.Flush();
            DateTime tb = DateTime.Now;
            Trace.WriteLine("共耗时" + (tb - ta).TotalMilliseconds);
        }

        private bool WriteAlarm(FileStream fs)
        {
            if (SignalIndex == -1)
            {
                OnErrorOccurred?.Invoke(this, "信源卡未初始化");
                fs?.Close();
                return false;
            }

            try
            {
                byte[] dat = new byte[4096];
                byte[] readDat = new byte[2048];
                fs.Seek(0, SeekOrigin.Begin);

                int getLen = fs.Read(readDat, 0, readDat.Length);

                for (int i = 0; i < readDat.Length / 2; i++)
                    Array.Copy(readDat, i * 2, dat, i * 4, 2);

                SendCmdEvent(SignalIndex, [0xAA, 0xDD]);
                while (getLen > 0)
                {
                    SendDataEvent(SignalIndex, dat, 4096);
                    Thread.Sleep(10);
                    Array.Clear(dat, 0, 4096);
                    Array.Clear(readDat, 0, readDat.Length);
                    getLen = fs.Read(readDat, 0, 2048);
                    for (int i = 0; i < readDat.Length / 2; i++)
                        Array.Copy(readDat, i * 2, dat, i * 4, 2);
                }
                Array.Clear(dat, 0, 4096);
                SendDataEvent(SignalIndex, dat, 4096);
                OnInfoOccurred?.Invoke(this, "信源下载成功");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke(this, "信源下载失败：" + ex.Message);
                return false;
            }
            finally
            {
                fs?.Close();
            }

        }




        /// <summary>
        /// 信源下载
        /// </summary>
        private async void BtnDownloadSignal_Click(object sender, RoutedEventArgs e)
        {
#if !DEBUG
            if (SignalIndex == -1)
            {
                OnErrorOccurred?.Invoke(this, "未检测到" + CardName);
                return;
            }
#endif
            BtnDownloadSignal.IsEnabled = false;
            try
            {

                GenerateData(out FileStream fs);
                WriteAlarm(fs);


                //OnInfoOccurred?.Invoke(this, $"{CardName}:信源下载完成");
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke(this, $"{CardName}:信源下载失败：{ex.Message}");
                MessageBox.Show($"信源下载失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                BtnDownloadSignal.IsEnabled = true;
            }
        }

        /// <summary>
        /// 启动信源
        /// </summary>
        private void BtnStartSignal_Click(object sender, RoutedEventArgs e)
        {
            if (SignalIndex == -1)
            {
                OnErrorOccurred?.Invoke(this, "未检测到" + CardName);
                return;
            }

            try
            {
                byte[] cmdData = { 0x55, 0xAA };
                if (SendCmdEvent != null)
                {
                    int result = SendCmdEvent(SignalIndex, cmdData);
                    //AddLog("启动命令发送", result == 1 ? "成功" : "失败");
                    OnInfoOccurred?.Invoke(this, $"{CardName}启动命令发送{(result == 1 ? "成功" : "失败")}");
                }
                else
                {
                    //AddLog($"{CardName}启动信源", "失败");
                    OnErrorOccurred?.Invoke(this, $"{CardName}启动命令发送失败：未添加指令发送事件");
                    MessageBox.Show("未添加指令发送事件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke(this, $"{CardName}启动信源失败：{ex.Message}");
                MessageBox.Show($"启动信源失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 停止信源
        /// </summary>
        private void BtnStopSignal_Click(object sender, RoutedEventArgs e)
        {
            if (SignalIndex == -1)
            {
                OnErrorOccurred?.Invoke(this, "未检测到" + CardName);
                return;
            }

            try
            {
                if (SendCmdEvent != null)
                {
                    int result = SendCmdEvent(SignalIndex, [0xDD, 0xAA]);
                    //AddLog("停止命令发送", result == 1 ? "成功" : "失败");
                    OnInfoOccurred?.Invoke(this, $"{CardName}停止命令发送{(result == 1 ? "成功" : "失败")}");
                }
                else
                {
                    OnErrorOccurred?.Invoke(this, $"{CardName}停止信源失败：未添加指令发送事件");
                    MessageBox.Show("未添加指令发送事件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke(this, $"{CardName}停止信源失败：{ex.Message}");
                MessageBox.Show($"停止信源失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// DataGrid行加载事件
        /// </summary>
        private void DgChannels_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = (e.Row.GetIndex() + 1).ToString();
        }

        /// <summary>
        /// DataGrid单元格编辑结束事件
        /// </summary>
        private void DgChannels_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                var channel = e.Row.Item as ChannelConfig;
                if (channel != null)
                {
                    //if (e.Column.Header.ToString() == "信号类型")
                    //{
                    //    if(channel.SignalType=="直流")
                    //    {

                    //    }
                    //}

                }
            }
        }

        #endregion


        #region 配置文件操作

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        private void LoadConfigFromFile(string filePath, bool reloadCoef=false)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("配置文件不存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var doc = XDocument.Load(filePath);
                var channels = doc.Descendants("Table1").ToList();

                if (channels.Count != ChannelCount)
                {
                    MessageBox.Show($"配置文件通道数量({channels.Count})与设定通道数量({ChannelCount})不匹配",
                        "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (reloadCoef)
                {
                    _channels.Clear();
                    int index = 1;
                    foreach (var channelElement in channels)
                    {
                        var channel = new ChannelConfig
                        {
                            ChannelName = channelElement.Element("column2")?.Value ?? $"通道{index++}",
                            SignalType = channelElement.Element("column3")?.Value ?? "直流",
                            Minimum = double.Parse(channelElement.Element("column4")?.Value ?? "-1"),
                            Maximum = double.Parse(channelElement.Element("column5")?.Value ?? "7"),
                            Frequency = double.Parse(channelElement.Element("column6")?.Value ?? "0"),
                            Phase = double.Parse(channelElement.Element("column7")?.Value ?? "0"),
                            DutyCycle = int.Parse(channelElement.Element("column8")?.Value ?? "0"),
                            CoefK = double.Parse(channelElement.Element("column9")?.Value ?? "1"),
                            CoefB = double.Parse(channelElement.Element("column10")?.Value ?? "0")
                        };
                        _channels.Add(channel);
                    }
                }
                else
                {
                    int index = 1;
                    foreach (var channelElement in channels)
                    {
                        _channels[index - 1].ChannelName = channelElement.Element("column2")?.Value ?? $"通道{index}";
                        _channels[index - 1].SignalType = channelElement.Element("column3")?.Value ?? "直流";
                        _channels[index - 1].Minimum = double.Parse(channelElement.Element("column4")?.Value ?? "-1");
                        _channels[index - 1].Maximum = double.Parse(channelElement.Element("column5")?.Value ?? "7");
                        _channels[index - 1].Frequency = double.Parse(channelElement.Element("column6")?.Value ?? "0");
                        _channels[index - 1].Phase = double.Parse(channelElement.Element("column7")?.Value ?? "0");
                        _channels[index - 1].DutyCycle = int.Parse(channelElement.Element("column8")?.Value ?? "0");
                        index++;
                    }
                }



                // 根据卡名设置通道名称
                //SetChannelNamesByCardName();

                CurrentConfigFile = filePath;
                BtnOpenConfig.ToolTip = filePath;
                OnInfoOccurred?.Invoke(this, $"{CardName}:配置加载成功");
            }
            catch (Exception ex)
            {
                OnErrorOccurred?.Invoke(this, $"{CardName}:配置加载失败：{ex.Message}");
                MessageBox.Show($"加载配置文件失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        private void SaveConfigToFile(string filePath)
        {
            try
            {
                var doc = new XDocument(
                        new XElement("NewDataSet",
                            _channels.Select(ch =>
                                new XElement("Table1",
                                new XElement("column1", ch.ChannelName),
                                new XElement("column2", ch.ChannelName),
                                new XElement("column3", ch.SignalType),
                                new XElement("column4", ch.Minimum),
                                new XElement("column5", ch.Maximum),
                                new XElement("column6", ch.Frequency),
                                new XElement("column7", ch.Phase),
                                new XElement("column8", ch.DutyCycle),
                                new XElement("column9", ch.CoefK),
                                new XElement("column10", ch.CoefB)
                            ))
                        )

                );

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                doc.Save(filePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置文件失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        #endregion

        private void CboSignalType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (changedByUser)
            {

                if ((e.AddedItems[0] as ComboBoxItem).Content.ToString() == "直流")
                {
                    NumLow.Value = -1;
                    NumLow.IsReadOnly = true;
                }
                else
                {
                    NumLow.IsReadOnly = false;
                }
            }
        }
    }



    /// <summary>
    /// 通道配置数据模型
    /// </summary>
    public class ChannelConfig : INotifyPropertyChanged, IDataErrorInfo
    {
        private string _channelName = "";
        private string _signalType = "直流";
        private double _minimum = -1.0;
        private double _maximum = 7.0;
        private double _frequency = 0.0;
        private double _phase = 0.0;
        private int _dutyCycle = 0;
        private double _coefK = 1.0;
        private double _coefB = 0.0;

        /// <summary>
        /// 通道名称
        /// </summary>
        public string ChannelName
        {
            get => _channelName;
            set => SetProperty(ref _channelName, value);
        }

        /// <summary>
        /// 信号类型
        /// </summary>
        public string SignalType
        {
            get => _signalType;
            set
            {
                SetProperty(ref _signalType, value);
                // 方波时自动设置占空比为50，其他波形设置为0
                if (value == "方波" && DutyCycle == 0)
                {
                    DutyCycle = 50;
                }
                else if (value != "方波")
                {
                    DutyCycle = 0;
                }
            }
        }

        /// <summary>
        /// 下限值
        /// </summary>
        public double Minimum
        {
            get => _minimum;
            set => SetProperty(ref _minimum, value);
        }

        /// <summary>
        /// 上限值
        /// </summary>
        public double Maximum
        {
            get => _maximum;
            set => SetProperty(ref _maximum, value);
        }

        /// <summary>
        /// 频率
        /// </summary>
        public double Frequency
        {
            get => _frequency;
            set => SetProperty(ref _frequency, Math.Max(0, Math.Min(100, value)));
        }

        /// <summary>
        /// 相位
        /// </summary>
        public double Phase
        {
            get => _phase;
            set => SetProperty(ref _phase, Math.Max(0, Math.Min(360, value)));
        }

        /// <summary>
        /// 占空比
        /// </summary>
        public int DutyCycle
        {
            get => _dutyCycle;
            set => SetProperty(ref _dutyCycle, Math.Max(0, Math.Min(100, value)));
        }

        /// <summary>
        /// 标定系数K
        /// </summary>
        public double CoefK
        {
            get => _coefK;
            set => SetProperty(ref _coefK, value);
        }

        /// <summary>
        /// 标定系数B
        /// </summary>
        public double CoefB
        {
            get => _coefB;
            set => SetProperty(ref _coefB, value);
        }

        public string Error => null;
        public string this[string columnName]
        {
            get
            {
                if (columnName == nameof(DutyCycle))
                {
                    if (SignalType == "方波" && (DutyCycle == 0 || DutyCycle == 100))
                    {
                        return "方波信号的占空比不能为0或100";
                    }
                    if (DutyCycle < 0 || DutyCycle > 100)
                    {
                        return "占空比范围：0~100";
                    }
                }
                else if (columnName == nameof(Minimum))
                {
                    if (Minimum < -1 || Minimum > 7)
                    {
                        return "下限值范围：-1~7";
                    }
                }
                else if (columnName == nameof(Maximum))
                {
                    if (Maximum < -1 || Maximum > 7)
                    {
                        return "上限值范围：-1~7";
                    }
                }
                else if (columnName == nameof(Frequency))
                {
                    if (Frequency < 0 || Frequency > 100)
                    {
                        return "频率范围：0~100";
                    }
                }
                else if (columnName == nameof(Phase))
                {
                    if (Phase < 0 || Phase > 360)
                    {
                        return "相位范围：0~360";
                    }
                }
                return null;
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value))
                return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
