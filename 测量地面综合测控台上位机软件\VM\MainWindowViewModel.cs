﻿using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace 测量地面综合测控台上位机软件
{
    public class MainWindowViewModel : ObservableObject
    {
        /// <summary>
        /// 数字量卡
        /// </summary>
        public DigitalCard DigitalCard { get { return Global.Card_Digital; } }

        /// <summary>
        /// LVDS卡
        /// </summary>
        public LVDSCard? LVDSCard { get { return Global.Card_LVDS; } }

        public PCICard? SignalCard { get { return Global.Card_Signal; } }


        public ICommand DigitalCardCommand { get; set; }

        public PowerCardViewModel PowerCardVM { get; set; }




        public SelfTestInfo_CLSFYTJ SelfTestInfo_CLFSYTJ { get; } = new SelfTestInfo_CLSFYTJ() { };//TODO:根据配置修改KB值

        public SelfTestInfo_ZTCLFSJ SelfTestInfo_ZTCLFSJ { get; } = new SelfTestInfo_ZTCLFSJ() { };//TODO:根据配置修改KB值


        public MonitorViewModel MonitorVM { get { return App.ServiceProvider.GetRequiredService<MonitorViewModel>(); } }


        //public EventHandler<LogEventArgs>? LogOccured;

        public event Action? LogsUpdated;
        public ObservableCollection<object> Logs { get; } = [];







        public MainWindowViewModel()
        {
            PowerCardVM = App.ServiceProvider.GetRequiredService<PowerCardViewModel>();

            DigitalCardCommand = new MyCustomCommand(SendDigitalCommand, IsDigitalCardCanExcute);
        }



        public void AddLog(LogHelper.LogLevel level, string message, DateTime? time = null, Exception? ex = null)
        {
            while (Logs.Count >= 300)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    Logs.RemoveAt(0); // 保持日志数量不超过300条
                });
            }

            if (time == null)
                time = DateTime.Now;

            switch (level)
            {
                case LogHelper.LogLevel.DEBUG:
                    App.Log.Debug(message);
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        Logs.Add(new { Time = time?.ToString("HH:mm:ss.fff"), Type = "调试", Msg = message });
                    });
                    break;
                case LogHelper.LogLevel.INFO:
                    App.Log.Info(message);
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        Logs.Add(new { Time = time?.ToString("HH:mm:ss.fff"), Type = "信息", Msg = message });
                    });
                    break;
                case LogHelper.LogLevel.WARN:
                    App.Log.Warn(message);
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        Logs.Add(new { Time = time?.ToString("HH:mm:ss.fff"), Type = "警告", Msg = message });
                    });
                    break;
                case LogHelper.LogLevel.ERROR:
                    App.Log.Error(message);
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                     {
                         Logs.Add(new { Time = time?.ToString("HH:mm:ss.fff"), Type = "错误", Msg = message });
                     });
                    break;
                    //case LogHelper.LogLevel.FATAL:
                    //    App.Log.Fatal(message);
                    //    Logs.Add(new { Time = DateTime.Now.ToString("HH:mm:ss.fff"), Type = "致命", Msg = message });
                    //    break;
            }



            LogsUpdated?.Invoke();


            if (ex != null)
            {
                App.Log.ErrorException(ex);
            }
        }



        private bool IsDigitalCardCanExcute(object? o)
        {
            return DigitalCard.Online & !isSending;
        }

        private bool isSending = false;

        private async void SendDigitalCommand(object? state)
        {
            await Task.Run(() =>
            {
                try
                {
                    AddLog(LogHelper.LogLevel.INFO, $"发送指令【{state}】");

                    isSending = true;
                    if (state == null)
                    {
                        AddLog(LogHelper.LogLevel.ERROR, "SendDigitalCommand方法的参数state为null，无法执行指令");
                        return Task.CompletedTask;
                    }
                    if (!DigitalCard.Online)
                    {
                        AddLog(LogHelper.LogLevel.ERROR, "数字量卡不在线，无法发送指令");
                        return Task.CompletedTask;
                    }

                    string cmdType = (string)state;

                    switch (cmdType)
                    {
                        case "测量收发一体机自检":
                            SelfTest();
                            break;
                        case "大功率启动":
                            StartHeavyDuty();
                            break;
                        case "测量收发一体机锂电池转电":
                            ConvertToLiBattery_CLSFYTJ();
                            break;

                        case "姿态测量发射机自检":
                            SelfTest(false);
                            break;

                        case "姿态测量发射机锂电池转电":
                            ConvertToLiBattery_ZTCLFSJ();
                            break;

                        case "锂电池停止工作":
                            LiBatteryStopWork();
                            break;
                        default:
                            throw new ArgumentException($"未知的数字量指令: {cmdType}");
                    }
                }
                catch (Exception ex)
                {
                    App.Log.Error($"发送数字量指令时发生异常: {ex.Message}");
                    App.Log.ErrorException(ex);
                }
                finally
                {
                    isSending = false;
                }

                return Task.CompletedTask;
            });

        }

        /// <summary>
        /// 自检
        /// </summary>
        /// <param name="clsfytj">True:测量收发一体机 False:姿态测量发送机</param>
        private async void SelfTest(bool clsfytj = true)
        {
            //发送自检指令帧
            byte[] cmd;
            SendFrame_DigitalCard cmdFrame;
            string cmdName;
            int resLen;
            //ReceiveFrameType rptType;
            byte rptType;
            if (clsfytj)
            {
                cmd = [0x5A, 0x54, 0x81, 0x34, 0x00, 0xB5, 0x02, 0x5A, 0xFE];    //测量收发一体机自检
                cmdFrame = new(0x11, Global.FrameCounter_DigitalCard++, cmd);
                cmdName = "测量收发一体机自检";
                resLen = 50;
                rptType = 0x11;
            }
            else
            {
                //下发配置
                DigitalCard.SetSimulateChannelPara(14, 2, Global.DigitalCardSimulatePara[14].BaudRate, Global.DigitalCardSimulatePara[14].Parity, 2);
                //下发指令
                cmd = [0x9A, 0x95, 0x9A, 0x95, 0x9A, 0x95];//姿态测量发射机自检
                cmdFrame = new(0x1D, Global.FrameCounter_DigitalCard++, cmd);
                cmdName = "姿态测量发射机自检";
                resLen = 44;
                rptType = 0x14;
            }

            byte[] sendedCmd = cmdFrame.ToBytes();

            DigitalCard.SendData(sendedCmd, cmdName);


            // 轮询等待回令帧，超时时间500ms
            bool responseMatched = false;
            var sw = System.Diagnostics.Stopwatch.StartNew();
            while (sw.ElapsedMilliseconds < 500)
            {
                byte[] response = DigitalCard.ReadData($"{cmdName}回令", false);
                if (response.Length != 0)
                {
                    ReceiveFrame_DigitalCard resFrame = new ReceiveFrame_DigitalCard(response);

                    if (resFrame.FrameType == cmdFrame.FrameType)
                    {
                        responseMatched = true;
                        break;
                    }
                    //if (response.Length >= sendedCmd.Length)
                    //{
                    //    if (response.Take(sendedCmd.Length).SequenceEqual(sendedCmd))
                    //    {
                    //        responseMatched = true;
                    //        break;
                    //    }
                    //    else
                    //    {
                    //        AddLog(LogHelper.LogLevel.ERROR, "回令内容不一致，等待下一次自检回令...");
                    //    }
                    //}
                    //else
                    //{
                    //    AddLog(LogHelper.LogLevel.ERROR, "回令长度不一致，等待下一次自检回令...");
                    //}
                }
                await Task.Delay(20); // 避免CPU占用过高
            }

            if (!responseMatched)
            {
                AddLog(LogHelper.LogLevel.ERROR, "回令超时或内容不一致");
                return;
            }

            // 若回令与指令一致，则等待自检回报帧 超时时间2s
            bool reportReceived = false;
            sw.Restart();
            while (sw.ElapsedMilliseconds < 2000)
            {
                byte[] report = DigitalCard.ReadData($"{cmdName}回报帧", false);
                if (report.Length >= resLen)
                {
                    ReceiveFrame_DigitalCard receiveFrame = new(report);
                    // 根据实际帧类型判断是否为自检回报帧
                    if (receiveFrame.Valid && receiveFrame.FrameType == rptType)
                    {
                        // 更新自检信息
                        if (clsfytj)
                        {
                            if (receiveFrame.TimeStamp > 1200)
                            {
                                AddLog(LogHelper.LogLevel.WARN, $"测量收发一体机自检回报数据超时:{receiveFrame.TimeStamp} ms");
                            }
                            if (SelfTestInfo_CLFSYTJ.Update(receiveFrame.Data))
                            {
                                AddLog(LogHelper.LogLevel.INFO, $"接收到测量收发一体机自检结果");
                                reportReceived = true;
                                break;
                            }
                        }
                        else
                        {
                            if (receiveFrame.TimeStamp > 400)
                            {
                                AddLog(LogHelper.LogLevel.WARN, $"姿态测量发射机自检回报数据超时:{receiveFrame.TimeStamp} ms");
                            }
                            if (SelfTestInfo_ZTCLFSJ.Update(receiveFrame.Data))
                            {
                                AddLog(LogHelper.LogLevel.INFO, $"接收到姿态测量发射机自检结果");
                                reportReceived = true;
                                break;
                            }
                        }
                    }
                    else
                    {
                        if (receiveFrame.Valid)
                            AddLog(LogHelper.LogLevel.WARN, $"接受到非自检回报帧：{receiveFrame.FrameType:X2}");
                    }
                }
                await Task.Delay(20);
            }

            if (!reportReceived)
            {
                AddLog(LogHelper.LogLevel.ERROR, "自检回报帧超时或无效");
            }
        }

        /// <summary>
        /// 启动大功率
        /// </summary>
        private async void StartHeavyDuty()
        {
            ////下发配置
            //DigitalCard.SetSimulateChannelPara(13, 5, Global.DigitalCardSimulatePara[13].BaudRate, Global.DigitalCardSimulatePara[13].Parity, 2);
            ////大功率启动指令帧
            //byte[] cmd = [0x5A, 0x54, 0x82, 0x18, 0x00, 0x00, 0x02, 0x5A, 0xFE];   //TODO:校验不知道是多少(倒数第四字节)
            //SendFrame_DigitalCard cmdFrame = new(0x11, Global.FrameCounter_DigitalCard++, 0x00, 0x03, 0x05, cmd); //TODO: 通道号是否为0x01?
            //byte[] sendedCmd = cmdFrame.ToBytes();

            //DigitalCard.SendData(sendedCmd, "大功率启动");

            //// 轮询等待回令帧，超时时间500ms
            //bool responseMatched = false;
            //var sw = System.Diagnostics.Stopwatch.StartNew();
            //while (sw.ElapsedMilliseconds < 500)
            //{
            //    byte[] response = DigitalCard.ReadData("大功率启动回令", false);
            //    if (response.Length != 0)
            //    {
            //        if (response.Length >= sendedCmd.Length)
            //        {
            //            if (response.Take(sendedCmd.Length).SequenceEqual(sendedCmd))
            //            {
            //                responseMatched = true;
            //                break;
            //            }
            //            else
            //            {
            //                AddLog(LogHelper.LogLevel.ERROR, "回令内容不一致，等待下一次回令...");
            //            }
            //        }
            //        else
            //        {
            //            AddLog(LogHelper.LogLevel.ERROR, "回令长度不一致，等待下一次回令...");
            //        }
            //    }
            //    await Task.Delay(20); // 避免CPU占用过高
            //}

            //if (!responseMatched)
            //{
            //    AddLog(LogHelper.LogLevel.ERROR, "回令超时或内容不一致");
            //    return;
            //}
        }


        /// <summary>
        /// 锂电池转电
        /// </summary>
        private async void ConvertToLiBattery_CLSFYTJ()
        {
            //锂电池转电指令帧
            //byte[] cmd = [0x5A, 0x54, 0x83, 0x18, 0x00, 0x00, 0x02, 0x5A, 0xFE];   //TODO:校验不知道是多少(倒数第四字节)
            //SendFrame_DigitalCard cmdFrame = new(0x11, Global.FrameCounter_DigitalCard++, 0x00, 0x03, 0x05, cmd);//TODO: 通道号是否为0x01?
            //byte[] sendedCmd = cmdFrame.ToBytes();

            //DigitalCard.SendData(sendedCmd, "测量收发一体机锂电池转电");

            //// 轮询等待回令帧，超时时间500ms
            //bool responseMatched = false;
            //var sw = System.Diagnostics.Stopwatch.StartNew();
            //while (sw.ElapsedMilliseconds < 500)
            //{
            //    byte[] response = DigitalCard.ReadData("测量收发一体机锂电池转电回令");
            //    if (response.Length != 0)
            //    {
            //        if (response.Length >= sendedCmd.Length)
            //        {
            //            if (response.Take(sendedCmd.Length).SequenceEqual(sendedCmd))
            //            {
            //                responseMatched = true;
            //                break;
            //            }
            //            else
            //            {
            //                AddLog(LogHelper.LogLevel.ERROR, "回令内容不一致，等待下一次回令...");
            //            }
            //        }
            //        else
            //        {
            //            AddLog(LogHelper.LogLevel.ERROR, "回令长度不一致，等待下一次回令...");
            //        }
            //    }
            //    await Task.Delay(20); // 避免CPU占用过高
            //}

            //if (!responseMatched)
            //{
            //    AddLog(LogHelper.LogLevel.ERROR, "回令超时或内容不一致");
            //    return;
            //}
        }

        private async void ConvertToLiBattery_ZTCLFSJ()
        {
            //锂电池转电指令帧
            //byte[] cmd = [0x3A, 0x39];
            //SendFrame_DigitalCard cmdFrame = new(0x12, Global.FrameCounter_DigitalCard++, 0x00, 0x05, 0x02, cmd);//TODO: 通道号是否为0x02?
            //byte[] sendedCmd = cmdFrame.ToBytes();

            //DigitalCard.SendData(sendedCmd, "姿态测量发射机锂电池转电");

            //// 轮询等待回令帧，超时时间500ms
            //bool responseMatched = false;
            //var sw = System.Diagnostics.Stopwatch.StartNew();
            //while (sw.ElapsedMilliseconds < 500)
            //{
            //    byte[] response = DigitalCard.ReadData("姿态测量发射机锂电池转电回令");
            //    if (response.Length != 0)
            //    {
            //        if (response.Length >= sendedCmd.Length)
            //        {
            //            if (response.Take(sendedCmd.Length).SequenceEqual(sendedCmd))
            //            {
            //                responseMatched = true;
            //                break;
            //            }
            //            else
            //            {
            //                AddLog(LogHelper.LogLevel.ERROR, "回令内容不一致，等待下一次回令...");
            //            }
            //        }
            //        else
            //        {
            //            AddLog(LogHelper.LogLevel.ERROR, "回令长度不一致，等待下一次回令...");
            //        }
            //    }
            //    await Task.Delay(20); // 避免CPU占用过高
            //}

            //if (!responseMatched)
            //{
            //    AddLog(LogHelper.LogLevel.ERROR, "回令超时或内容不一致");
            //    return;
            //}
        }


        private async void LiBatteryStopWork()
        {
            //锂电池停止工作指令帧
            //byte[] cmd = [0x35, 0x36];
            //SendFrame_DigitalCard cmdFrame = new(0x12, Global.FrameCounter_DigitalCard++, 0x00, 0x0A, 0x05, cmd);//TODO: 通道号是否为0x02?
            //byte[] sendedCmd = cmdFrame.ToBytes();

            //DigitalCard.SendData(sendedCmd, "锂电池停止工作");

            //// 轮询等待回令帧，超时时间500ms
            //bool responseMatched = false;
            //var sw = System.Diagnostics.Stopwatch.StartNew();
            //while (sw.ElapsedMilliseconds < 500)
            //{
            //    byte[] response = DigitalCard.ReadData("锂电池停止工作回令");
            //    if (response.Length != 0)
            //    {
            //        if (response.Length >= sendedCmd.Length)
            //        {
            //            if (response.Take(sendedCmd.Length).SequenceEqual(sendedCmd))
            //            {
            //                responseMatched = true;
            //                break;
            //            }
            //            else
            //            {
            //                AddLog(LogHelper.LogLevel.ERROR, "回令内容不一致，等待下一次回令...");
            //            }
            //        }
            //        else
            //        {
            //            AddLog(LogHelper.LogLevel.ERROR, "回令长度不一致，等待下一次回令...");
            //        }
            //    }
            //    await Task.Delay(20); // 避免CPU占用过高
            //}

            //if (!responseMatched)
            //{
            //    AddLog(LogHelper.LogLevel.ERROR, "回令超时或内容不一致");
            //    return;
            //}
        }

        private async void SetData()
        {
            throw new NotImplementedException();
        }


        public void Simulator(bool state)
        {
            if (state)
                Global.StartSimulate();
            else
                Global.StopSimulate();
        }


        public void Analyse(bool state)
        {
            if (state)
            {
                Global.StartMonitorLVDS();
                MonitorVM.StartUpdateUI();
            }
            else
            {
                Global.StopMonitorLVDS();
                MonitorVM.StopUpdateUI();
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="state"></param>
        public void StartTest(bool[] state)
        {
            for (int i = 0; i < state.Length; i++)
            {
                if (state[i])
                    DigitalCard.StartSimulate((byte)i, Global.DigitalCardSimulatePara[i].Cycle, Global.DigitalCardSimulatePara[i].BaudRate, Global.DigitalCardSimulatePara[i].Parity, Global.DigitalCardSimulatePara[i].DataLen);
            }
        }

        public void StopTest(byte ch)
        {
            DigitalCard.StopSimulate(ch);
        }

        private void StopDownload()
        {
            DigitalCard.StopDownload();
        }

        public void StopAllTest()
        {
            DigitalCard.StopAllSimulate();
            DigitalCard.StopDownload();
        }

    }
}
