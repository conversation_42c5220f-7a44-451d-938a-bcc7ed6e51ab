﻿using DevExpress.Xpf.Grid;
using DevExpress.Xpf.WindowsUI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using 测量地面综合测控台上位机软件.VM;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// Page1.xaml 的交互逻辑
    /// </summary>
    public partial class Page1 : NavigationPage
    {
        private Page1ViewModel ViewModel => (Page1ViewModel)DataContext;

        public Page1()
        {
            InitializeComponent();

            // 页面加载完成后进行性能优化设置
            Loaded += Page1_Loaded;
            Unloaded += Page1_Unloaded;
        }

        private void Page1_Loaded(object sender, RoutedEventArgs e)
        {
            // 性能优化设置
            OptimizeGridPerformance();
        }

        /// <summary>
        /// 优化Grid性能设置
        /// </summary>
        private void OptimizeGridPerformance()
        {
            // 启用虚拟化以提高大数据集性能
            TableView.UseLightweightTemplates = UseLightweightTemplates.All;
            TableView.ShowTotalSummary = false;
            TableView.ShowGroupedColumns = false;

            // 禁用不必要的功能以提高性能
            DataGrid.EnableSmartColumnsGeneration = false;
            DataGrid.AutoGenerateColumns = AutoGenerateColumnsMode.None;
        }

        /// <summary>
        /// 开始实时更新按钮点击事件
        /// </summary>
        private void StartUpdate_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.StartRealTimeUpdate();
        }

        /// <summary>
        /// 停止更新按钮点击事件
        /// </summary>
        private void StopUpdate_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.StopRealTimeUpdate();
        }

        /// <summary>
        /// 重置数据帧按钮点击事件
        /// </summary>
        private void ResetData_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.ResetDataFrame();
        }

        /// <summary>
        /// 导入数据帧按钮点击事件
        /// </summary>
        private void ImportFrame_Click(object sender, RoutedEventArgs e)
        {
            // 创建文件对话框
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择数据帧文件",
                Filter = "二进制文件 (*.bin)|*.bin|数据文件 (*.dat)|*.dat|所有文件 (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    // 读取文件数据
                    byte[] frameData = System.IO.File.ReadAllBytes(openFileDialog.FileName);

                    if (frameData.Length == 7500)
                    {
                        ViewModel.UpdateDataFrame(frameData);
                        MessageBox.Show("数据帧导入成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show($"文件大小不正确！期望7500字节，实际{frameData.Length}字节。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导入文件失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 页面卸载事件处理
        /// </summary>
        private void Page1_Unloaded(object sender, RoutedEventArgs e)
        {
            // 清理资源
            ViewModel?.Dispose();
        }
    }
}
