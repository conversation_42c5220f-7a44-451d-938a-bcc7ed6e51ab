﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Windows.Graphics;


namespace 测量地面综合测控台上位机软件
{

    public partial class Channel(Device parent) : ObservableObject
    {
        public readonly Device Parent = parent;

        /// <summary>
        /// 通道名称
        /// </summary>
        [ObservableProperty]
        [Display(Name = "通道名称", Description = "通道名称", Order = 0)]
        private string channelName = string.Empty;

        /// <summary>
        /// 通道描述
        /// </summary>
        [ObservableProperty]
        [Display(Name = "通道描述", Description = "通道描述", Order = 1)]
        private string channelDescription = string.Empty;



        /// <summary>
        /// 一个采样点的字节数
        /// </summary>
        [ObservableProperty]
        [Display(Name = "样本点长度", Description = "单个样本点占用的字节长度", Order = 2)]
        private int pointBytes = 2;

        public enum EnumDataType
        {
            有符号整数,
            无符号整数,
            单精度浮点数,
            双精度浮点数,
            比特位,
            版本号,
            自定义
        }

        /// <summary>
        /// 数据类型
        /// <para>有符号整数、无符号整数、单精度浮点数、双精度浮点数</para>
        /// </summary>
        [ObservableProperty]
        [Display(Name = "样本点数据类型", Description = "样本点的数据类型", Order = 3)]
        private EnumDataType dataType = EnumDataType.无符号整数;


        /// <summary>
        /// 是否大端字节序
        /// </summary>
        [ObservableProperty]
        [Display(Name = "样本点是否大端字节序", Description = "样本点是否大端字节序，仅在样本点长度大于1时用到", Order = 4)]
        private bool isBigEndian = true;

        /// <summary>
        /// 位置
        /// </summary>
        private string position_Str = string.Empty;

        /// <summary>
        /// 位置
        /// </summary>
        [Display(Name = "样本点位置", Description = "样本点在帧中的位置(从0开始)", Order = 5)]
        public string Position_Str
        {
            get { return position_Str; }
            set
            {
                if (position_Str != value)
                {
                    position_Str = value;
                    Position.Clear();
                    string[] temp = Position_Str.Split([',', ' '], StringSplitOptions.RemoveEmptyEntries);
                    for (int i = 0; i < temp.Length; i++)
                        Position.Add(Convert.ToInt32(temp[i]));

                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 位置
        /// </summary>
        public List<int> Position { get; } = [];

        /// <summary>
        /// 比特位索引
        /// </summary>
        [ObservableProperty]
        [Display(Name = "比特位索引", Description = "样本点在字节中bit的索引，仅在样本点的数据类型为比特位时用到", Order = 6)]
        private int bitIndex = -1;

        /// <summary>
        /// 版本号格式
        /// </summary>
        [ObservableProperty]
        [Display(Name = "版本号格式", Description = "版本号格式，仅在样本点的数据类型为版本号时用到", Order = 7)]
        private string versionFormat = string.Empty;

        /// <summary>
        /// 是否在界面上监控
        /// </summary>
        [ObservableProperty]
        [Display(Name = "是否在界面上监测", Description = "是否在界面上监测", Order = 8)]
        private bool monitor = true;

        /// <summary>
        /// 是否在界面上显示曲线
        /// </summary>
        [ObservableProperty]
        [Display(Name = "是否在界面上显示曲线", Description = "是否在界面上显示曲线", Order = 9)]
        private bool showCurve = false;

        /// <summary>
        /// 增益系数
        /// </summary>
        [ObservableProperty]
        [Display(Name = "增益系数", Description = "K值", Order = 10)]
        private decimal gainCoef = 1;

        /// <summary>
        /// 偏移系数
        /// </summary>
        [ObservableProperty]
        [Display(Name = "偏移系数", Description = "B值", Order = 11)]
        private decimal offsetCoef = 0;

        /// <summary>
        /// 最小值(电压量)
        /// </summary>
        [ObservableProperty]
        [Display(Name = "最小值", Description = "通道的最小值", Order = 12)]
        private decimal minimum = decimal.MinValue;

        /// <summary>
        /// 最大值(电压量)
        /// </summary>
        [ObservableProperty]
        [Display(Name = "最大值", Description = "通道的最大值", Order = 13)]
        private decimal maximum = decimal.MaxValue;

        /// <summary>
        /// 通道电压量单位
        /// </summary>
        [ObservableProperty]
        [Display(Name = "电压量单位", Description = "通道电压量单位", Order = 14)]
        private string unit1 = string.Empty;

        /// <summary>
        /// 通道物理量单位
        /// </summary>
        [ObservableProperty]
        [Display(Name = "物理量单位", Description = "通道物理量单位", Order = 15)]
        private string unit2 = string.Empty;




        /// <summary>
        /// 样本点时间间隔
        /// </summary>
        public decimal SampleTimeInterval = 1;

        public void UpdateSampleTimeInterval(decimal frameCycle)
        {
            SampleTimeInterval = frameCycle / (Position.Count / PointBytes);
        }

        public int SampleCount { get { return Position.Count/PointBytes; } }


        private string versionStr = string.Empty;

        public string VersionStr
        {
            get { return versionStr; }
        }







        private string lastHexValue = "00";

        public string LastHexValue
        {
            get { return lastHexValue; }
            private set
            {
                if (value != lastHexValue)
                {
                    lastHexValue = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal lastDecValue;
        public decimal LastDecValue
        {
            get { return lastDecValue; }
            private set
            {
                if (value != lastDecValue)
                {
                    lastDecValue = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal lastVolValue;
        public decimal LastVolValue
        {
            get { return lastVolValue; }
            private set
            {
                if (value != lastVolValue)
                {
                    lastVolValue = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal lastPhyValue;
        public decimal LastPhyValue
        {
            get { return lastPhyValue; }
            private set
            {
                if (value != lastPhyValue)
                {
                    lastPhyValue = value;
                    OnPropertyChanged();
                }
            }
        }
        //#endif


        /// <summary>
        /// 电压量转物理量的自定义方法
        /// </summary>
        public Func<decimal, decimal>? Function_ConvertToPhysical;

        /// <summary>
        /// Hex数字量转Dec数字量的自定义方法(仅在DataType为"自定义"时使用)
        /// </summary>
        public Func<byte[], decimal>? Function_ConvertToDecimal;


        public class LogEventArg(string level, string message, Exception? exception = null) : EventArgs
        {
            public string Level { get; set; } = level;
            public string Message { get; set; } = message;
            public Exception? Exception { get; set; } = exception;
        }

        public EventHandler<LogEventArg>? LogOccurred;


        #region Private Function


        /// <summary>
        /// 按指定字节序将data转换为指定数据类型的值，支持任意长度整数
        /// </summary>
        private static decimal ConvertBytes(byte[] data, EnumDataType type, bool isBigEndian, int bitIndex = 0)
        {
            if (data == null || data.Length == 0)
                throw new ArgumentNullException(nameof(data));

            byte[] buffer = new byte[data.Length];
            Buffer.BlockCopy(data, 0, buffer, 0, data.Length);

            switch (type)
            {
                case EnumDataType.有符号整数:
                    return ConvertToSignedInteger(buffer, isBigEndian);
                case EnumDataType.无符号整数:
                    return ConvertToUnsignedInteger(buffer, isBigEndian);
                case EnumDataType.单精度浮点数:
                    if (buffer.Length != 4)
                        throw new ArgumentException("单精度浮点数必须为4字节");
                    if (BitConverter.IsLittleEndian == isBigEndian)
                        Array.Reverse(buffer);
                    return Convert.ToDecimal(BitConverter.ToSingle(buffer, 0));
                case EnumDataType.双精度浮点数:
                    if (buffer.Length != 8)
                        throw new ArgumentException("双精度浮点数必须为8字节");
                    if (BitConverter.IsLittleEndian == isBigEndian)
                        Array.Reverse(buffer);
                    return Convert.ToDecimal(BitConverter.ToDouble(buffer, 0));
                case EnumDataType.比特位:
                    return ((data[0] & (1 << bitIndex)) >> bitIndex);
                default:
                    throw new ArgumentException("未知的数据类型");
            }
        }


        /// <summary>
        /// 字节数组转换为无符号整数
        /// </summary>
        /// <param name="buffer">不超过8字节的字节数组</param>
        /// <param name="isBigEndian">是否大端字节序</param>
        /// <returns></returns>
        private static ulong ConvertToUnsignedInteger(byte[] buffer, bool isBigEndian)
        {
            switch (buffer.Length)
            {
                case 1:
                    return buffer[0];
                case 2:
                    return isBigEndian ? BitConverter.ToUInt16([.. buffer.Reverse()], 0) : BitConverter.ToUInt16(buffer, 0);
                case 4:
                    return isBigEndian ? BitConverter.ToUInt32([.. buffer.Reverse()], 0) : BitConverter.ToUInt32(buffer, 0);
                case 8:
                    return isBigEndian ? BitConverter.ToUInt64([.. buffer.Reverse()], 0) : BitConverter.ToUInt64(buffer, 0);
                default:
                    if (buffer.Length > 8)
                    {
                        byte[] tempBuffer = new byte[8];
                        Buffer.BlockCopy(buffer, 0, tempBuffer, 0, 8);
                        return isBigEndian ? BitConverter.ToUInt64([.. tempBuffer.Reverse()], 0) : BitConverter.ToUInt64(tempBuffer, 0);
                    }
                    else
                    {
                        byte[] temp;
                        if (!isBigEndian)
                        {
                            temp = [.. buffer.Reverse()];
                        }
                        else
                        {
                            temp = new byte[buffer.Length];
                            Buffer.BlockCopy(buffer, 0, temp, 0, buffer.Length);
                        }

                        ulong result = 0;

                        for (int i = 0; i < temp.Length; i++)
                            result = (result << 8) | temp[i];

                        return result;
                    }
            }
        }

        /// <summary>
        /// 字节数组转换为有符号整数
        /// </summary>
        /// <param name="buffer">不超过8字节的字节数组</param>
        /// <param name="isBigEndian">是否大端字节序</param>
        /// <returns></returns>
        private static long ConvertToSignedInteger(byte[] buffer, bool isBigEndian)
        {
            switch (buffer.Length)
            {
                case 1:
                    return (sbyte)buffer[0];
                case 2:
                    return isBigEndian ? BitConverter.ToInt16([.. buffer.Reverse()], 0) : BitConverter.ToInt16(buffer, 0);
                case 4:
                    return isBigEndian ? BitConverter.ToInt32([.. buffer.Reverse()], 0) : BitConverter.ToInt32(buffer, 0);
                case 8:
                    return isBigEndian ? BitConverter.ToInt64([.. buffer.Reverse()], 0) : BitConverter.ToInt64(buffer, 0);
                default:

                    if (buffer.Length > 8)
                    {
                        byte[] tempBuffer = new byte[8];
                        Buffer.BlockCopy(buffer, 0, tempBuffer, 0, 8);
                        return isBigEndian ? BitConverter.ToInt64([.. tempBuffer.Reverse()], 0) : BitConverter.ToInt64(tempBuffer, 0);
                    }
                    else
                    {
                        byte[] temp;
                        if (!isBigEndian)
                        {
                            temp = [.. buffer.Reverse()];
                        }
                        else
                        {
                            temp = new byte[buffer.Length];
                            Buffer.BlockCopy(buffer, 0, temp, 0, buffer.Length);
                        }

                        // 最高位为符号位
                        bool negative = (temp[0] & 0x80) != 0;
                        ulong unsigned = ConvertToUnsignedInteger(temp, true);

                        if (!negative)
                        {
                            return (long)unsigned;
                        }
                        else
                        {
                            // 计算补码
                            int totalBits = temp.Length * 8;
                            long signed = (long)(unsigned - ((ulong)1 << totalBits));
                            return signed;
                        }
                    }
            }
        }


        /// <summary>
        /// 字节数组转换为版本号
        /// </summary>
        /// <param name="buffer">字节数组</param>
        /// <param name="formattedStr">版本号格式。例如"[7:4].[3:0]"表示第一字节的高4位为大版本号，低4位为小版本号，"[15:10].[6:4].[2:0]"表示第一字节的高6位为主版本号，第二字节的bit6~bit4为子版本号，bit2~bit0为修正版本号</param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        private static string ConvertToVersion(byte[] buffer, string formattedStr)
        {
            string[] parts = formattedStr.Split('.');
            List<int> values = new List<int>();
            int totalBits = buffer.Length * 8;

            foreach (string part in parts)
            {
                Match match = Regex.Match(part, @"\[(\d+):(\d+)\]");
                if (!match.Success)
                    throw new ArgumentException($"无效的格式: {part}");

                int high = int.Parse(match.Groups[1].Value);
                int low = int.Parse(match.Groups[2].Value);

                if (high < low)
                    throw new ArgumentException($"{part}：高位索引必须 >= 低位索引 ");

                if (high >= totalBits || low < 0)
                    throw new ArgumentException($"{part}：bit索引超出范围");

                int numBits = high - low + 1;
                int value = 0;

                for (int i = 0; i < numBits; i++)
                {
                    int pos = high - i;
                    int overallIndex = totalBits - 1 - pos;
                    int byteIndex = overallIndex / 8;
                    int bitInByte = overallIndex % 8;
                    int bit = (buffer[byteIndex] >> (7 - bitInByte)) & 1;
                    value = (value << 1) | bit;
                }
                values.Add(value);
            }

            string[] strParts = new string[values.Count];
            for (int i = 0; i < values.Count; i++)
            {
                // 除了主版本号，其余的小于10时补零成两位数
                if (i == 0)
                    strParts[i] = values[i].ToString();
                else
                {
                    if (values[i] < 10)
                        strParts[i] = values[i].ToString("D2");
                    else
                        strParts[i] = values[i].ToString();
                }
            }

            return string.Join(".", strParts);
        }

        #endregion



        // public void ClearSamples()
        // {
        //     samplesRawData.Clear();
        //     samplesRawData_Decimal.Clear();
        //     samplesVol.Clear();
        //     samplesPhy.Clear();
        // }

        // public byte[][] GetSamplesRawData()
        // {
        //     return [.. samplesRawData];
        // }

        // public decimal[] GetSamplesRawData_Decimal()
        // {
        //     return [.. samplesRawData_Decimal];
        // }

        // public decimal[] GetSamplesVol()
        // {
        //     return [.. samplesVol];
        // }

        // public decimal[] GetSamplesPhy()
        // {
        //     return [.. samplesPhy];
        // }

        public void GetNewSamplesFromFrame(byte[] oneFrame, out byte[][] hexSamples, out decimal[] digSamples, out decimal[] volSamples, out decimal[] phySamples)
        {
            hexSamples = [];
            digSamples = [];
            volSamples = [];
            phySamples = [];

            try
            {
                List<byte> data = [];

                List<byte[]> hexSamplesList = [];
                List<decimal> digSamplesList = [];
                List<decimal> volSamplesList = [];
                List<decimal> phySamplesList = [];


                //从帧frame中将该通道的原始值提取到data中
                foreach (int i in Position)
                    data.Add(oneFrame[i]);

                for (int i = 0; i + PointBytes - 1 < data.Count; i += PointBytes)
                {
                    //获取Hex值
                    byte[] hex = new byte[PointBytes];
                    for (int j = 0; j < PointBytes; j++)
                        hex[j] = data[i + j];
                    //samplesRawData.Enqueue(hex);

                    // if (samplesRawData.Count > SamplesCount)
                    // {
                    //     //如果超过最大采样点数量，则删除最早的采样点
                    //     samplesRawData.TryDequeue(out _);
                    // }

                    hexSamplesList.Add(hex);

                    if (DataType == EnumDataType.版本号)
                    {
                        if (!string.IsNullOrWhiteSpace(VersionFormat))
                            versionStr = ConvertToVersion(hex, VersionFormat);
                    }
                    //转换为十进制值
                    else
                    {
                        decimal dec;
                        if (DataType == EnumDataType.自定义 && Function_ConvertToDecimal != null)
                            dec = Function_ConvertToDecimal(hex);
                        else
                            dec = Convert.ToDecimal(ConvertBytes(hex, DataType, IsBigEndian));

                        // samplesRawData_Decimal.Enqueue(dec);

                        // if (samplesRawData_Decimal.Count > SamplesCount)
                        // {
                        //     //如果超过最大采样点数量，则删除最早的采样点
                        //     samplesRawData_Decimal.TryDequeue(out _);
                        // }

                        digSamplesList.Add(dec);


                        //转换为电压值
                        decimal vol = dec * GainCoef + OffsetCoef;
                        // samplesVol.Enqueue(vol);

                        // if (samplesVol.Count > SamplesCount)
                        // {
                        //     //如果超过最大采样点数量，则删除最早的采样点
                        //     samplesVol.TryDequeue(out _);
                        // }

                        volSamplesList.Add(vol);

                        decimal phy = vol;

                        if (Function_ConvertToPhysical != null)
                        {
                            //转换为物理量值
                            phy = Function_ConvertToPhysical(vol);
                            //samplesPhy.Enqueue(phy);
                        }
                        else
                        {
                            //如果没有指定转换函数，则直接使用电压值作为物理量值
                            //samplesPhy.Enqueue(phy);
                        }

                        // if (samplesPhy.Count > SamplesCount)
                        // {
                        //     //如果超过最大采样点数量，则删除最早的采样点
                        //     samplesPhy.TryDequeue(out _);
                        // }

                        phySamplesList.Add(phy);

#if EnableLastValue
                        if (i == data.Count - PointBytes)
                        {
                            LastHexValue = hex.ToHexString();
                            LastDecValue = dec;
                            LastVolValue = vol;
                            LastPhyValue = phy;
                        }
#endif


                    }


                }

                hexSamples = [.. hexSamplesList];
                digSamples = [.. digSamplesList];
                volSamples = [.. volSamplesList];
                phySamples = [.. phySamplesList];
            }
            catch (Exception ex)
            {
                LogOccurred?.Invoke(this, new LogEventArg("错误", $"获取{ChannelName}数据失败：{ex.Message}", ex));
            }
        }

        public void UpdateLastValue(byte[] oneFrame)
        {
            try
            {
                List<byte> data = [];
                //从帧frame中将该通道的原始值提取到data中
                foreach (int i in Position)
                    data.Add(oneFrame[i]);





                // for (int i = 0; i + PointBytes - 1 < data.Count; i += PointBytes)
                {
                    //获取Hex值
                    byte[] hex = [.. data.TakeLast(PointBytes)];



                    if (DataType == EnumDataType.版本号)
                    {
                        if (!string.IsNullOrWhiteSpace(VersionFormat))
                            versionStr = ConvertToVersion(hex, VersionFormat);
                    }
                    //转换为十进制值
                    else
                    {
                        decimal dec;
                        if (DataType == EnumDataType.自定义 && Function_ConvertToDecimal != null)
                            dec = Function_ConvertToDecimal(hex);
                        else
                            dec = Convert.ToDecimal(ConvertBytes(hex, DataType, IsBigEndian));



                        //转换为电压值
                        decimal vol = dec * GainCoef + OffsetCoef;

                        decimal phy = vol;

                        if (Function_ConvertToPhysical != null)
                        {
                            //转换为物理量值
                            phy = Function_ConvertToPhysical(vol);

                        }
                        else
                        {
                            //如果没有指定转换函数，则直接使用电压值作为物理量值
                        }

                        LastHexValue = hex.ToHexString();
                        LastDecValue = dec;
                        LastVolValue = vol;
                        LastPhyValue = phy;

                    }
                }
            }
            catch (Exception ex)
            {
                LogOccurred?.Invoke(this, new LogEventArg("错误", $"获取{ChannelName}数据失败：{ex.Message}", ex));
            }
        }


        /// <summary>
        /// 将通道信息保存到XML节点中
        /// </summary>
        /// <param name="node"></param>
        public void SaveToXMLNode(ref System.Xml.XmlElement node)
        {
            try
            {
                var properties = this.GetType()
            .GetProperties()
            .Where(p => p.CanRead && p.CanWrite && Attribute.IsDefined(p, typeof(DisplayAttribute)))
            .Select(p =>
            {
                var display = p.GetCustomAttributes(typeof(DisplayAttribute), true)
                               .Cast<DisplayAttribute>()
                               .FirstOrDefault();
                return new
                {
                    Property = p,
                    Order = display?.Order ?? int.MaxValue,
                    Name = display?.Name ?? p.Name
                };
            })
            .OrderBy(p => p.Order)
            .ThenBy(p => p.Name);


                // 使用反射将属性保存到XML节点中
                foreach (var item in properties)
                {
                    var value = item.Property.GetValue(this);
                    string valueStr = value?.ToString() ?? string.Empty;
                    node.SetAttribute(item.Name, valueStr);
                }
            }
            catch (Exception ex)
            {
                LogOccurred?.Invoke(this, new LogEventArg("错误", $"保存通道{ChannelName}的配置信息失败：{ex.Message}", ex));
                System.Windows.MessageBox.Show($"保存通道{ChannelName}的配置信息失败：{ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从通道XML节点中读取通道信息
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        public bool ReadFromXMLNode(System.Xml.XmlNode node)
        {
            try
            {
                if (node == null || node.Attributes == null)
                    return false;

                // 建立 Display.Name 到 PropertyInfo 的映射
                var propertyMap = this.GetType()
                    .GetProperties()
                    .Where(p => p.CanRead && p.CanWrite && Attribute.IsDefined(p, typeof(DisplayAttribute)))
                    .Select(p => new
                    {
                        Property = p,
                        DisplayName = p.GetCustomAttributes(typeof(DisplayAttribute), true)
                                       .Cast<DisplayAttribute>()
                                       .FirstOrDefault()?.Name ?? p.Name
                    })
                    .ToDictionary(x => x.DisplayName, x => x.Property, StringComparer.OrdinalIgnoreCase);


                foreach (var p in propertyMap)
                {
                    if (node.Attributes[p.Key] == null)
                    {
                        LogOccurred?.Invoke(node, new LogEventArg("错误", $"从节点【{node.OuterXml}】中未找到'{p.Key}'，请检查配置文件。"));
                        return false;
                    }
                }

                foreach (System.Xml.XmlAttribute attr in node.Attributes)
                {
                    // 先用 Display.Name 匹配
                    if (!propertyMap.TryGetValue(attr.Name, out var property))
                    {
                        // 匹配失败再用属性名匹配（兼容老数据）
                        property = this.GetType().GetProperty(attr.Name);
                    }

                    if (property != null && property.CanWrite)
                    {
                        string typeName = property.PropertyType.Name;
                        switch (typeName)
                        {
                            case "String":
                                property.SetValue(this, attr.Value);
                                break;
                            case "Int32":
                                property.SetValue(this, Convert.ToInt32(attr.Value));
                                break;
                            case "Boolean":
                                property.SetValue(this, Convert.ToBoolean(attr.Value));
                                break;
                            case "Decimal":
                                property.SetValue(this, Convert.ToDecimal(attr.Value));
                                break;
                            case "EnumDataType":
                                property.SetValue(this, Enum.Parse(typeof(EnumDataType), attr.Value));
                                break;
                            default:
                                // 其他类型可以根据需要添加处理
                                break;
                        }
                    }
                    else
                    {
                        //文件中没有找到对应的属性，可能是因为属性名发生了变化或者没有定义该属性
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                LogOccurred?.Invoke(this, new LogEventArg("错误", $"读取通道{ChannelName}的配置信息失败：{ex.Message}", ex));
                System.Windows.MessageBox.Show($"读取通道{ChannelName}的配置信息失败：{ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                return false;
            }
        }
    }
}
