﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class ReceiveFrame_PowerCard
    {
        /// <summary>
        /// 帧头
        /// </summary>
        public static readonly byte[] FrameHead = [0xEB, 0x90];

        /// <summary>
        /// 帧尾
        /// </summary>
        public static readonly byte[] FrameTail = [0x14, 0x6F];

        /// <summary>
        /// 帧长
        /// </summary>
        public ushort FrameLength { get; }

        /// <summary>
        /// 全帧计数
        /// </summary>
        public byte CounterF { get; }

        /// <summary>
        /// 子帧计数 
        /// </summary>
        public byte CounterS { get; }

        /// <summary>
        /// 28V供电电压
        /// </summary>
        public ushort Voltage28 { get; }

        /// <summary>
        /// 28V供电电流
        /// </summary>
        public ushort Current28 { get; }

        /// <summary>
        /// 5V供电电压
        /// </summary>
        public ushort Voltage5 { get; }

        /// <summary>
        /// 5V供电电流
        /// </summary>
        public ushort Current5 { get; }



        /// <summary>
        /// 是否有效
        /// </summary>
        public bool Valid { get; }


        public ReceiveFrame_PowerCard(byte[] data)
        {
            Valid = false;

            byte[]? frame = null;
            if (data == null || data.Length < 19)
            {
                return;
            }

            for (int i = 0; i < data.Length - 3; i++)
            {
                // 找到帧头
                if (data[i] == FrameHead[0] && data[i + 1] == FrameHead[1])
                {
                    ushort fLen = (ushort)((data[i + 2] << 8) | data[i + 3]);
                    if (i + fLen <= data.Length)
                    {
                        // 找到帧尾
                        if (data[i + fLen - 2] == FrameTail[0] && data[i + fLen - 1] == FrameTail[1])
                        {
                            frame = data.BlockCopy(i, fLen);
                            FrameLength = fLen;
                            break;
                        }
                    }
                }
            }

            if (frame == null || frame.Length < 19)
            {
                System.Diagnostics.Trace.TraceWarning("电源卡数据未找到有效帧");
                return;
            }

            CounterF = frame[5];
            CounterS = frame[6];

            Voltage28 = (ushort)((frame[9] << 8) | frame[10]);
            Current28 = (ushort)((frame[11] << 8) | frame[12]);
            Voltage5 = (ushort)((frame[13] << 8) | frame[14]);
            Current5 = (ushort)((frame[15] << 8) | frame[16]);

            Valid = true;
        }
    }

}
