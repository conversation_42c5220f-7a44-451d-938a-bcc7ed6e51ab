﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows10.0.26100.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <SupportedOSPlatformVersion>10.0.17763.0</SupportedOSPlatformVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NeutralLanguage>zh</NeutralLanguage>
    <Configurations>Debug;Release;远程调试</Configurations>
  </PropertyGroup>
<PropertyGroup>
  <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
  <PlatformTarget>x64</PlatformTarget>
  <BaseOutputPath>\\192.168.0.8\share\Debug</BaseOutputPath>
</PropertyGroup>


  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
    <Optimize>False</Optimize>
  </PropertyGroup>


  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='远程调试|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
    <DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <CheckForOverflowUnderflow>False</CheckForOverflowUnderflow>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="MonitorViewModel.cs" />
    <Compile Remove="bin\**" />
    <Compile Remove="参考\**" />
    <EmbeddedResource Remove="bin\**" />
    <EmbeddedResource Remove="参考\**" />
    <None Remove="bin\**" />
    <None Remove="参考\**" />
    <Page Remove="bin\**" />
    <Page Remove="参考\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Image\gray.png" />
    <None Remove="Image\green.png" />
    <None Remove="Image\red.png" />
    <None Remove="Image\模拟.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="DevExpress.Reporting.Core" Version="25.1.3" />
    <PackageReference Include="devexpress.wpf.charts" Version="25.1.3" />
    <PackageReference Include="devexpress.wpf.controls" Version="25.1.3" />
    <PackageReference Include="devexpress.wpf.layoutcontrol" Version="25.1.3" />
    <PackageReference Include="HandyControl" Version="3.5.1" />
    <PackageReference Include="ini-parser-netstandard" Version="2.5.3" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="WPF-UI" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Image\gray.png" />
    <Resource Include="Image\green.png" />
    <Resource Include="Image\red.png" />
    <Resource Include="Image\模拟.png" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DLL\hnfs_pc32_x64v1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\LVDS板卡配置数据.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\100HZ5V锯齿1V直流.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\15HZ方波1V直流.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\1901-001_param.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\2001-001_param.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\20HZ5V锯齿1V直流.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\20HZ三角波直流1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\25HZ三角波直流1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\30HZ三角波直流1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\50HZ5V锯齿1V直流.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\50HZ方波1V直流.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\50HZ正弦直流1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\50HZ直流正弦1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\5HZ三角波直流1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\5种混合波形5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\BY75-58采编器_Test - 副本.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\BY75-58采编器_Test.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\BY75-58采编器_Test2.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\BY75-58采编器_Test3.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\dizz1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\KB.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\param.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\param2.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\test.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\三角波直流5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\方波直流5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\正弦5V直流0V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\正弦直流5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流0V正弦5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流10HZ方波5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流2.5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流5HZ方波5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流三角波5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流方波5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流正弦5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流递增0.1V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\直流锯齿波5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\SignalParam\锯齿波直流5V.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\七室水冷泵.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\三室控制系统状态.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\121无线.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\122无线.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\体遥.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\存储器模拟量.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\模拟数据\122无线.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\模拟数据\七室水冷泵.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\模拟数据\三室控制系统状态.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\模拟数据\发射机.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\模拟数据\惯组.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\系统状态.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\发射机.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\存储器状态.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\惯组.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="配置文件\配置文件.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="V\MonitorPage2.xaml.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>

</Project>
