﻿<Window
    x:Class="测量地面综合测控台上位机软件.WindowDataProcess"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Width="800"
    Height="450"
    mc:Ignorable="d"
    Title="数据处理">
    <Window.DataContext>
        <local:DataProcessViewModel />
    </Window.DataContext>
    <Window.Resources>
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter" />
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <Button
                Command="{Binding StartProcessCommand}"
                Content="📂打开"
                IsEnabled="{Binding IsProcessing, Converter={StaticResource InverseBooleanConverter}}"
                Margin="0,0,10,0" />
            <Button
                Command="{Binding StopProcessCommand}"
                Content="停止处理"
                IsEnabled="{Binding IsProcessing}" />
        </StackPanel>

        <!-- 消息显示区域 -->
        <ScrollViewer Grid.Row="1" Margin="10" VerticalScrollBarVisibility="Auto">
            <TextBox
                x:Name="MessageTextBox"
                IsReadOnly="True"
                TextWrapping="Wrap"
                VerticalAlignment="Stretch"
                HorizontalAlignment="Stretch"
                Background="White"
                Foreground="Black"
                FontSize="14"
                Padding="5" 
                
                />
        </ScrollViewer>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Height="Auto">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📂 " VerticalAlignment="Center" Margin="0,0,3,0" />
                    <TextBlock>
                        <Hyperlink
                            x:Name="FilePathHyperlink"
                            Click="FilePathHyperlink_Click"
                            ToolTip="点击打开文件所在文件夹"
                            TextDecorations="Underline">
                            <Run Text="{Binding FileName}" />
                        </Hyperlink>
                    </TextBlock>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar Width="200" Value="{Binding PercentProgress}" Maximum="100" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>