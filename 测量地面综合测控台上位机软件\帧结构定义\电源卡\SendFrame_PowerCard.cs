﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public class SendFrame_PowerCard
    {
        /// <summary>
        /// 帧头
        /// </summary>
        public static readonly byte[] FrameHead = [0xEB, 0x90];
        /// <summary>
        /// 帧尾
        /// </summary>
        public static readonly byte[] FrameTail = [0x14, 0x6F];

        /// <summary>
        /// <para>0x01：28V电源控制</para>
        /// <para>0x02：激活信号1</para>
        /// <para>0x03：激活信号2</para>
        /// <para>0x04：起飞信号</para>
        /// <para>0x05：5V电源控制</para>
        /// <para>0x06：有源信号1</para>
        /// <para>0x07：有源信号2</para>
        /// </summary>
        public byte FrameType { get; set; }

        /// <summary>
        /// 帧长
        /// </summary>
        public ushort FrameLength { get; } = 13;

        /// <summary>
        /// 全帧计数
        /// </summary>
        public byte CounterF { get; set; }


        public bool OutputEnable { get; set; }


        public SendFrame_PowerCard(byte frameType, byte counterF, bool outputEnable)
        {
            FrameType = frameType;
            CounterF = counterF;
            OutputEnable = outputEnable;
        }

        public byte[] ToBytes()
        {
            byte[] bytes = new byte[FrameLength];
            // 填充帧头
            Buffer.BlockCopy(FrameHead, 0, bytes, 0, FrameHead.Length);
            // 填充帧类型
            bytes[2] = FrameType;
            // 填充帧长度
            bytes[3] = (byte)(FrameLength >> 8);
            bytes[4] = (byte)(FrameLength & 0xFF);
            // 填充全帧计数
            bytes[5] = CounterF;
            // 填充数据区
            if (OutputEnable)
            {
                bytes[9] = 0x55;
                bytes[10] = 0xAA;
            }
            else
            {
                bytes[9] = 0x55;
                bytes[10] = 0x55;
            }
            // 填充帧尾
            Buffer.BlockCopy(FrameTail, 0, bytes, FrameLength - 2, FrameTail.Length);
            return bytes;
        }

    }

}
