﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace 测量地面综合测控台上位机软件
{
    /// <summary>
    /// WindowDataProcess.xaml 的交互逻辑
    /// </summary>
    public partial class WindowDataProcess : Window
    {
        private DataProcessViewModel? viewModel;

        public WindowDataProcess()
        {
            InitializeComponent();

            // 获取DataContext中的ViewModel
            if (DataContext is DataProcessViewModel vm)
            {
                viewModel = vm;
                viewModel.MsgReceived += OnMessageReceived;
            }

            Loaded += WindowDataProcess_Loaded;
            Unloaded += WindowDataProcess_Unloaded;
        }

        private void WindowDataProcess_Loaded(object sender, RoutedEventArgs e)
        {
            // 确保ViewModel事件已订阅
            if (DataContext is DataProcessViewModel vm && viewModel != vm)
            {
                if (viewModel != null)
                {
                    viewModel.MsgReceived -= OnMessageReceived;
                    viewModel.PropertyChanged -= ViewModel_PropertyChanged;
                }

                viewModel = vm;
                viewModel.MsgReceived += OnMessageReceived;
                viewModel.PropertyChanged += ViewModel_PropertyChanged;

                // 初始化超链接状态
                UpdateHyperlinkState();
            }
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 当FileName属性变化时更新超链接状态
            if (e.PropertyName == nameof(DataProcessViewModel.FileName))
            {
                UpdateHyperlinkState();
            }
        }

        private void UpdateHyperlinkState()
        {
            if (FilePathHyperlink != null)
            {
                string? filePath = viewModel?.FileName;
                bool isValidPath = !string.IsNullOrEmpty(filePath) && File.Exists(filePath);

                // 更新超链接状态
                FilePathHyperlink.IsEnabled = isValidPath;

                // 更新工具提示
                FilePathHyperlink.ToolTip = isValidPath
                    ? "点击打开文件所在文件夹"
                    : "文件路径无效或文件不存在";
            }
        }

        private void WindowDataProcess_Unloaded(object sender, RoutedEventArgs e)
        {
            // 取消订阅事件
            if (viewModel != null)
            {
                viewModel.MsgReceived -= OnMessageReceived;
                viewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }
        }

        private void OnMessageReceived(object? sender, string message)
        {
            // 在UI线程上更新消息
            Dispatcher.Invoke(() =>
            {
                MessageTextBox.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\r\n");
                MessageTextBox.ScrollToEnd();
            });
        }

        private void FilePathHyperlink_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前选择的文件路径
                string? filePath = viewModel?.FileName;

                if (string.IsNullOrEmpty(filePath))
                {
                    MessageBox.Show("请先选择一个文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"文件不存在：\n{filePath}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 使用Windows资源管理器打开文件所在文件夹并选中文件
                string argument = $"/select,\"{filePath}\"";

                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = argument,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件所在文件夹：\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
