﻿<Window x:Class="测量地面综合测控台上位机软件.Window_test"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:测量地面综合测控台上位机软件"
        mc:Ignorable="d"
        Title="Window_test" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        <TextBox x:Name="txtBox" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="5,5,5,5" TextWrapping="Wrap"  TextAlignment="Left" AcceptsReturn="True" Text="AA BB CC" />

        <Button Grid.Row="1" Content="发送" Click="Button_Click"/>
    </Grid>
</Window>
