﻿// See https://aka.ms/new-console-template for more information



using System.ComponentModel;
using System.Data;

void GenerateQSSLBData()
{
    int frameLen = 20;

    int dataStartPos = 3;
    int dataLen = 9;

    byte[] frameTemplate = [0x5A, 0x54, 0xF6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0D, 0x5A, 0xFE];

    byte dataValue = 0x00;
    using (FileStream fs = new FileStream("七室水冷泵.dat", FileMode.Create, FileAccess.Write))
    {
        using (BufferedStream writer = new BufferedStream(fs, 4096000))
        {
            for (int i = 0; i <= 0xFFFFFF; i++)
            {
                //填充递增数
                for (int j = 0; j < dataLen; j++)
                {
                    frameTemplate[dataStartPos + j] = dataValue++;
                }
                //填充帧计数
                frameTemplate[12]= (byte)((i >> 16) & 0xFF);
                frameTemplate[13] = (byte)((i >> 8) & 0xFF);
                frameTemplate[14] = (byte)(i & 0xFF);
                //填充校验和
                byte checksum = 0;
                for (int j = 2; j <=14; j++)
                {
                    checksum += frameTemplate[j];
                }
                frameTemplate[16] = checksum;
                writer.Write(frameTemplate, 0, frameLen);
            }
            writer.Flush();
        }
        fs.Close();
    }
}


void GenerateSS()
{
    int frameLen = 20;

    int dataStartPos = 3;
    int dataLen = 9;
    byte[] frameTemplate = [0x5A, 0x54, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0D, 0x5A, 0xFE];

    byte dataValue = 0x00;
    using (FileStream fs = new FileStream("三室控制系统状态.dat", FileMode.Create, FileAccess.Write))
    {
        using (BufferedStream writer = new BufferedStream(fs, 4096000))
        {
            for (int i = 0; i <= 0xFFFFFF; i++)
            {
                //填充递增数
                for (int j = 0; j < dataLen; j++)
                {
                    frameTemplate[dataStartPos + j] = dataValue++;
                }
                //填充帧计数
                frameTemplate[12] = (byte)((i >> 16) & 0xFF);
                frameTemplate[13] = (byte)((i >> 8) & 0xFF);
                frameTemplate[14] = (byte)(i & 0xFF);
                //填充校验和
                byte checksum = 0;
                for (int j = 2; j <= 14; j++)
                {
                    checksum += frameTemplate[j];
                }
                frameTemplate[16] = checksum;
                writer.Write(frameTemplate, 0, frameLen);
            }
            writer.Flush();
        }
        fs.Close();
    }

}

void GenerateGZ()
{
    int frameLen = 44;
    int dataStartPos = 3;
    int dataLen = 30;
    byte[] frameTemplate = 
        [
        0x5A, 0x54, 0xC3,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00,             //数据
        0x00, 0x00,0x00,        //计数
        0x00, 0x00, 0x00, 0x00,//填充字
        0x00,
        0x22, 0x5A, 0xFE];

    byte dataValue = 0x00;
    using (FileStream fs = new FileStream("惯组.dat", FileMode.Create, FileAccess.Write))
    {
        using (BufferedStream writer = new BufferedStream(fs, 4096000))
        {
            for (int i = 0; i <= 0xFFFFFF; i++)
            {
                //填充递增数
                for (int j = 0; j < dataLen; j++)
                {
                    frameTemplate[dataStartPos + j] = dataValue++;
                }
                //填充帧计数
                frameTemplate[12] = (byte)((i >> 16) & 0xFF);
                frameTemplate[13] = (byte)((i >> 8) & 0xFF);
                frameTemplate[14] = (byte)(i & 0xFF);
                //填充校验和
                byte checksum = 0;
                for (int j = 2; j <= 14; j++)
                {
                    checksum += frameTemplate[j];
                }
                frameTemplate[16] = checksum;
                writer.Write(frameTemplate, 0, frameLen);
            }
            writer.Flush();
        }
        fs.Close();
    }


}

void GenerateFSJ()
{
    int frameLen = 20;

    int dataStartPos = 3;
    int dataLen = 9;
    byte[] frameTemplate = [0x5A, 0x54, 0xD4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0D, 0x5A, 0xFE];

    byte dataValue = 0x00;
    using (FileStream fs = new FileStream("发射机.dat", FileMode.Create, FileAccess.Write))
    {
        using (BufferedStream writer = new BufferedStream(fs, 4096000))
        {
            for (int i = 0; i <= 0xFFFFFF; i++)
            {
                //填充递增数
                for (int j = 0; j < dataLen; j++)
                {
                    frameTemplate[dataStartPos + j] = dataValue++;
                }
                //填充帧计数
                frameTemplate[12] = (byte)((i >> 16) & 0xFF);
                frameTemplate[13] = (byte)((i >> 8) & 0xFF);
                frameTemplate[14] = (byte)(i & 0xFF);
                //填充校验和
                byte checksum = 0;
                for (int j = 2; j <= 14; j++)
                {
                    checksum += frameTemplate[j];
                }
                frameTemplate[16] = checksum;
                writer.Write(frameTemplate, 0, frameLen);
            }
            writer.Flush();
        }
        fs.Close();
    }
}


void GenerateWX()
{
    int frameLen = 150;
    byte dataValue = 0x00;


    using (FileStream fs = new FileStream("122无线.dat", FileMode.Create, FileAccess.Write))
    {
        using (BufferedStream writer = new BufferedStream(fs, 4096000))
        {
            for (int i = 0; i <= 0x00FFFF; i++)
            {
                for(int j=0;j<50;j++)
                {
                    if(j==49)
                    {
                        byte[] frameTemplate = new byte[150];
                        frameTemplate[148] = 0x14;
                        frameTemplate[149] = 0x6F;

                        for(int m=0;m<148;m++)
                        {
                            frameTemplate[m] = dataValue++;
                        }
                        writer.Write(frameTemplate, 0, frameLen);
                    }
                    else
                    {
                        byte[] frameTemplate = new byte[150];
                        frameTemplate[148] = 0xEB;
                        frameTemplate[149] = 0x90;

                        for (int m = 0; m < 148; m++)
                        {
                            if(m==51 &&j>=47)
                            {
                                frameTemplate[m] = (byte)((i >> ((49 - j) * 8)) & 0xFF);
                            }
                            else
                                frameTemplate[m] = dataValue++;
                        }
                        writer.Write(frameTemplate, 0, frameLen);
                    }
                }
            }
            writer.Flush();
        }
        fs.Close();
    }

}

//Console.WriteLine("开始生成七室数据");
//GenerateQSSLBData();
//Console.WriteLine("开始生成三室数据");
//GenerateSS();
//Console.WriteLine("开始生成惯组数据");
//GenerateGZ();
//Console.WriteLine("开始生成发射机数据");
//GenerateFSJ();
Console.WriteLine("开始生成无线数据");
GenerateWX();

Console.WriteLine("生成完成");