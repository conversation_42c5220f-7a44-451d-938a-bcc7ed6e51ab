﻿using DevExpress.Mvvm.UI;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Docking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测量地面综合测控台上位机软件
{
    public interface IDockLayoutManagerSerializationService
    {
        void Serialize(object path);
        void Deserialize(object path);
    }
    public class DockLayoutManagerSerializationService : ServiceBase, IDockLayoutManagerSerializationService
    {
        DockLayoutManager DockLayoutManager { get { return AssociatedObject as DockLayoutManager; } }

        void IDockLayoutManagerSerializationService.Deserialize(object path)
        {
            DXSerializer.Deserialize(AssociatedObject, path, AssociatedObject.GetType().Name, null);
        }

        void IDockLayoutManagerSerializationService.Serialize(object path)
        {
            DXSerializer.Serialize(AssociatedObject, path, AssociatedObject.GetType().Name, null);
        }
    }


    public class MVVMSerializationLayoutAdapter : ILayoutAdapter
    {
        #region ILayoutAdapter Members
        string ILayoutAdapter.Resolve(DockLayoutManager owner, object item)
        {
            BaseLayoutItem panelHost = owner.GetItem("PanelHost");
            if (panelHost == null)
            {
                panelHost = new LayoutGroup() { Name = "PanelHost", DestroyOnClosingChildren = false };
                owner.LayoutRoot.Add(panelHost);
            }
            return "PanelHost";
        }
        #endregion
    }
}
