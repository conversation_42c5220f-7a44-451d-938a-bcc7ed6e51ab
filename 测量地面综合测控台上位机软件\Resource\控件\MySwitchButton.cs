﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Navigation;

namespace 测量地面综合测控台上位机软件
{
    public class MySwitchButton : System.Windows.Controls.Button
    {
        public static readonly DependencyProperty IsCheckedProperty =
        DependencyProperty.Register("IsChecked", typeof(bool), typeof(MySwitchButton),new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.AffectsRender));

        public static readonly DependencyProperty ContextWhenCheckedProperty =
        DependencyProperty.Register("ContextWhenChecked", typeof(string), typeof(MySwitchButton), new FrameworkPropertyMetadata("关", FrameworkPropertyMetadataOptions.AffectsRender));

        public static readonly DependencyProperty ContextWhenUnCheckedProperty =
        DependencyProperty.Register("ContextWhenUnChecked", typeof(string), typeof(MySwitchButton), new FrameworkPropertyMetadata("开", FrameworkPropertyMetadataOptions.AffectsRender));

        public bool IsChecked
        {
            get { return (bool)GetValue(IsCheckedProperty); }
            set { SetValue(IsCheckedProperty, value); }
        }


        public string ContextWhenChecked
        {
            get { return (string)GetValue(ContextWhenCheckedProperty); }
            set { SetValue (ContextWhenCheckedProperty, value); }
        }

      
        public string ContextWhenUnChecked
        {
            get { return (string)GetValue(ContextWhenUnCheckedProperty); }
            set { SetValue(ContextWhenUnCheckedProperty, value); }
        }


        public MySwitchButton()
        {
            
        }

    }
}
