﻿using DevExpress.Xpf.WindowsUI;
using System.Windows;
using System.Windows.Controls;
using 测量地面综合测控台上位机软件.VM;

namespace 测量地面综合测控台上位机软件.V
{
    /// <summary>
    /// Page2.xaml 的交互逻辑
    /// </summary>
    public partial class Page2 : NavigationPage
    {
        private FrameManagementViewModel ViewModel => (FrameManagementViewModel)DataContext;

        public Page2()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 树形控件选择项变化事件
        /// </summary>
        private void TreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is TreeNodeViewModel selectedNode)
            {
                ViewModel.SelectedNode = selectedNode;
            }
        }

        /// <summary>
        /// 清除搜索按钮点击事件
        /// </summary>
        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.SearchText = string.Empty;
        }
    }
}
